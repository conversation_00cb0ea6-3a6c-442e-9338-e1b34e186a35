# [Paper Dashboard 2](https://demos.creative-tim.com/paper-dashboard/examples/dashboard.html) [![version][version-badge]][CHANGELOG] [![license][license-badge]][LICENSE]

![alt text](https://s3.amazonaws.com/creativetim_bucket/products/86/original/opt_pd2_thumbnail.jpg)

If you are a developer or a website owner that needs to work within a dashboard and want to have a pretty sight while doing so, **[Paper Dashboard 2](https://creative-tim.com/live/paper-dashboard-2)** is for you. Paper Dashboard is a Bootstrap Admin Panel which combines soft colours with beautiful typography and spacious cards and graphics. It is a powerful tool, but it is light and easy to use. It has enough features to allow you to get the job done, but it is not crowded to the point where you can't find the files for a specific plugin.

We like consistency and design that blends into its purpose. Paper Dashboard is a perfect example of our most thoughtful work. It combines over a dozen components and plugins, while looking like everything fits together. For an easy start or inspiration for you project, we have also create a set of example pages, like the user settings or usage graphics.

Paper Dashboard is built using the same design language as **[Paper Kit](https://www.creative-tim.com/product/paper-kit-2)**. You can easily use them together, or pick between them depending on the project you have.

**Bootstrap 4 Support**
Paper Dashboard 2 is built on top of the much awaited Bootstrap 4. This makes starting a new project very simple. It also provides benefits if you are already working on a Bootstrap 4 project; you can just import the Paper Dashboard style over it. Most of the elements have been redesigned; but if you are using an element we have not touched, it will fall back to the Bootstrap default.

**Example Pages**
We wanted to fully display the power of this dashboard, so the kit comes packed with examples showing you how to use the components. Inside the product you will find:

## Links:

+ [Live Preview](https://demos.creative-tim.com/paper-dashboard/examples/dashboard.html)
+ [Paper Dashboard 2 PRO](https://www.creative-tim.com/product/paper-dashboard-2-pro) (from $39)

**Tutorial**
In order for you to easily be able to use the Paper Dashboard 2, we have created a tutorial page in our documentation. It shows the structure for the files inside the archive and how to import them. It then features every components with a description and example how to use it. You can see the details [here](https://demos.creative-tim.com/paper-dashboard/docs/1.0/getting-started/introduction.html).

## Deploy
:rocket: You can deploy your own version of the template to Genezio with one click:

[![Deploy to Genezio](https://raw.githubusercontent.com/Genez-io/graphics/main/svg/deploy-button.svg)](https://app.genez.io/start/deploy?repository=https://github.com/creativetimofficial/paper-dashboard&utm_source=github&utm_medium=referral&utm_campaign=github-creativetim&utm_term=deploy-project&utm_content=button-head)

### What's included

Within the download you'll find the following directories and files:

```
Paper Dashboard 2
.
├── CHANGELOG.md
├── README.md
├── assets
│   ├── css/
│   ├── demo/
│   ├── fonts/
│   ├── img/
│   ├── js
│   │   ├── core/
│   │   ├── paper-dashboard.js
│   │   ├── paper-dashboard.js.map
│   │   ├── paper-dashboard.min.js
│   │   └── plugins
│   │       ├── bootstrap-notify.js
│   │       ├── chartjs.min.js
│   │       └── perfect-scrollbar.jquery.min.js
│   └── scss/
│       ├── paper-dashboard/
│       │   ├── cards/
│       │   ├── mixins/
│       │   └── plugins/
│       └── paper-dashboard.scss
├── docs/
│   └── documentation.html
├── examples/
│   ├── dashboard.html
│   ├── icons.html
│   ├── map.html
│   ├── notifications.html
│   ├── tables.html
│   ├── typography.html
│   ├── upgrade.html
│   └── user.html
├── gulpfile.js
├── nucleo-icons.html
└── package.json
```

## Getting started
- Download the project’s zip
- Make sure you have [node.js](https://nodejs.org/en/){:rel="nofollow"} installed
- Type `npm install` in terminal/console in the source folder where `package.json` is located
- You will find all the branding colors inside `assets/scss/core/variables/_brand.scss`. You can change them with a HEX value or with other predefined variables from `assets/scss/core/variables/_colors.scss`
- Run in terminal `gulp compile-scss` for a single compilation or gulp watch for continous compilation of the changes that you make in `*.scss` files. This command should be run in the same folder where `gulpfile.js` and `package.json` are located
- Run in terminal `gulp open-app` for opening the Presentation Page (default) of the product. You can set in `gulpfile.js` from your downloaded archive any page you want to open in browser, `at line 30: gulp.src('./examples/dashboard.html')`

## Upgrade to PRO Version

Are you looking for more components? Please check our Premium Version of Paper Dashboard right [here](https://www.creative-tim.com/product/paper-dashboard-2-pro).

## Useful Links

More products from Creative Tim: <http://www.creative-tim.com/bootstrap-themes>

Tutorials: <https://www.youtube.com/channel/UCVyTG4sCw-rOvB9oHkzZD1w>

Freebies: <http://www.creative-tim.com/products>

Affiliate Program (earn money): <http://www.creative-tim.com/affiliates/new>

Social Media:

Twitter: <https://twitter.com/CreativeTim>

Facebook: <https://www.facebook.com/CreativeTim>

Dribbble: <https://dribbble.com/creativetim>

Google+: <https://plus.google.com/+CreativetimPage>

Instagram: <https://instagram.com/creativetimofficial>

[CHANGELOG]: ./CHANGELOG.md
[LICENSE]: ./LICENSE
[version-badge]: https://img.shields.io/badge/version-2.0.1-blue.svg
[license-badge]: https://img.shields.io/badge/license-MIT-blue.svg
