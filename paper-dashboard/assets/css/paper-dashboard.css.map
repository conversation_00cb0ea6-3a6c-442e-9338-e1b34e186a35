{"version": 3, "sources": ["paper-dashboard.scss", "paper-dashboard.css", "paper-dashboard/plugins/_plugin-animate-bootstrap-notify.scss", "paper-dashboard/plugins/_plugin-perfect-scrollbar.scss", "paper-dashboard/_buttons.scss", "paper-dashboard/_variables.scss", "paper-dashboard/mixins/_buttons.scss", "paper-dashboard/mixins/_vendor-prefixes.scss", "paper-dashboard/mixins/_transparency.scss", "paper-dashboard/mixins/_inputs.scss", "paper-dashboard/_inputs.scss", "paper-dashboard/_typography.scss", "paper-dashboard/_misc.scss", "paper-dashboard/_checkboxes-radio.scss", "paper-dashboard/_navbar.scss", "paper-dashboard/_page-header.scss", "paper-dashboard/mixins/_page-header.scss", "paper-dashboard/_dropdown.scss", "paper-dashboard/_alerts.scss", "paper-dashboard/_images.scss", "paper-dashboard/_nucleo-outline.scss", "paper-dashboard/_tables.scss", "paper-dashboard/_sidebar-and-main-panel.scss", "paper-dashboard/_footers.scss", "paper-dashboard/_fixed-plugin.scss", "paper-dashboard/_cards.scss", "paper-dashboard/cards/_card-plain.scss", "paper-dashboard/cards/_card-chart.scss", "paper-dashboard/cards/_card-user.scss", "paper-dashboard/cards/_card-map.scss", "paper-dashboard/cards/_card-stats.scss", "paper-dashboard/_responsive.scss"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;ECeE;ACmBF;;;;;CDbC;ACoBD;EACE,8BAA8B;EAC9B,sBAAsB;EACtB,iCAAiC;EACjC,yBAAyB,EAAA;;AAG3B;EACE,2CAA2C;EAC3C,mCAAmC,EAAA;;AAGrC;EACE,8BAA8B;EAC9B,sBAAsB,EAAA;;AAGxB;;EAEE,gCAAgC;EAChC,wBAAwB,EAAA;;AAG1B;;EAEE,gCAAgC;EAChC,wBAAwB,EAAA;;AAG1B;EACE;IAEE,+BAA+B,EAAA;EAGjC;IAEE,mCAAmC,EAAA;EAGrC;IAEE,kCAAkC,EAAA,EAAA;;AAItC;EACE;IAEE,+BAA+B,EAAA;EAGjC;IAEE,mCAAmC,EAAA;EAGrC;IAEE,kCAAkC,EAAA,EAAA;;AAItC;EACE,6BAA6B;EAC7B,qBAAqB,EAAA;;AAKvB;EACE;IACE,UAAU;IAEV,mCAAmC,EAAA;EAGrC;IACE,UAAU;IAEV,eAAe,EAAA,EAAA;;AAInB;EACE;IACE,UAAU;IAEV,mCAAmC,EAAA;EAGrC;IACE,UAAU;IAEV,eAAe,EAAA,EAAA;;AAInB;EACE,kCAAkC;EAClC,0BAA0B,EAAA;;AAI5B;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU,EAAA,EAAA;;AAId;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU,EAAA,EAAA;;AAId;EACE,+BAA+B;EAC/B,uBAAuB,EAAA;;AAGzB;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU;IAEV,kCAAkC,EAAA,EAAA;;AAItC;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU;IAEV,kCAAkC,EAAA,EAAA;;AAItC;EACE,mCAAmC;EACnC,2BAA2B,EAAA;;AAG7B;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU;IAEV,mCAAmC,EAAA,EAAA;;AAIvC;EACE;IACE,UAAU,EAAA;EAGZ;IACE,UAAU;IAEV,mCAAmC,EAAA,EAAA;;AAIvC;EACE,iCAAiC;EACjC,yBAAyB,EAAA;;ACjO3B;;EF0JE;AEvJF;EACE,2BAA2B;EAC3B,qBAAqB;EACrB,wBAAwB;EACxB,kBAAkB;EAClB,sBAAsB,EAAA;;AAGxB;;EFyJE;AEtJF;EACE,aAAa;EACb,UAAU;EACV,2DAA2D;EAC3D,mEAAmE;EACnE,YAAY;EACZ,mDAAA;EACA,WAAW;EACX,mCAAA;EACA,kBAAkB,EAAA;;AAGpB;EACE,aAAa;EACb,UAAU;EACV,2DAA2D;EAC3D,mEAAmE;EACnE,WAAW;EACX,mDAAA;EACA,QAAQ;EACR,mCAAA;EACA,kBAAkB,EAAA;;AAGpB;;EAEE,cAAc;EACd,6BAA6B,EAAA;;AAG/B;;;;;;EAME,YAAY,EAAA;;AAGd;;;;;;EAME,sBAAsB;EACtB,YAAY,EAAA;;AAGd;;EFoJE;AEjJF;EACE,sBAAsB;EACtB,kBAAkB;EAClB,+DAA+D;EAC/D,uEAAuE;EACvE,WAAW;EACX,2CAAA;EACA,WAAW;EACX,mCAAA;EACA,kBAAkB,EAAA;;AAGpB;EACE,sBAAsB;EACtB,kBAAkB;EAClB,8DAA8D;EAC9D,sEAAsE;EACtE,UAAU;EACV,0CAAA;EACA,UAAU;EACV,mCAAA;EACA,kBAAkB,EAAA;;AAGpB;;;EAGE,sBAAsB;EACtB,YAAY,EAAA;;AAGd;;;EAGE,sBAAsB;EACtB,WAAW,EAAA;;AAGb,gBAAA;AACoC;EAtGpC;IAwGI,yBAAyB,EAAA,EAC1B;;AAGH;EA5GA;IA8GI,yBAAyB,EAAA,EAC1B;;AClHH;;EAEI,iBCU4B;EDT5B,gBCsQ+B;EDrQ/B,mBC2PmC;ED1PnC,mBC0Q+B;EDzQ/B,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,kBCgJ+B;ED/I/B,kBC0L+B;EDzL/B,eAAe;EETf,yBD2DgC;EC8D5B,cD7G4B;EEJhC,4BHIiD,EAAA;EEVjD;;;;;;;;;;;;;;;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;;IACI,gBAAiB,EAAA;EAOjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMI,yBD2BwB;IC1BxB,qBD0BwB,EAAA;ECiEhC;;IACI,cDlE4B;ICmE5B,qBDnE4B,EAAA;ICqE5B;;;;MAGI,6BDnF4B;MCoF5B,cDvEwB;MCwExB,qBDxEwB;MCyExB,gBAAgB,EAAA;EAIxB;;IACI,cDhF4B,EAAA;ICkF5B;;;;;MAII,6BDjG4B;MCkG5B,cDrFwB;MCsFxB,qBAAqB;MACrB,gBAAgB,EAAA;EFtJ5B;;;IIGE,UJewB;IIZxB,0BAAQ;IJaF,qBAAqB,EAAA;EAnB7B;;;;;;IGEU,gBHsBuB;IACxB,qBAAqB,EAAA;EAzB9B;;IA6BM,SAAS,EAAA;EA7Bf;;IAkCQ,gBC4MgC;ID3MhC,mBC2MgC;ID1MhC,eC0MgC;IDzMhC,UAAU;IACV,oBCyMiC;IDxMjC,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB,EAAA;IAzC3B;;MA4CY,UAAU,EAAA;IA5CtB;;MAgDY,gBCiM4B;MDhM5B,mBCgM4B;MD/L5B,eC+L4B,EAAA;MDjPxC;;;;;;;;QAwDgB,oBCwLyB,EAAA;IDhPzC;;MA6DY,cCsL0B;MDrL1B,iBCqL0B;MDpL1B,aCoL0B,EAAA;MDnPtC;;;;;;;;QAqEgB,mBC6KwB,EAAA;IDlPxC;;;;;;;;MA6EY,kBAAkB;MAClB,QAAQ;MACR,SAAS;MACT,kCAAkC;MAClC,sBAAsB;MACtB,WAAW,EAAA;IAlFvB;;MAsFU,eAAe,EAAA;EAtFzB;;IA2FQ,kBAAkB;IAClB,QAAQ,EAAA;;AAMhB;EEhGI,yBDoEgC;ECqD5B,cD7G4B,EAAA;ECVhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBDoCwB;ICnCxB,qBDmCwB,EAAA;ECwDhC;IACI,cDzD4B;IC0D5B,qBD1D4B,EAAA;IC4D5B;MAGI,6BDnF4B;MCoF5B,cD/D4C;MCgE5C,qBDhE4C;MCiE5C,gBAAgB,EAAA;EAIxB;IACI,cDvE4B,EAAA;ICyE5B;MAII,6BDjG4B;MCkG5B,cD7E4C;MC8E5C,qBAAqB;MACrB,gBAAgB,EAAA;;AFnD5B;EEjGI,yBDuEgC;ECkD5B,cD7G4B,EAAA;ECVhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBDuCwB;ICtCxB,qBDsCwB,EAAA;ECqDhC;IACI,cDtD4B;ICuD5B,qBDvD4B,EAAA;ICyD5B;MAGI,6BDnF4B;MCoF5B,cD5D4C;MC6D5C,qBD7D4C;MC8D5C,gBAAgB,EAAA;EAIxB;IACI,cDpE4B,EAAA;ICsE5B;MAII,6BDjG4B;MCkG5B,cD1E4C;MC2E5C,qBAAqB;MACrB,gBAAgB,EAAA;;AFlD5B;EElGI,yBD0EgC;EC+C5B,cD7G4B,EAAA;ECVhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBD0CwB;ICzCxB,qBDyCwB,EAAA;ECkDhC;IACI,cDnD4B;ICoD5B,qBDpD4B,EAAA;ICsD5B;MAGI,6BDnF4B;MCoF5B,cDzDyC;MC0DzC,qBD1DyC;MC2DzC,gBAAgB,EAAA;EAIxB;IACI,cDjE4B,EAAA;ICmE5B;MAII,6BDjG4B;MCkG5B,cDvEyC;MCwEzC,qBAAqB;MACrB,gBAAgB,EAAA;;AFjD5B;EEnGI,yBD6EgC;EC4C5B,cD7G4B,EAAA;ECVhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBD6CwB;IC5CxB,qBD4CwB,EAAA;EC+ChC;IACI,cDhD4B;ICiD5B,qBDjD4B,EAAA;ICmD5B;MAGI,6BDnF4B;MCoF5B,cDtD4C;MCuD5C,qBDvD4C;MCwD5C,gBAAgB,EAAA;EAIxB;IACI,cD9D4B,EAAA;ICgE5B;MAII,6BDjG4B;MCkG5B,cDpE4C;MCqE5C,qBAAqB;MACrB,gBAAgB,EAAA;;AFhD5B;EEpGI,yBDgFgC;ECyC5B,cD7G4B,EAAA;ECVhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBDgDwB;IC/CxB,qBD+CwB,EAAA;EC4ChC;IACI,cD7C4B;IC8C5B,qBD9C4B,EAAA;ICgD5B;MAGI,6BDnF4B;MCoF5B,cDnD0C;MCoD1C,qBDpD0C;MCqD1C,gBAAgB,EAAA;EAIxB;IACI,cD3D4B,EAAA;IC6D5B;MAII,6BDjG4B;MCkG5B,cDjE0C;MCkE1C,qBAAqB;MACrB,gBAAgB,EAAA;;AF7C5B;EEmDI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cDjGgC;EG1DlC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AFpF3C;EEkDI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cDxFgC;EGnElC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AFnF3C;EEiDI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cDrFgC;EGtElC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AFlF3C;EEgDI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cDlFgC;EGzElC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AFjF3C;EE+CI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cD/EgC;EG5ElC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AFhF3C;EE8CI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cD5EgC;EG/ElC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;;AF/E3C;EE6CI,uBD1GoC;EC2GpC,oCAAuC;EACvC,cDhJgC;EGXlC,UF4JoB;EEzJpB,0BAAQ,EAAA;EF2JN;;IAME,oCAAuC;IACvC,0CAAkC;IAClC,gCAAmC,EAAA;IACnC;;MACI,qDAA6C,EAAA;EAInD;IACI,oCAAyC,EAAA;EAO3C;;;;;;;IAME,wCAA4C;IAC5C,gCAAmC,EAAA;EF/E3C;IAGQ,cCnD4B;IDoD5B,yBCrG4B,EAAA;;ADwGpC;EEpHI,yBDYgC;EC2B5B,cD6B4B;EDkDhC,cC3DgC,EAAA;ECzDhC;;;;IAWI,oCAA8C;IAC9C,yBAA8B;IAC9B,2BAA2B,EAAA;EAG/B;IACI,gBAAiB,EAAA;EAOjB;;;;;;;IAMI,yBDpBwB;ICqBxB,qBDrBwB,EAAA;EC6B5B;IACI,cDsCwB,EAAA;ICpCxB;MAII,yBAAsC,EAAA;EAI9C;IACI,yBAA6B,EAAA;IAE7B;MAII,yBAAoC,EAAA;EAI5C;IACI,yBAAgC,EAAA;IAEhC;MAII,yBAAuC,EAAA;EAI/C;IACI,yBAAgC,EAAA;IAEhC;MAII,yBAAuC,EAAA;EAI/C;IACI,yBAAgC,EAAA;IAEhC;MAII,yBAAuC,EAAA;EAI/C;;;;IASI,oCAAyC;IACzC,yBAAuC;IACvC,2BAA2B,EAAA;EAG/B;IAEI,yBAAuC,EAAA;IAEvC;MACI,gBAAgB,EAAA;EAS5B;IACI,cDjH4B;ICkH5B,qBDlH4B,EAAA;ICoH5B;MAGI,6BDnF4B;MCoF5B,cDxHwB;MCyHxB,qBDzHwB;MC0HxB,gBAAgB,EAAA;EAIxB;IACI,cD/H4B,EAAA;ICiI5B;MAII,6BDjG4B;MCkG5B,cDtIwB;MCuIxB,qBAAqB;MACrB,gBAAgB,EAAA;EAtCpB;IFWA,cC5D4B,EAAA;EDuDpC;IAWY,cCpEwB,EAAA;EDyDpC;;IAiBa,yBCzHuB;ID0HvB,cC3EuB,EAAA;EDyDpC;IAwBQ,6BAA6B,EAAA;;AAIrC;EI/IE,YJmJyB;EIhJzB,yBAAQ;EJiJF,oBAAoB,EAAA;;AAG5B;EACI,iBCqEqC;EDpErC,qBC/FgC;EDgGhC,kBAAgE;EAChE,6BC5GoC,EAAA;;AD+GxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAYY,6BC3H4B,EAAA;;ADgIxC;EACE,SCzK4B;ED0K5B,sBCmBkC;EDlBlC,6BCnIsC,EAAA;;ADsIxC;EEYG,eD6DgC;EC5DhC,kBD1CgC;EC2ChC,kBDSgC,EAAA;ECPhC;IACI,kBAAsD,EAAA;;AFd7D;EESG,mBD2DoC;EC1DpC,kBD5CgC;EC6ChC,iBDYgC,EAAA;ECVhC;IACI,iBAAsD,EAAA;;AFV7D;EACI,gBAAgB,EAAA;;AAEpB;EACI,WAAW,EAAA;;AAEf;EACI,gBAAgB,EAAA;;AAEpB;EACI,kBAAkB;EAClB,QAAQ;EACR,gBAAgB;EAChB,UAAU,EAAA;;AAEd;EAEI,iBAAiB,EAAA;;AAFrB;EAMM,oCAAwC,EAAA;;AAM9C;EACI,iBChN4B;EDiN5B,mBCa+B;EDZ/B,mBCpB+B;EDqB/B,kBCrB+B,EAAA;EDiBnC;IAOQ,kBAAiE,EAAA;;AAIzE;EAEI,aAAa,EAAA;;AK/Mf;ECvBA,cL6BkC,EAAA;;AILlC;ECxBA,cL6BkC,EAAA;;AIJlC;ECzBA,cL6BkC,EAAA;;AKzBpC;EACI,yBLQgC;EKPhC,yBLsBgC;EKrBhC,kBLkJ+B;EKjJ/B,cLLgC;EKMhC,mBAAmB;EACnB,YAAY;EACZ,eLoP+B;EI1B/B,oGAAoG;EFpO9F,gBGYkB,EAAA;EAT5B;IAYQ,yBLa4B;IE5B1B,gBGgBsB;IACxB,qBAAqB;IACrB,cLyC4B,EAAA;IKxDpC;;;;MAqBY,sBAAsB;MACtB,iBAAiB;MACjB,6BLsB4B,EAAA;EKlBpC;;;;IH9BM,gBGkCsB,EAAA;EAG5B;IACI,sBLRyB;IKSzB,cLrC4B,EAAA;IKmChC;MAKQ,+BAA+B,EAAA;EAGvC;IACI,yBLyB4B;IKxB5B,cLwB4B,EAAA;EKtBhC;IACI,yBLE4B;IKD5B,yBL6B4B;IK5B5B,cL4B4B,EAAA;IK/BhC;MAMQ,+BAA+B,EAAA;EAGvC;IACI,yBL/C4B;IKgD5B,yBLoB4B,EAAA;EK7EpC;IA6DQ,kBLyF2B;IKxF3B,eL6L2B;IK5L3B,gBAAgB;IAChB,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,sBAAsB,EAAA;EAG1B;IACI,0BAA4D;IAC5D,gCAAgC,EAAA;EAxExC;;IA6EQ,yBLnE4B,EAAA;;AIwBhC;;EAGQ,yBJuJwB,EAAA;;AI1JhC;;EAOQ,kBJmJwB,EAAA;EI1JhC;;;;IAWY,yBAAkE,EAAA;;AAK9E;;EAGQ,kBAAsD,EAAA;EAH9D;;;;IAOY,yBAA8E,EAAA;;AAP1F;;;;EAaQ,yBAA0E,EAAA;EAblF;;;;IAgBY,2BAA6F,EAAA;;AASzG;;EAGQ,kBJ8GwB,EAAA;EIjHhC;;;;IAOY,yBAAkE,EAAA;;AAP9E;;;;EAaQ,yBJoGwB,EAAA;;AIhGhC;;EAGQ,4BAAoG,EAAA;EAH5G;;;;IAOY,yBAA8E,EAAA;;AAP1F;;;;EAaQ,yBAA8E,EAAA;EAbtF;;;;;;;;IAiBY,2BAA2F,EAAA;;ACxB3G;;EAKQ,sBL/DyB;EKgEzB,cL5F4B;EK6F5B,yBLnF4B;EKoF5B,kBAAkB,EAAA;;AAM1B;;;;;;EAKQ,0CL1E0C;EK2E1C,mBAAmB,EAAA;EAN3B;;;;;;;;;;;;;;;;IAUY,mBAAmB;IACnB,0CL/EsC,EAAA;;AKoElD;;;;EAmBgB,0CLvFkC,EAAA;;AI4C9C;;;;ECkDI,0CL/F0C;EKgG1C,YAAY,EAAA;;AAIpB;EAEQ,cLvD4B,EAAA;;AK0DpC;EAEQ,cLrE4B,EAAA;;AKyEpC;EAEI,kBLM+B,EAAA;EKRnC;IAIM,yBLpE8B;IKqE9B,kBAAkB,EAAA;;AALxB;EASI,cAAc;EACd,WAAW;EACX,cL3EgC;EK4EhC,eAAe,EAAA;;AAInB;EAEI,kBLV+B,EAAA;EKQnC;IAKM,kBAAkB,EAAA;;AAMxB;;EAGI,yBLjKgC;EKkKhC,qBLnJgC,EAAA;;AK+IpC;;EAUM,0CLnJ4C,EAAA;;AKyIlD;;EAkBQ,yBLzI4B,EAAA;;AKuHpC;;EA2BQ,yBLjJ4B;EKkJ5B,yBLhI4B;EKiI5B,kBAAkB,EAAA;;AAM1B;;EAEI,6BAA6B;EAC7B,yBLvLgC;EKwLhC,cLvJgC;EKwJhC,4BL3D+B;EK4D/B,+BL5D+B;EI4E/B,oGAAoG,EAAA;ECtBxG;;IASM,WAAW,EAAA;EAKb;;IACE,yBLzK8B,EAAA;EK2KhC;;IACE,yBL3K8B,EAAA;EK6KhC;;IACE,yBLvN8B;IKwN9B,cLpJ8B,EAAA;EKsJhC;;IACE,yBL3N8B;IK4N9B,cLjK8B,EAAA;EKmKhC;;IACE,cL3J8B,EAAA;EK6JhC;;IACE,cLvK8B,EAAA;EKuIpC;;;;ID/MI,uBJsMgC;IK+C5B,kBAAkB,EAAA;EAtC1B;;IA0CQ,WAAW,EAAA;;AAInB;;EAEE,SAAS,EAAA;;AAIX;EACE,iBAAiB,EAAA;;AAEnB;EACE,kBAAkB,EAAA;;AAGpB;;EAEI,mBAAmB;EACnB,kBAAkB,EAAA;EAHtB;;IAMQ,eAAe,EAAA;EANvB;;IAUQ,cLnM4B,EAAA;;AKuMpC;;EAGQ,yBLhQ4B,EAAA;;AKoQpC;EACI,kBLvI+B;EKwI/B,yBAAyB;EACzB,4BAA4B;EAC5B,mBAAmB,EAAA;;AAGvB;;;EAGI,oBAAoB,EAAA;;AAExB;;;EAGI,mBAAmB,EAAA;;AAEvB;EACI,yBLtRgC;EKuRhC,cLtPgC;EKuPhC,mBAAmB,EAAA;;AAGvB;EACI,iBL7S4B;EK8S5B,oBLlHgC,EAAA;;AKoHpC;EACI,qBL/RgC,EAAA;;AKkSpC;EACI,cAAc,EAAA;;AAElB;EACI,eAAe;EACf,gBAAgB;EAChB,sBAAsB;EACtB,YAAY;EACZ,YAAY;EACZ,yBL5SgC;EK6ShC,kBL/K+B;EKgL/B,cAAc,EAAA;;AAGlB;;;;EAKQ,mBAA6C,EAAA;;AAIrD;EACI,WAAW;EACX,UAAU;EACV,gBAAgB;EAChB,eAAe,EAAA;;AAGnB;EACI,eAAe,EAAA;;AAInB;EACE,UAAU;EACV,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,SAAS;EACT,OAAO;EACP,WAAW;EACX,YAAY;EACZ,YAAY,EAAA;;AAGd;EACI,mBLhHmC,EAAA;;AKmHvC;EACI,UAAU;EACV,kBAAkB;EAClB,cAAc;EACd,gBAAgB,EAAA;;AAGpB;;EAGQ,wBAAwB;EACxB,iBAAiB;EACjB,gBAAgB,EAAA;;AALxB;EASQ,mBAAmB,EAAA;EAT3B;IAYW,eAAe,EAAA;;AAZ1B;EAiBQ,gBAAgB;EAChB,wBAAwB,EAAA;;AAlBhC;EAsBQ,eAAe,EAAA;;AC/YvB;;;;;EAKI,8DNE2E,EAAA;;AMA/E;EACI,gBNgQ+B,EAAA;;AM7PnC;EACI,cN0DgC,EAAA;EM3DpC;IAIQ,cNuD4B,EAAA;;AMpDpC;EACI,gBNkOgC;EMjOhC,iBAAiB;EACjB,mBAAwC,EAAA;EAH5C;IAMQ,gBNkP2B;IMjP3B,yBAAyB;IACzB,WAAW,EAAA;;AAGnB;EACI,gBNwNgC;EMvNhC,mBAAwC,EAAA;;AAE5C;EACI,cNqN8B;EMpN9B,mBAAwC;EACxC,kBAAkB,EAAA;;AAEtB;EACI,kBNiNkC;EMhNlC,mBAAmB;EACnB,gBAAqC;EACrC,mBNqG+B,EAAA;EMzGnC;;;IAQQ,iBAAiB,EAAA;;AAGzB;EACI,iBNuMiC;EMtMjC,kBAAkB;EAClB,mBAAmB,EAAA;;AAEvB;EACI,cNmM8B;EMlM9B,gBNkN+B;EMjN/B,yBAAyB,EAAA;;AAE7B;EAEQ,iBAAiB,EAAA;;AAUzB;EACI,gBNkM+B,EAAA;EMnMnC;IAIQ,yBAAyB,EAAA;IAJjC;MAOY,cNvDwB;MMwDxB,qBAAqB,EAAA;EARjC;IAYQ,iBAAiB,EAAA;;AAIzB;;;;EAII,cN7DgC;EM8DhC,gBN2K+B,EAAA;;AMzKnC;;EAEI,0BAA0B;EAC1B,gBNuK+B;EMtK/B,cNpEgC;EMqEhC,mBN2JmC,EAAA;;AMxJvC;EACI,cNmJ8B,EAAA;;AMhJlC;;EAEE,yBAAgC,EAAA;;AAElC;;EAEE,yBAA6B,EAAA;;AAE/B;;EAEE,yBAAgC,EAAA;;AAElC;;EAEE,yBAAgC,EAAA;;AAElC;;EAEE,yBAA+B,EAAA;;AAGjC;;EAEI,yBAA6B,EAAA;;AAIjC;EACI,iBAAiB;EACjB,yBN1EgC;EM2EhC,aAAa;EACb,gBN0HgC;EMzHhC,gBAAgB,EAAA;EALpB;IAQQ,cNhF4B;IMiF5B,mBNiH+B;IMhH/B,yBAAyB,EAAA;EAVjC;IAcQ,qBN7E4B;IM8E5B,cN9E4B,EAAA;IM+DpC;MAkBY,cNjFwB,EAAA;EM+DpC;IAuBQ,qBN1E4B;IM2E5B,cN3E4B,EAAA;IMmDpC;MA2BY,cN9EwB,EAAA;EMmDpC;IAgCQ,sCN5H0C;IM6H1C,cNxJ4B,EAAA;IMuHpC;MAoCY,+BNhIsC,EAAA;;AOzClD;EACI,cPsBgC;EOrBhC,eP8P+B;EO7P/B,8DPI2E;EOH3E,kCAAkC;EAClC,mCAAmC,EAAA;;AAGvC;EACI,kBAAkB;EAClB,mBPIgC,EAAA;;AOFpC,eAAA;AACA;;;;;;;;;;;;;;;;;;;;;ELHI,6BF2R+B,EAAA;;AO3QnC;;;ELhBI,6BF2R+B,EAAA;;AOrQnC;;;;EN+KI,gEAAgE;EAGhE,yBAAyB,EAAA;;AM3K7B;EACI,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,kBAAkB;EAClB,mBP9BgC,EAAA;EOwBpC;IASQ,eAAe,EAAA;EATvB;IAaQ,WAAW,EAAA;;AAInB;EACI,qBAAqB;EACrB,QAAQ;EACR,SAAS;EACT,gBAAgB;EAChB,sBAAsB;EACtB,sBAAsB;EACtB,uBAAuB;EACvB,mCAAmC;EACnC,kCAAkC,EAAA;;AAGtC;EACE,WAAW,EAAA;;AAEb;EACE,YAAY,EAAA;;AAId;EAEI,iBAAiB;EACjB,mBAAmB,EAAA;EAHvB;IAMM,aAAa,EAAA;EANnB;;IAUM,yBAA8B,EAAA;;AAVpC;EAcI,qBAAqB,EAAA;;AAdzB;EAkBM,kBAAkB;EAClB,sBAAsB;EACtB,kCAAkC;EAClC,WAAW;EACX,YAAY;EACZ,WAAW,EAAA;;AAvBjB;EA0BM,UAAU,EAAA;;AA1BhB;EA6BM,oCAAoC;EACpC,WAAW;EACX,cAAc;EACd,YAAY;EACZ,OAAO;EACP,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,UAAU,EAAA;;AAKhB;EAEI,uBAAuB,EAAA;;AAM3B;ELmGI,qBFpKgC;EEqKhC,yBFrKgC,EAAA;;AOiEpC;ELmGI,qBF3JgC;EE4JhC,yBF5JgC,EAAA;;AOwDpC;ELmGI,qBFrJgC;EEsJhC,yBFtJgC,EAAA;;AOkDpC;ELmGI,qBFxJgC;EEyJhC,yBFzJgC,EAAA;;AOqDpC;ELmGI,qBFlJgC;EEmJhC,yBFnJgC,EAAA;;AO+CpC;ELmGI,qBF/IgC;EEgJhC,yBFhJgC,EAAA;;AO4CpC;ELmGI,qBFnNgC;EEoNhC,yBFpNgC;EOqI9B,cAAc,EAAA;;AAIpB;EAGM,mBAAmB,EAAA;;AC1JzB;;EAEI,mBAAmB;EACnB,kBAAkB,EAAA;;AAGtB;EACE,eAAe;EACf,oBAAoB,EAAA;EAFtB;IAKQ,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;IACf,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB,EAAA;EAVxB;;IAeQ,YAAY;IACZ,qBAAqB;IACrB,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,OAAO;IACP,eAAe;IACf,kBAAkB;IAClB,MAAM;IACN,yBAAyB;IAKzB,+BAA+B,EAAA;EA7BvC;IAgCQ,0BAA0B;IAC1B,gBAAgB;IAChB,SAAS;IACT,kBAAkB;IAClB,eAAe;IACf,UAAU;IACV,WAAW;IACX,SAAS;IACT,yBAAyB,EAAA;EAxCjC;IA4CY,cRpBwB;IQqBxB,WAAW;IACX,mBAAmB,EAAA;;AAW/B;;EAEI,UAAU;EACV,kBAAkB;EAClB,kBAAkB,EAAA;;AAEtB;EACI,UAAU,EAAA;;AAGd;;EAEI,mBAAmB,EAAA;;AAGvB;;EAEI,+BAA+B,EAAA;;AAGnC;EACE,iBAAiB,EAAA;EADnB;IAIQ,kBAAkB,EAAA;EAJ1B;IAQY,cR7DwB;IQ8DxB,WAAW;IACX,mBAAmB,EAAA;;AAK/B;EACI,0BAA0B;EAC1B,gBAAgB;EAChB,eAAe;EACf,mCAAmC;EACnC,kCAAkC;EAClC,qBAAqB;EACrB,kBAAkB;EAClB,YAAY;EACZ,SAAS;EACT,SAAS,EAAA;;AAGb;EACI,yBAAyB,EAAA;;AAG7B;;EAEI,UAAU;ENnEV,+BMoEwC;EACxC,YAAW;EACX,cAAc,EAAA;;AAGlB;EACI,0BAA0B;EAC1B,gBAAgB;EAChB,SAAS;EACT,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,eAAe,EAAA;;AAPnB;EAWI,UAAU,EAAA;;AAId;;EAEI,cR9GgC,EAAA;;AS9BpC;EACI,qBTkTqC;ESjTrC,wBTiTqC;EShTrC,gBAAgB;EAChB,mBAAmB,EAAA;EAJvB;IAOQ,sBAAsB,EAAA;IAP9B;MAUY,cTIwB,EAAA;ISdpC;MAcY,cT+CwB,EAAA;ES7DpC;;IAuBQ,cTsC4B,EAAA;IIrClC;;MKEQ,cTmC0B,EAAA;IIpClC;;MKCQ,cTmC0B,EAAA;IInClC;;MKAQ,cTmC0B,EAAA;ES7DpC;;IA+BQ,cT8B4B;IS7B5B,WAAW,EAAA;EAhCnB;;IAsCM,SAAS;IACT,iBAAiB;IACjB,iBAAiB,EAAA;IAxCvB;;;;;;MA6CQ,cTgB4B,EAAA;MS7DpC;;;;;;QAgDU,UAAU,EAAA;IAhDpB;;MAsDU,cTO0B,EAAA;MIrClC;;QKiCU,cTIwB,EAAA;MIpClC;;QKgCU,cTIwB,EAAA;MInClC;;QK+BU,cTIwB,EAAA;ES7DpC;IAgEQ,qBAAqB;IACrB,SAAS;IACT,kBAAkB;IAClB,cAAc;IACd,gBAAgB,EAAA;EApExB;IAwEQ,kBAAkB;IAClB,WAAW;IACX,iBAAiB;IACjB,aAAa,EAAA;EAGjB;IAEQ,OAAO;IACP,cAAc,EAAA;EAjF1B;IAsFQ,oBAAoB;IACpB,mBAAmB,EAAA;IAvF3B;MA0FY,mBAAmB,EAAA;MA1F/B;QA6FgB,SAAS,EAAA;IA7FzB;MAmGgB,eAAe,EAAA;IAnG/B;MAwGoB,WAAW,EAAA;EAxG/B;IAkHY,kBAAkB;IAClB,OAAO;IACP,QAAQ;IACR,cAAc;IACd,WAAW;IACX,SAAS,EAAA;EAvHrB;IA2HY,kBTyEuB,EAAA;ISpMnC;MA6HgB,kBTkFmB,EAAA;IS/MnC;MAgIgB,iBTkFmB,EAAA;ESlNnC;IAqIY,yBAAyB;IACzB,mBTwH2B;ISvH3B,sBTgEwB;IS/DxB,qBTqIyB;ISpIzB,iBAAiB,EAAA;IAzI7B;;MA6IgB,gBAAgB,EAAA;IA7IhC;;MAkJgB,eAAe;MACf,kBAAkB;MAClB,QAAQ;MACR,kBAAkB;MAClB,WAAW,EAAA;IAtJ3B;MA0JgB,QAAQ;MACR,eAAe,EAAA;IA3J/B;MAgKoB,WAAW;MACX,YAAY,EAAA;IAjKhC;MAsKgB,WAAW;MACX,cTzJoB,EAAA;ESdpC;;;;IA+KY,kBTtBuB;ISuBvB,cTnHwB,EAAA;ES7DpC;IAqLQ,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,6BAA6B,EAAA;EA1LrC;IA8LQ,0BAA0B;IAC1B,eTmE2B;ISlE3B,mBTM4B;ISL5B,sBTK4B;ISJ5B,qBT2E6B,EAAA;ES7QrC;IAsMQ,WAAW;IACX,YAAY;IACZ,sBAAsB;IACtB,UAAU;IACV,eAAe,EAAA;IA1MvB;MA6MY,UAAU;MACV,WAAW;MACX,kBAAkB;MAClB,cAAc,EAAA;EAhN1B;IAsNY,WAAW,EAAA;EAtNvB;IA2NM,wCAA4C;IAC5C,gBAAgB;IAChB,6BAA6B,EAAA;IA7NnC;MAgOQ,cTnK4B,EAAA;MS7DpC;QAmOU,WAAW;QACX,cTvK0B,EAAA;IS7DpC;MAyOW,mBT5KyB,EAAA;IS7DpC;MA6OQ,cThL4B,EAAA;IS7DpC;;;;;MAoPQ,cT9K4B,EAAA;EStEpC;IA0PY,cT7LwB,EAAA;IS7DpC;MA6PgB,WAAW;MACX,cTjMoB,EAAA;ES7DpC;IAmQY,mBTtMwB,EAAA;ES7DpC;;;;IA0QY,cT9LwB,EAAA;ES5EpC;IA8QY,yBTjNwB,EAAA;ES7DpC;IAqRU,eTrByB,EAAA;;AS2BnC;EACI,oCAA2C,EAAA;;AAG/C;EACI,oCAA2C,EAAA;;AAG/C;EACI,oCAAwC,EAAA;;AAG5C;EACI,oCAA2C,EAAA;;AAG/C;EACI,oCAA0C,EAAA;;AAG9C;EACI,oCAA2C,EAAA;;AAG/C;EACI,oCAAyC,EAAA;;ACpT7C;EACI,iBAAiB;EACjB,kBAAkB;EAClB,UAAU;EACV,cVUgC;EUThC,kBAAkB,EAAA;EALtB;IAQQ,kBAAkB;IAClB,sBAAsB;IACtB,kCAAkC;IAClC,WAAW;IACX,YAAY;IACZ,WAAW,EAAA;EAbnB;IAiBQ,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,UAAU;IAGV,gCAAgC;IAChC,kBAAkB;IAClB,cAAc;IACd,eAAe;IACf,WAAW;IACX,gBAAgB,EAAA;EA5BxB;IAiCQ,kBAAkB;IAClB,SAAS;IACT,WAAW,EAAA;EAnCnB;IAuCQ,YAAY;IACZ,UAAU,EAAA;EAxClB;;IA6CQ,+BVJ0C,EAAA;EUzClD;IAiDQ,gBAAgB;IAChB,iBAAiB,EAAA;EAlDzB;IAsDQ,gBAAgB;IAChB,iBAAiB,EAAA;EAvDzB;IA2DQ,mBAAmB,EAAA;EA3D3B;IA8DQ,gBAAgB,EAAA;EA9DxB;IAmEQ,kBAAkB;IAClB,UAAU;IACV,WAAW;IACX,YAAY;IACZ,cAAc;IACd,OAAO;IACP,MAAM;IACN,WAAW,EAAA;EA1EnB;IA8EQ,oCAAgC,EAAA;EA9ExC;ICCI,iCXsBgC;IWtBX,+CAAA;IAC0C,0BAAA;IACN,2BAAA;IACE,0BAAA;IAC3D,+EAAoD;IAAE,oBAAA,EAAqB;;ACL/E;;EAGI,cAAc;EACd,yGAAyG,EAAA;;AAJ7G;;ETGE,USIsB;ETDtB,wBAAQ;EDJA,gDFoRiD;EY7QrD,kBAAkB;EAClB,kBAAkB,EAAA;EAVxB;;IV0DO,gDAAiD,EAAA;EU1DxD;;IV0DO,gDAAiD,EAAA;;AU1DxD;;ETGE,USkBoB;ETfpB,0BAAQ;ESgBN,mBAAmB,EAAA;EAtBvB;;IV0DO,kDAAiD;IUhClD,oBAAoB;IACpB,oBAAoB,EAAA;EA3B1B;;IV0DO,iDAAiD;IU3BlD,uBAAuB;IACvB,iBAAiB,EAAA;;AAKvB;EAGM,aAAa,EAAA;;AAHnB;EASM,kBAAkB;EAClB,yBAAyB,EAAA;;AAK/B;;EVMO,gDAAiD;EUHpD,oBAAoB;EACpB,oBAAoB,EAAA;;AAIxB;;EAEE,UAAU;EACV,mBAAmB,EAAA;EAHrB;;IVcQ,8CAA6C,EAAA;;AUJrD;EVZO,kDAAiD;EUgBlD,oBAAoB;EACpB,oBAAoB,EAAA;;AAL1B;EAUQ,4BAA4B,EAAA;;AAMpC;EAEI,oBAAoB,EAAA;;AAFxB;EAMI,oBAAoB,EAAA;;AAGxB;EACI,yBZlFgC;EYmFhC,cAAc;EACd,mBZ2DgC;EY1DhC,gBAAgB;EAChB,YAAY,EAAA;EALhB;IAQQ,yBZpC4B;IYqC5B,WAAW,EAAA;EATnB;IAaQ,cZ9E4B;IY+E5B,mBZkJ+B;IYjJ/B,kBZ8B4B,EAAA;EY7CpC;IAmBQ,cAAc;IACd,gBAAgB;IAChB,kBAAkB;IAClB,kBAAkB,EAAA;EAtB1B;IA0BQ,cZrH4B;IYsH5B,eZsI2B;IYrI3B,4BAA4B;IAC5B,WAAW;IACX,mBAAmB;IACnB,WAAW;IACX,cAAc,EAAA;IAhCtB;MAmCW,gBAAgB,EAAA;EAnC3B;IAwCQ,qBAAqB,EAAA;EAGzB;IACI,eAAe,EAAA;EA5CvB;IAgDO,4BZc6B;IYb7B,6BZa6B,EAAA;EY9DpC;IAqDQ,+BZS4B;IYR5B,gCZQ4B,EAAA;EYLhC;IACI,gBAAgB;IAChB,qBAAqB,EAAA;EA3D7B;;IAgEQ,yBAA8B;IAC9B,UAAU;IACV,qBAAqB,EAAA;EAlE7B;;IAuEQ,yBZzG4B,EAAA;EYkCpC;;IA4EQ,yBZ1D2C,EAAA;EYlBnD;;IAgFQ,yBZ7DwC,EAAA;EYnBhD;;IAoFQ,yBZhE2C,EAAA;EYpBnD;;IAwFQ,yBZnE2C,EAAA;EYrBnD;;IA4FQ,yBZtE0C,EAAA;;AY0ElD;EACE,oBAAoB,EAAA;;AAGtB;EACI,iBAAiB,EAAA;;AAErB;EACI,WAAW;EACX,UAAU,EAAA;;AAGd;;;;;;EAMI,iCZ9IgC;EY+IhC,wCAAwC;EACxC,yCAAyC;EACzC,WAAW;EACX,qBAAqB;EACrB,kBAAkB;EAClB,WAAW;EACX,UAAU,EAAA;;AAGd;;;;;;EAMI,iCZnNgC;EYoNhC,wCAAwC;EACxC,yCAAyC;EACzC,WAAW;EACX,qBAAqB;EACrB,kBAAkB;EAClB,WAAW;EACX,UAAU,EAAA;;AAGd;;EAEI,qBAAqB;EACrB,WAAW,EAAA;;AAEf;;EAEI,qBAAqB;EACrB,WAAW,EAAA;;AAKf;EACI;IACE,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;IACjB,kBAAkB,EAAA;EAEpB;IACE,aAAa,EAAA;EAEf;;;IAGE,uCAAuC;IACvC,yGAAyG,EAAA;EAE3G;;;IAGE,qCAAqC;IACrC,8BAA8B,EAAA;EAEhC;IAKE,4BAA4B,EAAA;EAE9B;IACE,8BAA8B,EAAA;EAGhC;IACE,yGAAyG;IACzG,qCAAqC,EAAA;EAGvC;;IAEI,UAAU;IACV,WAAW,EAAA;EAIf;IAGS,cAAc,EAAA;EAMvB;IACI,wBAAwB,EAAA,EAC3B;;AAGL;EAEQ,cZjT4B;EYkT5B,eZtD2B,EAAA;EYmDnC;IAMY,iBAAiB;IACjB,sCAAsC,EAAA;IAPlD;MAUgB,eAAe,EAAA;EAV/B;IAcY,cAAc;IACd,cAAc,EAAA;EAf1B;IAkBY,YAAY,EAAA;;AAlBxB;;;;EA0BY,yBZ/TwB;EYgUxB,cZ1UwB;EY2UxB,UAAU;EACV,qBAAqB,EAAA;;AAIjC;;EAEI,kBAAkB,EAAA;;AAGtB;EAEQ,kBAAkB,EAAA;EAF1B;IAIY,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB,EAAA;IAN7B;MAQgB,eAAe,EAAA;IAR/B;MAWgB,iBAAiB,EAAA;EAXjC;IAeY,cZtRwB,EAAA;IYuQpC;MAiBgB,cZxRoB,EAAA;;AYuQpC;;EAwBY,cZ7WwB;EY8WxB,UAAU;EACV,qBAAqB,EAAA;;AA1BjC;EA+BY,kBAAkB,EAAA;;AAI9B;;;EAGI,8BAA8B;EAC9B,mCAAmC;EACnC,oCAAoC;EACpC,WAAW;EACX,qBAAqB;EACrB,kBAAkB;EAClB,WAAW;EACX,aAAa,EAAA;;AAGjB;;;EAGI,2BAA2B;EAC3B,mCAAmC;EACnC,oCAAoC;EACpC,WAAW;EACX,qBAAqB;EACrB,kBAAkB;EAClB,WAAW;EACX,aAAa,EAAA;;AAGjB;;EAGQ,cAAc,EAAA;;AAItB;EAGgB,gCAAgC;EAChC,eAAe;EACf,cAAc,EAAA;EAL9B;IAQoB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,mBAAmB,EAAA;IAZvC;MAgBwB,cAAc;MACd,kBAAkB;MAClB,QAAQ;MACR,iBAAiB;MACjB,SAAS,EAAA;IApBjC;MAuBwB,gBAAgB;MAChB,gBAAgB;MAChB,iBAAiB,EAAA;IAzBzC;MA4BwB,cAAc;MACd,gBAAgB;MAChB,iBAAiB,EAAA;EA9BzC;IAkCoB,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,UAAU;IACV,QAAQ;IACR,iBAAiB,EAAA;EAvCrC;IA0CoB,qBAAqB,EAAA;IA1CzC;MA6CwB,cAAc;MACd,oCAAoC,EAAA;IA9C5D;MAiDwB,qBAAqB,EAAA;;AAjD7C;EAwDQ,yBAAyB;EACzB,0BAA0B,EAAA;EAzDlC;IA4DY,6BAA6B;IAC7B,gBAAgB,EAAA;IA7D5B;MA+DgB,qBAAqB;MACrB,gBAAgB;MAChB,eAAe,EAAA;MAjE/B;QAoEoB,cAAc;QACd,gBAAgB;QAChB,iBAAiB,EAAA;;ACnerC;EACI,SAAS;EACT,kBbuJ+B;EatJ/B,cbWgC;EaVhC,kBAAkB;EAClB,qBAAqB;EACrB,kBAAkB,EAAA;EANtB;IASM,yBAA6C,EAAA;EATnD;IAaM,yBAA4C,EAAA;EAblD;IAiBM,yBAA6C,EAAA;EAjBnD;IAqBM,yBAA0C,EAAA;EArBhD;IAyBM,yBAA6C,EAAA;EAzBnD;IA6BM,yBAA6C,EAAA;EA7BnD;IAiCM,cbnB8B;IaoB9B,WAAW;IACX,iBAAiB;IACjB,cAAc;IACd,UAAU,EAAA;IArChB;;MAyCU,0BAA0B,EAAA;IAzCpC;MA8CQ,UAAU,EAAA;EA9ClB;IAmDQ,eAAe;IACf,cAAc;IACd,UAAU;IACV,kBAAkB;IAClB,QAAQ;IACR,iBAAiB,EAAA;EAxDzB;IA4DQ,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,YAAY,EAAA;EAlEpB;IAsEQ,cAAc;IACd,cAAc,EAAA;EAvEtB;IA2EQ,kBAAkB,EAAA;;AC3E1B;EACI,eAAe;EACf,kBduJ+B,EAAA;;AcrJnC;EACI,gDdkR2D,EAAA;;AevR/D;;;;;kCnB09EkC;AmBp9ElC;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,4QAA4Q;EAC5Q,mBAAmB;EACnB,kBAAkB,EAAA;;AAEpB;;0BnBu9E0B;AmBp9E1B;EACE,qBAAqB;EACrB,gDAAgD;EAChD,kBAAkB;EAClB,WAAW;EACX,oBAAoB;EACpB,0BAAA;EACA,mCAAmC;EACnC,kCAAkC,EAAA;;AAEpC;;0BnBu9E0B;AmBp9E1B;EACE,uBAAuB;EACvB,oBAAoB,EAAA;;AAEtB;EACE,cAAc,EAAA;;AAEhB;EACE,cAAc,EAAA;;AAEhB;;oCnBu9EoC;AmBp9EpC;;EAEE,qBAAqB;EACrB,oBAAoB;EACpB,sBAAsB,EAAA;;AAExB;EACE,kBAAkB,EAAA;;AAEpB;;0BnBu9E0B;AmBp9E1B;EACE,eAAe;EACf,yBAAyB;EACzB,qBAAqB,EAAA;;AAEvB;EACE,kBAAkB,EAAA;;AAEpB;EACE,kBAAkB;EAClB,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB,EAAA;;AAEpB;EACE,MAAM;EACN,mBAAmB,EAAA;;AAErB;;EAEE,kBAAkB;EAClB,kBAAkB,EAAA;;AAEpB;;0BnBu9E0B;AmBp9E1B;EACE,kDAAkD;EAElD,0CAA0C,EAAA;;AAE5C;EACE;IACE,+BAA+B,EAAA;EAEjC;IACE,iCAAiC,EAAA,EAAA;;AAWrC;EACE;IAKE,uBAAuB,EAAA;EAEzB;IAKE,yBAAyB,EAAA,EAAA;;AAG7B;;0BnBi9E0B;AmB98E1B;EACE,gEAAgE;EAKhE,wBAAwB,EAAA;;AAE1B;EACE,gEAAgE;EAKhE,yBAAyB,EAAA;;AAE3B;EACE,gEAAgE;EAKhE,yBAAyB,EAAA;;AAE3B;EACE,gEAAgE;EAKhE,uBAAuB,EAAA;;AAEzB;EACE,gEAAgE;EAKhE,uBAAuB,EAAA;;AAEzB;;0BnBi9E0B;AmB78E1B;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAIpB,oCAAA;ACrjBA;EAGM,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,gBAAgB;EAChB,cAAc,EAAA;;AAPpB;EAWM,eAAe;EACf,WAAW,EAAA;;AAZjB;EAgBQ,SAAS,EAAA;EAhBjB;;IAoBY,UAAU;IACV,SAAS,EAAA;;AArBrB;EA0BQ,SAAS,EAAA;;AA1BjB;EA8BM,gBAAgB,EAAA;;AAGlB;EACI,gBAAgB,EAAA;EADpB;;;;;;IASQ,cAAc;IACd,iBAAiB,EAAA;;AA3C7B;EAgDO,eAAe;EACf,gBhByN4B;EgBxN5B,iBAAiB;EACjB,yBAAyB;EACzB,SAAS,EAAA;;AApDhB;;EAyDO,aAAa;EACb,gBAAgB;EAChB,UAAU;EACV,WAAW,EAAA;EA5DlB;;IA+DW,kBAAkB,EAAA;EA/D7B;;;IAqEgB,UAAU;IACV,UAAU,EAAA;;AAtE1B;;;;;;EAgFO,iBAAiB;EACjB,sBAAsB,EAAA;;AAjF7B;EAqFO,gBAAgB,EAAA;;AArFvB;EAwFO,eAAe;EACf,gBhB8K4B;EgB7K5B,eAAe;EACf,kBAAkB;EAClB,QAAQ;EACR,iBAAiB,EAAA;;AA7FxB;EAgGQ,gBhB0K2B;EgBzK3B,iBhBwJ6B;EgBvJ7B,iBAAiB;EACjB,iBAAiB,EAAA;;AAnGzB;EAuGI,WAAW,EAAA;;AAvGf;EA2GQ,kBAAkB,EAAA;;AAI1B;EAEQ,chByI0B;EgBxI1B,yBAAyB,EAAA;;AAHjC;EAMQ,chBuI0B,EAAA;EgB7IlC;IASY,cAAc;IACd,kBAAkB,EAAA;;AAV9B;EAcQ,gBhB2I2B;EgB1I3B,gBAAgB,EAAA;EAfxB;IAiBY,chBlGwB;IgBmGxB,iBAAiB;IACjB,gBhBqIuB,EAAA;;AgBxJnC;EAuBO,gBhBiI4B;EgBhI5B,kBhBiH+B,EAAA;;AgBzItC;EA2BQ,gBAAgB,EAAA;;AA3BxB;EA8BQ,iBAAiB;EACjB,gBAAgB,EAAA;EA/BxB;IAkCY,iBAAiB,EAAA;;AAlC7B;EAuCQ,YAAY;EACZ,iBAAiB;EACjB,gBAAgB;EAChB,cAAc,EAAA;EA1CtB;IA6CY,WAAW,EAAA;;AAKvB;EACE,gBAAgB;EAChB,oBAAoB,EAAA;;AAGtB;EACI,mBAAmB,EAAA;;AAGvB;EACE,yBAAyB,EAAA;;AC3K3B;EACI,kBAAkB;EAClB,MAAM;EACN,aAAa,EAAA;EAHjB;IAMQ,iBAAiB;IACjB,YAAY,EAAA;;AAIpB;;EAEI,eAAe;EACf,MAAM;EACN,YAAY;EACZ,SAAS;EACT,YAAY;EACZ,OAAO;EACP,aAAa;EACb,4BAA4B,EAAA;EAThC;;IAYQ,kBAAkB;IAClB,0BAA0B;IAC1B,cAAc;IACd,YAAY;IACZ,UAAU;IACV,qBAAqB,EAAA;IAjB7B;;MAoBU,wBAAwB,EAAA;IApBlC;;MAwBY,YAAY,EAAA;EAxBxB;;IA6BM,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,UAAU,EAAA;EAhChB;;IAqCM,kBAAkB;IAClB,sBAAsB;IACtB,cAAc;IACd,YAAY;IACZ,WAAW;IACX,WAAW;IACX,gBAAgB,EAAA;IA3CtB;;MA8CU,WAAW;MACX,YAAY,EAAA;EA/CtB;;IAoDQ,gBAAgB;IAChB,cAAc,EAAA;IArDtB;;MAwDY,SAAS;MACT,kBAAkB;MAClB,WAAW,EAAA;IA1DvB;;MA+DgB,eAAe,EAAA;IA/D/B;;MAmEgB,mBAAmB;MACnB,cjBjEoB;MiBkEpB,cAAc;MACd,qBAAqB;MACrB,kBAAkB;MAClB,yBAAyB;MACzB,eAAe;MACf,eAAe;MACf,iBAAiB;MACjB,iBAAiB;MACjB,WAAW,EAAA;IA7E3B;;MAiFc,gBAAgB,EAAA;IAjF9B;;;;MAsFc,UAAU,EAAA;IAtFxB;;;;MA2FgB,UAAU,EAAA;IA3F1B;;MAgGY,eAAe;MACf,WAAW;MACX,kBAAkB;MAClB,iBAAiB;MACjB,WAAW;MACX,kBAAkB;MAClB,+BjBzEsC;MiB0EtC,kBAAkB,EAAA;IAvG9B;;MA2GU,gBAAgB,EAAA;IA3G1B;;;;MAiHY,aAAa,EAAA;EAjHzB;;IAuHQ,kBAAkB;IAClB,UAAU;IACV,YAAY;IACZ,WAAW;IACX,cAAc;IACd,MAAM;IACN,OAAO;IACP,sBAAsB;IACtB,kCAAkC,EAAA;IA/H1C;;MAkIY,kBAAkB;MAClB,UAAU;MACV,WAAW;MACX,YAAY;MACZ,WAAW;MACX,cAAc;MACd,mBAAmB;MACnB,UAAU,EAAA;EAzItB;;IA8IQ,kBAAkB;IAClB,mBjB6C4B;IiB5C5B,UAAU,EAAA;IAhJlB;;MAwJY,UAAU;MACV,WAAW;MACX,WAAW;MACX,kBAAkB;MAClB,iBAAiB;MACjB,kBAAkB,EAAA;IA7J9B;;MAiKY,cAAc;MACd,UAAU;MACV,mBAAmB;Mf5GvB,iCAAoC,EAAA;IevD5C;;MAwKY,WAAW;MACX,kBAAkB;MAClB,SAAS;MACT,WAAW;MACX,WAAW;MACX,wBAAwB;MACxB,0CjBjJsC,EAAA;IiB7BlD;;MAmLY,WAAW;MACX,eAAe;MACf,iBAAiB;MACjB,cjBnLwB;MiBoLxB,iBAAiB;MACjB,2DAA2D,EAAA;IAxLvE;;MA4LY,yBAAyB;MACzB,iBAAiC;MACjC,cAAc;MACd,mBAAmB;MACnB,ejBsDuB;MiBrDvB,cjB9LwB;MiB+LxB,qBAAqB;MACrB,gBjB0DuB;MiBzDvB,iBAAiB;MACjB,gBAAgB,EAAA;EArM5B;;IA0MQ,kBAAkB;IAClB,sBAAsB;IACtB,cAAc;IACd,YAAY;IACZ,WAAW;IACX,WAAW;IACX,gBAAgB,EAAA;IAhNxB;;MAmNY,WAAW;MACX,YAAY,EAAA;EApNxB;;;IA0NQ,cAAc;IACd,WAAW;IACX,UAAU;IACV,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,MAAM;IACN,OAAO,EAAA;EAjOf;;If2EI,mBFzBgC;IE6BhC,iDAAwD;IeuJpD,UAAU,EAAA;EflOhB;;IACE,mBFFgC,EAAA;EE0K5B;;;;;;;;;;;;IAME,cFjI0B;IEkI1B,WAAW,EAAA;EAGb;;;;IAEI,UAAU,EAAA;EAMhB;;IACE,cF9I4B,EAAA;EEgJ9B;;IACE,yBFjJ4B;IEkJ5B,WAAW,EAAA;EAKb;;;;;;IAGE,yBAA6B,EAAA;EAE/B;;IACE,yBF7J4B;IE8J5B,WAAW,EAAA;EA5MjB;;IACE,mBFkHgC,EAAA;EElG1B;;;;;;;;;;;;IAME,cFgCwB;IE/BxB,UAAU,EAAA;EAPZ;;;;;;;;;;;;IAME,cFsCwB;IErCxB,UAAU,EAAA;EAPZ;;;;;;;;;;;;IAME,cFmCwB;IElCxB,UAAU,EAAA;EAPZ;;;;;;;;;;;;IAME,cFyCwB;IExCxB,UAAU,EAAA;EAPZ;;;;;;;;;;;;IAME,cF4CwB;IE3CxB,UAAU,EAAA;EAPZ;;;;;;;;;;;;IAME,cFuBwB;IEtBxB,UAAU,EAAA;;Ae4OtB;EACI,gCAAgC,EAAA;;AAEpC;EACI,wBAAwB,EAAA;;AAG5B;;EAIY,cjBhRwB,EAAA;;AiB4QpC;EAQY,oCAAoC,EAAA;;AAMhD;EACI,kBAAkB;EAClB,YAAY;EACZ,yBjB4D2C;EiB3D3C,yBAAyB;EflSzB,0DeqSiE,EAAA;EAPrE;IAUQ,oBAAoB;IACpB,4BAA4B;IAC5B,gBAAgB,EAAA;EAZxB;IAgBQ,gBAAgB,EAAA;EAhBxB;IAqBQ,mBAAmB,EAAA;;AAK3B;;EAGM,YAAY;EACZ,gBAAgB,EAAA;;AAItB;EACE,aAAa;EACb,iBAAiB;EACjB,oBAAoB;EACpB,mBAAmB;EAAG,8BAAA;EAEtB,4EAA4E;EAC5E,kBAAkB;EAClB,gBAAgB,EAAA;EARlB;IAYM,cjBxU8B,EAAA;EiB4TpC;IAeM,gBAAgB;IAChB,+BjBlT4C;IiBmT5C,cAAc;IACd,eAAe,EAAA;IAlBrB;MAqBQ,cjBjV4B,EAAA;;AiBuVpC;EACE,aAAa,EAAA;;AAGf;EACE,aACF,EAAA;;AC3WA;EACI,eAAe,EAAA;EADnB;IAIQ,yBAAyB,EAAA;EAJjC;IAQQ,qBAAqB;IACrB,WAAW;IACX,eAAe,EAAA;EAVvB;IAcQ,gBAAgB;IAChB,UAAU;IACV,gBAAgB,EAAA;IAhBxB;MAmBY,qBAAqB,EAAA;MAnBjC;QAsBgB,cAAc;QACd,elB+KoB;QkB9KpB,mBlBuOuB;QkBtOvB,yBAAyB;QACzB,qBAAqB,EAAA;QA1BrC;UA6BoB,qBAAqB,EAAA;EA7BzC;IAoCQ,mBlB2N+B;IkB1N/B,gBAAgB,EAAA;EArCxB;IAyCQ,cAAc;IACd,WAAW;IACX,YAAY,EAAA;;AC3CpB;EACI,eAAe;EACf,QAAQ;EACR,WAAW;EACX,8BAA0B;EAC1B,aAAa;EACb,0BAA0B;EAC1B,kBAAkB;EAClB,UAAU,EAAA;EARd;;IAYQ,oBAAoB;IACpB,4BAA4B;IAC5B,yBAAyB,EAAA;EAdjC;IAkBQ,cAAc;IACd,aAAa;IACb,0BAA0B;IAC1B,WAAW,EAAA;EArBnB;IAyBQ,WAAW;IACX,qBAAqB;IACrB,qBAAqB;IACrB,YAAY;IACZ,mBAAmB;IACnB,eAAe,EAAA;EA9BvB;IAkCM,QAAQ;IACR,WAAW;IACX,eAAe,EAAA;EApCrB;;IAyCQ,WAAW;IACX,iBAAiB;IACjB,UAAU,EAAA;EA3ClB;IA+CQ,cAAc,EAAA;EA/CtB;IAmDQ,cAAc,EAAA;EAnDtB;;;IAyDQ,cAAc;IACd,kBAAkB,EAAA;EA1D1B;IA8DQ,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,cAAc,EAAA;EAjEtB;;IAsEQ,gBAAgB,EAAA;EAtExB;IA0EQ,yBAAyB;IACzB,kBAAkB;IAClB,eAAe;IACf,qBAAqB;IACrB,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,WAAW,EAAA;IAjFnB;MAoFQ,yBnBxD4B,EAAA;MmB5BpC;QAwFU,sBAAsB,EAAA;EAxFhC;;IA+FQ,qBAAqB,EAAA;EA/F7B;IAmGQ,yBnBvB4B,EAAA;EmB5EpC;IAsGQ,yBnB7B4B,EAAA;EmBzEpC;IAyGQ,yBnBnC4B,EAAA;EmBtEpC;IA4GQ,yBnB7B4B,EAAA;EmB/EpC;IA+GQ,yBnB7B4B,EAAA;EmBlFpC;IAmHQ,eAAe;IACf,YAAY,EAAA;EZEpB;IYEQ,cAAc;IACd,iBAAiB;IACjB,UAAU;IACV,WAAW,EAAA;EA3HnB;;;IAiIQ,WAAW;IACX,YAAY;IACZ,mBAAmB,EAAA;EAnI3B;IAuIQ,YAAY,EAAA;IAvIpB;MA0IY,kBAAkB,EAAA;EA1I9B;IA+IQ,kBAAkB;IAClB,eAAe;IACf,YAAY,EAAA;EAjJpB;IAqJQ,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,kBAAkB;IAClB,yBAAyB,EAAA;EA1JjC;IA+JY,WAAW;IACX,qBAAqB;IACrB,gBAAgB;IAChB,cAAc;IACd,cAAc,EAAA;EAnK1B;IAuKY,kBAAkB,EAAA;IAvK9B;MA0KgB,kBAAkB;MAClB,SAAS,EAAA;IA3KzB;;MAgLgB,kBAAkB,EAAA;EAhLlC;IAqLY,kBAAkB,EAAA;IArL9B;MAwLc,kBAAkB;MAClB,WAAW;MACX,mBnBoEyB;MmBnEzB,cnB9HsB,EAAA;MmB7DpC;QA8LgB,UAAU,EAAA;IA9L1B;MAmMgB,eAAe,EAAA;EAnM/B;IAwMc,gBAAgB;IAChB,eAAe;IACf,6BAA6B;IAC7B,gBAAgB;IAChB,SAAS,EAAA;EA5MvB;IAqNkB,eAAe;IACf,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,UAAU;IACV,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,gBAAgB;IAChB,UAAU,EAAA;IAjO5B;MAoOqB,gBAAgB,EAAA;EApOrC;;IA0OgB,6BAA6B,EAAA;EA1O7C;IAgPoB,qCAAqC,EAAA;EAhPzD;;IAuPY,qBAAqB;IACrB,yBAAyB,EAAA;EAxPrC;IA8PQ,UAAU;IACV,cAAc;IACd,UAAU;IACV,WAAW;IACX,gBAAgB,EAAA;EAlQxB;IAuQY,iBAAiB,EAAA;EAvQ7B;IA2QY,gBAAgB,EAAA;EA3Q5B;IAiRU,qBAAqB,EAAA;IAjR/B;MAoRa,0CAA0C;MAC1C,0CAAuC;MACvC,uCAAoC;MACpC,YAAY;MACZ,aAAa,EAAA;IAxR1B;MA4Ra,0CAA0C;MAC1C,+BAA+B;MAC/B,uCAAoC;MACpC,YAAY;MACZ,aAAa,EAAA;IAhS1B;MAqSa,WAAW;MACX,qBAAqB;MACrB,kBAAkB;MAClB,WAAW;MACX,4BAA4B;MAC5B,oCAAoC;MACpC,iCAAiC,EAAA;EA3S9C;IAiTU,8CAA6C;IAC7C,uBAAsB;IACtB,iBAAgB,EAAA;EAnT1B;IAyTQ,SAAQ,EAAA;;AAIhB;EjB3OQ,+CAA6C,EAAA;EiB2OrD;IAOQ,UAAU,EAAA;;AAPlB;EjB3OQ,8CAA6C,EAAA;;AiB2OrD;EjB3OQ,8CAA6C,EAAA;;AiB2OrD;EjB3OQ,8CAA6C,EAAA;;AkBlFrD;EACE,mBpB4JkC;EoB3JlC,+CAA+C;EAC/C,yBAAyB;EACzB,cpB4JiC;EoB3JjC,mBAAmB;EACnB,kBAAkB;EAClB,cAAc;EAMd,gFAAgF,EAAA;EAblF;IAgBQ,4BAA4B,EAAA;IAhBpC;MAmBY,eAAe;MACf,gBAAgB,EAAA;EApB5B;IA4BM,oBAAoB;IACpB,SAAS,EAAA;IA7Bf;MA0BQ,6BAA6B,EAAA;IA1BrC;MAgCU,gBAAgB,EAAA;EAhC1B;IAqCQ,kBpBoH2B,EAAA;IoBzJnC;MAwCU,aAAa,EAAA;EAxCvB;IA6CQ,yBpByB4B,EAAA;IoBtEpC;MAgDY,yBpBsBwB,EAAA;IoBtEpC;MAqDgB,cpBvCoB,EAAA;EoBdpC;IA2DQ,yBpBuB4B,EAAA;EoBlFpC;IA+DQ,yBpBgB4B,EAAA;EoB/EpC;IAmEQ,yBpBS4B,EAAA;EoB5EpC;IAuEQ,yBpBE4B,EAAA;EoBzEpC;IA2EQ,gBAAgB;IAChB,aAAa;IACb,kBAAkB,EAAA;EA7E1B;IAiFQ,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB,EAAA;EArF3B;IAyFM,cAAc,EAAA;EAzFpB;IA6FM,eAAe;IACf,kBAAkB;IAClB,gBAAgB;IAChB,oBAAoB,EAAA;EAhG1B;IAoGQ,mBpB2J+B;IoB1J/B,kBAAkB;IAClB,cpBxE4B,EAAA;EoB9BpC;IA0GQ,6BAA6B;IAC7B,SAAS,EAAA;IA3GjB;MAgHgB,iBAAiB;MACjB,kBAAkB;MAClB,QAAQ;MACR,cpBtDoB,EAAA;IoB7DpC;MAwHY,SAAS,EAAA;EAxHrB;IA6HQ,6BAA6B;IAC7B,gBAAgB;IAChB,gBAAgB,EAAA;IA/HxB;MAmIY,iBAAiB;MACjB,kBAAkB,EAAA;IApI9B;MAwIY,mBpBqBwB,EAAA;;AqB5JpC;EACI,uBAAuB;EACvB,gBAAgB,EAAA;EAFpB;;IAMQ,cAAc;IACd,eAAe;IACf,6BAA6B,EAAA;EARrC;IAYQ,eAAe;IACf,gBAAgB,EAAA;;ACdxB;EAGM,gBAAgB;EAChB,gBAAgB,EAAA;;AAJtB;EAOM,kBAAkB,EAAA;;AAPxB;EAYI,gBAAgB,EAAA;EAZpB;IAeM,gBAAgB;IAChB,gCAAgC,EAAA;;AAhBtC;EAqBI,gBAAgB,EAAA;;AArBpB;EAyBI,aAAa;EACb,wBAAwB;EACxB,kBAAkB;EAClB,mBAAmB,EAAA;;AA5BvB;EA+BI,gBAAgB,EAAA;EA/BpB;IAkCM,ctBJ8B,EAAA;;AsB9BpC;EAuCI,kBAAkB;EAClB,WAAW;EACX,SAAS,EAAA;EAzCb;IA4CM,SAAS,EAAA;;AC5Cf;EAEQ,aAAa,EAAA;EAFrB;IAKQ,mBAAmB,EAAA;;AAL3B;EAUQ,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB,EAAA;EAZzB;IAeY,gBAAgB,EAAA;;AAf5B;EAoBQ,YAAY;EACZ,aAAa;EACb,yBvBR4B;EuBS5B,kBAAkB,EAAA;;AAvB1B;EA2BQ,iBAAiB,EAAA;;AA3BzB;EA+BQ,qBAAqB,EAAA;;AA/B7B;EAmCM,cAAc,EAAA;;AAnCpB;EAwCQ,iBAAiB;EACjB,gBAAgB,EAAA;;AAzCxB;EA8CQ,kBAAkB;EAClB,kBAAkB,EAAA;;AC/C1B;EACI,aAAa,EAAA;;ACMjB;EAEQ,sBAAsB,EAAA;EAF9B;IAKU,iBAAiB;IACjB,cAAc,EAAA;IANxB;MASgB,gBAAgB,EAAA;IAThC;MAYc,czBWsB;MyBVtB,eAAe;MACf,kBAAkB,EAAA;;AAdhC;EAmBQ,sBAAsB,EAAA;EAnB9B;IAsBU,czBC0B,EAAA;EyBvBpC;IA0BU,gBAAgB;IAChB,mBAAmB,EAAA;;AA3B7B;EA+BQ,cAAc;EACd,gBAAgB,EAAA;EAhCxB;IAmCY,iBAAiB,EAAA;;AC1C7B;EjBAA;IiBGI,UAAU,EAAA;IjBHd;MiBMM,cAAc,EAAA;IjBNpB;MiBUM,eAAe;MACf,eAAe,EAAA;EAIjB;IACI,iBAAiB,EAAA;EAGrB;IACI,aAAa,EAAA;EAGjB;IACI,aAAa,EAAA;EAGjB;IAEQ,mBAAmB;IACnB,kBAAkB,EAAA;EAH1B;IAQQ,SAAS;IACT,eAAe,EAAA;EATvB;IAeU,gBAAgB,EAAA;EAf1B;IAkBY,mBAAmB,EAAA;EAlB/B;IAuBQ,cAAc,EAAA;EAvBtB;IA2BQ,aAAa,EAAA;EA3BrB;;IAgCQ,SAAS;IACT,gBAAgB;IAChB,wBAAwB;IACxB,WAAW;IACX,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAChB,kBAAkB,EAAA;IAxC1B;;MA2CY,aAAa,EAAA;EA3CzB;;IAiDQ,c1B9DwB,EAAA;E0BahC;;IAsDQ,c1BpBwB,EAAA;E0BlChC;IA0DQ,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,mB1B7BwB,EAAA;I0BlChC;MAkEY,eAAe,EAAA;IAlE3B;MAsEY,eAAe,EAAA;IAtE3B;MA0EY,WAAW;MACX,4BAA4B,EAAA;EA3ExC;IAgFQ,yB1B9CwB,EAAA;E0BlChC;IAoFQ,WAAW,EAAA;IApFnB;MAuFY,eAAe,EAAA;ETlH/B;IfUI,0DwB+GqE,EAAA;EAGrE;IAEQ,QAAQ;IxB5DZ,mCAAoC,EAAA;EwB0DxC;IxB1DI,iCAAoC,EAAA;EwB0DxC;IAWQ,kBAAkB;IAClB,kBAAkB,EAAA;EAZ1B;IxB1DI,oCAAoC,EAAA;EwB0DxC;;IxB1DI,iCAAoC,EAAA;EwB0DxC;IxB1DI,oCAAoC,EAAA;EwB0DxC;IA8BY,YAAY;IACZ,UAAU,EAAA;EAKtB;IAEQ,UAAU;IACV,QAAO;IxBjGX,mCAAoC,EAAA;EwBsGxC;;;IAGE,8BAA8B,EAAA;EAEhC;IACE,QAAQ;IxBzJT,8CAAwC;IAExC,+BAAyB;IACzB,qCAAqC;IAErC,6BAA6B,EAAA;EwBuJ9B;IACE,UAAU,EAAA;EAEZ;IACE,WAAW;IxBhKZ,iDAAwC;IAExC,kCAAyB;IACzB,qCAAqC;IAErC,6BAA6B,EAAA;EwB8J9B;IACE,QAAQ;IxBpKT,2CAAwC;IAExC,4BAAyB;IACzB,qCAAqC;IAErC,6BAA6B,EAAA;EwBkK9B;IACE,UAAU,EAAA;EAEZ;IACE,WAAW;IxB3KZ,8CAAwC;IAExC,+BAAyB;IACzB,qCAAqC;IAErC,6BAA6B,EAAA;EAoE9B;IACE;MAAI,QAAQ;MAAE,uBAAuB,EAAA;IACrC;MAAK,QAAQ;MAAE,yBAAyB,EAAA;IACxC;MAAK,yBAAyB,EAAA;IAC9B;MAAM,yBAAyB,EAAA,EAAA;EAEjC;IACE;MAAI,QAAQ;MAAE,+BAA+B,EAAA;IAC7C;MAAK,QAAQ;MAAE,iCAAiC,EAAA;IAChD;MAAK,iCAAiC,EAAA;IACtC;MAAO,iCAAiC,EAAA,EAAA;EAY1C;IACE;MAAK,QAAQ;MAAE,yBAAyB,EAAA;IACxC;MAAM,yBAAyB,EAAA;IAC/B;MAAM,uBAAuB,EAAA;IAC7B;MAAO,QAAQ;MAAE,oBAAoB,EAAA,EAAA;EAGvC;IACE;MAAK,QAAQ;MAAE,iCAAiC,EAAA;IAChD;MAAM,iCAAiC,EAAA;IACvC;MAAM,+BAA+B,EAAA;IACrC;MAAO,QAAQ;MAAE,4BAA4B,EAAA,EAAA;EAY/C;IACE;MAAI,WAAW;MAAE,uBAAuB,EAAA;IACxC;MAAK,WAAW;MAAE,0BAA0B,EAAA;IAC5C;MAAK,0BAA0B,EAAA;IAC/B;MAAM,0BAA0B,EAAA,EAAA;EAElC;IACE;MAAI,WAAW;MAAE,+BAA+B,EAAA;IAChD;MAAK,WAAW;MAAE,kCAAkC,EAAA;IACpD;MAAK,kCAAkC,EAAA;IACvC;MAAM,kCAAkC,EAAA,EAAA;EAW1C;IACE;MAAK,WAAW;MAAC,0BAA0B,EAAA;IAC3C;MAAM,wBAAwB,EAAA;IAC9B;MAAM,wBAAwB,EAAA;IAC9B;MAAO,WAAW;MAAC,oBAAoB,EAAA,EAAA;EAEzC;IACE;MAAI,WAAW;MAAC,kCAAkC,EAAA;IAClD;MAAK,gCAAgC,EAAA;IACrC;MAAK,gCAAgC,EAAA;IACrC;MAAM,WAAW;MAAC,4BAA4B,EAAA,EAAA;EwB+BhD;IACE;MAAI,UAAU,EAAA;IACd;MAAM,UAAU,EAAA,EAAA;EAMlB;IACE;MAAI,UAAU,EAAA;IACd;MAAM,UAAU,EAAA,EAAA;EAGlB;IACI,YAAY;IACZ,WAAW;IACX,eAAe;IACf,UAAU;IACV,MAAM;IACN,QAAQ;IACR,WAAW;IACX,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,6BAA6B;IxBvNjC,0DwBwNqE,EAAA;ERlOzE;IQuOY,iBAAiB,EAAA;EAIzB;IACI,gBAAgB,EAAA;EAGpB;;IAIY,WAAW,EAAA;EAKvB;;IAEI,eAAe;IACf,cAAc;IACd,MAAM;IACN,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,OAAO;IACP,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,UAAU;IxB1Pd,0DwB2PqE;IxBnMjE,oCAAoC,EAAA;EesO5C;IS3BM,WAAW,EAAA;EAGb;IAEM,mBAAmB,EAAA;EAFzB;IAMM,mBAAmB,EAAA;EANzB;IAUM,uBAAuB;IACvB,qBAAqB,EAAA;IAX3B;MAcU,+BAA+B;MAC/B,mCAAmC;MACnC,sBAAsB;MACtB,sBAAsB,EAAA;IAjBhC;MAqBU,+BAA+B;MAC/B,mCAAmC;MACnC,sBAAsB;MACtB,sBAAsB,EAAA,EACzB;;AAKX;EACE;IACE,gBAAgB,EAAA,EACjB;;AAGH;EACE;IAEI,kBAAkB,EAAA;EAFtB;IAMI,mBAAmB,EAAA,EACpB;;AAIL;EACI;IACI,2BAA2B,EAAA;EAG/B;IACI,aAAa,EAAA;EAGjB;IAGY,UAAU;IACV,kB1BtIoB,EAAA;E0BoChC;IAwGI,kBAAkB,EAAA;EdpP1B;IcwPQ,cAAc,EAAA;ERvVtB;IQ4VY,YAAY;IACZ,mBAAmB,EAAA;EAI3B;;IAMU,gC1B1UsB;I0B2UtB,kCAAkC;IAClC,qCAAqC;IACrC,WAAW;IACX,qBAAqB;IACrB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,iCAAiC,EAAA;EAf3C;;IAmBU,gC1BhTsB;I0BiTtB,kCAAkC;IAClC,qCAAqC;IACrC,WAAW;IACX,qBAAqB;IACrB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,iCAAiC,EAAA;EA5B3C;IAmCY,MAAM,EAAA,EACP;;AASf;EACE;IACE,aAAa,EAAA;EAGf;IACE,kBAAkB;IAClB,mBAAmB,EAAA;ERrZvB;IQ0ZY,cAAc;IACd,kBAAkB;IAClB,WAAW,EAAA;EAInB;IACI,cAAc;IACd,mBAAmB,EAAA;EAGvB;IAGM,gBAAgB,EAAA,EACjB;;AAKT;EA/HE;IAiIE,eAAe,EAAA;EAGjB;IACE,4BAA4B,EAAA;IAD9B;MAII,sBAAsB,EAAA;EAI1B;;;IAII,mBAAmB,EAAA;EAIvB;;IAGI,yBAAyB,EAAA;EAjJ7B;IAqJI,8BAA8B,EAAA;EAIlC;IAEI,gBAAgB,EAAA;EAFpB;IAKI,kBAAkB,EAAA;EALtB;IAQI,aAAa,EAAA,EACd;;AAKL;EACE;IAGM,uBAAuB,EAAA;IAH7B;MAMQ,8CAA2C,EAAA;IANnD;MAWQ,wBAAwB,EAAA;EAMhC;IAEI,6BAA6B,EAAA,EAC9B;;AAML;EACI;IACI,cAAc;IACd,eAAe,EAAA;EAGnB;IACE,sBAAsB,EAAA;IADxB;MAII,mBAAmB,EAAA;ERjgB3B;IQugBY,kBAAkB,EAAA;EAI1B;IAGY,eAAe,EAAA;IAH3B;MAMgB,eAAe,EAAA;EAM/B;IAEQ,UAAU,EAAA;EAIlB;IAEI,UAAU;IACV,aAAa,EAAA,EACd", "file": "paper-dashboard.css", "sourcesContent": ["/*!\n\n =========================================================\n * Paper Dashboard 2 - v2.0.1\n =========================================================\n\n * Product Page: https://www.creative-tim.com/product/paper-dashboard-2\n * Copyright 2020 Creative Tim (http://www.creative-tim.com)\n\n * Coded by www.creative-tim.com\n\n =========================================================\n\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\n */\n\n@import 'paper-dashboard/variables';\n@import 'paper-dashboard/mixins';\n\n// Plugins CSS\n@import \"paper-dashboard/plugins/plugin-animate-bootstrap-notify\";\n@import \"paper-dashboard/plugins/plugin-perfect-scrollbar\";\n\n// Core CSS\n@import \"paper-dashboard/buttons\";\n@import \"paper-dashboard/inputs\";\n@import \"paper-dashboard/typography\";\n@import \"paper-dashboard/misc\";\n@import \"paper-dashboard/checkboxes-radio\";\n\n\n// components\n@import \"paper-dashboard/navbar\";\n@import \"paper-dashboard/page-header\";\n@import \"paper-dashboard/dropdown\";\n@import \"paper-dashboard/alerts\";\n@import \"paper-dashboard/images\";\n@import \"paper-dashboard/nucleo-outline\";\n@import \"paper-dashboard/tables\";\n@import \"paper-dashboard/sidebar-and-main-panel\";\n@import \"paper-dashboard/footers\";\n@import \"paper-dashboard/fixed-plugin\";\n\n// cards\n@import \"paper-dashboard/cards\";\n@import \"paper-dashboard/cards/card-plain\";\n@import \"paper-dashboard/cards/card-chart\";\n@import \"paper-dashboard/cards/card-user\";\n@import \"paper-dashboard/cards/card-map\";\n@import \"paper-dashboard/cards/card-stats\";\n\n@import \"paper-dashboard/responsive\";\n", "/*!\n\n =========================================================\n * Paper Dashboard 2 - v2.0.1\n =========================================================\n\n * Product Page: https://www.creative-tim.com/product/paper-dashboard-2\n * Copyright 2020 Creative Tim (http://www.creative-tim.com)\n\n * Coded by www.creative-tim.com\n\n =========================================================\n\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\n */\n/*!\nAnimate.css - http://daneden.me/animate\nLicensed under the MIT license - http://opensource.org/licenses/MIT\n\nCopyright (c) 2015 <PERSON> Eden\n*/\n.animated {\n  -webkit-animation-duration: 1s;\n  animation-duration: 1s;\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both; }\n\n.animated.infinite {\n  -webkit-animation-iteration-count: infinite;\n  animation-iteration-count: infinite; }\n\n.animated.hinge {\n  -webkit-animation-duration: 2s;\n  animation-duration: 2s; }\n\n.animated.bounceIn,\n.animated.bounceOut {\n  -webkit-animation-duration: .75s;\n  animation-duration: .75s; }\n\n.animated.flipOutX,\n.animated.flipOutY {\n  -webkit-animation-duration: .75s;\n  animation-duration: .75s; }\n\n@-webkit-keyframes shake {\n  from, to {\n    -webkit-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0); }\n  10%, 30%, 50%, 70%, 90% {\n    -webkit-transform: translate3d(-10px, 0, 0);\n    transform: translate3d(-10px, 0, 0); }\n  20%, 40%, 60%, 80% {\n    -webkit-transform: translate3d(10px, 0, 0);\n    transform: translate3d(10px, 0, 0); } }\n\n@keyframes shake {\n  from, to {\n    -webkit-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0); }\n  10%, 30%, 50%, 70%, 90% {\n    -webkit-transform: translate3d(-10px, 0, 0);\n    transform: translate3d(-10px, 0, 0); }\n  20%, 40%, 60%, 80% {\n    -webkit-transform: translate3d(10px, 0, 0);\n    transform: translate3d(10px, 0, 0); } }\n\n.shake {\n  -webkit-animation-name: shake;\n  animation-name: shake; }\n\n@-webkit-keyframes fadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0); }\n  to {\n    opacity: 1;\n    -webkit-transform: none;\n    transform: none; } }\n\n@keyframes fadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0); }\n  to {\n    opacity: 1;\n    -webkit-transform: none;\n    transform: none; } }\n\n.fadeInDown {\n  -webkit-animation-name: fadeInDown;\n  animation-name: fadeInDown; }\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0; } }\n\n@keyframes fadeOut {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0; } }\n\n.fadeOut {\n  -webkit-animation-name: fadeOut;\n  animation-name: fadeOut; }\n\n@-webkit-keyframes fadeOutDown {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, 100%, 0);\n    transform: translate3d(0, 100%, 0); } }\n\n@keyframes fadeOutDown {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, 100%, 0);\n    transform: translate3d(0, 100%, 0); } }\n\n.fadeOutDown {\n  -webkit-animation-name: fadeOutDown;\n  animation-name: fadeOutDown; }\n\n@-webkit-keyframes fadeOutUp {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0); } }\n\n@keyframes fadeOutUp {\n  from {\n    opacity: 1; }\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0); } }\n\n.fadeOutUp {\n  -webkit-animation-name: fadeOutUp;\n  animation-name: fadeOutUp; }\n\n/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto; }\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute; }\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute; }\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent; }\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6; }\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9; }\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute; }\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute; }\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px; }\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px; }\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important; } }\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important; } }\n\n.btn,\n.navbar .navbar-nav > a.btn {\n  border-width: 2px;\n  font-weight: 600;\n  font-size: 0.8571em;\n  line-height: 1.35em;\n  text-transform: uppercase;\n  border: none;\n  margin: 10px 1px;\n  border-radius: 3px;\n  padding: 11px 22px;\n  cursor: pointer;\n  background-color: #66615B;\n  color: #FFFFFF;\n  -webkit-transition: all 150ms linear;\n  -moz-transition: all 150ms linear;\n  -o-transition: all 150ms linear;\n  -ms-transition: all 150ms linear;\n  transition: all 150ms linear; }\n  .btn:hover, .btn:focus, .btn:active, .btn.active, .btn:active:focus, .btn:active:hover, .btn.active:focus, .btn.active:hover,\n  .show > .btn.dropdown-toggle,\n  .show > .btn.dropdown-toggle:focus,\n  .show > .btn.dropdown-toggle:hover,\n  .navbar .navbar-nav > a.btn:hover,\n  .navbar .navbar-nav > a.btn:focus,\n  .navbar .navbar-nav > a.btn:active,\n  .navbar .navbar-nav > a.btn.active,\n  .navbar .navbar-nav > a.btn:active:focus,\n  .navbar .navbar-nav > a.btn:active:hover,\n  .navbar .navbar-nav > a.btn.active:focus,\n  .navbar .navbar-nav > a.btn.active:hover,\n  .show >\n  .navbar .navbar-nav > a.btn.dropdown-toggle,\n  .show >\n  .navbar .navbar-nav > a.btn.dropdown-toggle:focus,\n  .show >\n  .navbar .navbar-nav > a.btn.dropdown-toggle:hover {\n    background-color: #403D39 !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn:not([data-action]):hover,\n  .navbar .navbar-nav > a.btn:not([data-action]):hover {\n    box-shadow: none; }\n  .btn.disabled, .btn.disabled:hover, .btn.disabled:focus, .btn.disabled.focus, .btn.disabled:active, .btn.disabled.active, .btn:disabled, .btn:disabled:hover, .btn:disabled:focus, .btn:disabled.focus, .btn:disabled:active, .btn:disabled.active, .btn[disabled], .btn[disabled]:hover, .btn[disabled]:focus, .btn[disabled].focus, .btn[disabled]:active, .btn[disabled].active,\n  fieldset[disabled] .btn,\n  fieldset[disabled] .btn:hover,\n  fieldset[disabled] .btn:focus,\n  fieldset[disabled] .btn.focus,\n  fieldset[disabled] .btn:active,\n  fieldset[disabled] .btn.active,\n  .navbar .navbar-nav > a.btn.disabled,\n  .navbar .navbar-nav > a.btn.disabled:hover,\n  .navbar .navbar-nav > a.btn.disabled:focus,\n  .navbar .navbar-nav > a.btn.disabled.focus,\n  .navbar .navbar-nav > a.btn.disabled:active,\n  .navbar .navbar-nav > a.btn.disabled.active,\n  .navbar .navbar-nav > a.btn:disabled,\n  .navbar .navbar-nav > a.btn:disabled:hover,\n  .navbar .navbar-nav > a.btn:disabled:focus,\n  .navbar .navbar-nav > a.btn:disabled.focus,\n  .navbar .navbar-nav > a.btn:disabled:active,\n  .navbar .navbar-nav > a.btn:disabled.active,\n  .navbar .navbar-nav > a.btn[disabled],\n  .navbar .navbar-nav > a.btn[disabled]:hover,\n  .navbar .navbar-nav > a.btn[disabled]:focus,\n  .navbar .navbar-nav > a.btn[disabled].focus,\n  .navbar .navbar-nav > a.btn[disabled]:active,\n  .navbar .navbar-nav > a.btn[disabled].active,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn:hover,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn:focus,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn.focus,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn:active,\n  fieldset[disabled]\n  .navbar .navbar-nav > a.btn.active {\n    background-color: #66615B;\n    border-color: #66615B; }\n  .btn.btn-simple,\n  .navbar .navbar-nav > a.btn.btn-simple {\n    color: #66615B;\n    border-color: #66615B; }\n    .btn.btn-simple:hover, .btn.btn-simple:focus, .btn.btn-simple:active,\n    .navbar .navbar-nav > a.btn.btn-simple:hover,\n    .navbar .navbar-nav > a.btn.btn-simple:focus,\n    .navbar .navbar-nav > a.btn.btn-simple:active {\n      background-color: transparent;\n      color: #403D39;\n      border-color: #403D39;\n      box-shadow: none; }\n  .btn.btn-link,\n  .navbar .navbar-nav > a.btn.btn-link {\n    color: #66615B; }\n    .btn.btn-link:hover, .btn.btn-link:focus, .btn.btn-link:active, .btn.btn-link:active:focus,\n    .navbar .navbar-nav > a.btn.btn-link:hover,\n    .navbar .navbar-nav > a.btn.btn-link:focus,\n    .navbar .navbar-nav > a.btn.btn-link:active,\n    .navbar .navbar-nav > a.btn.btn-link:active:focus {\n      background-color: transparent;\n      color: #403D39;\n      text-decoration: none;\n      box-shadow: none; }\n  .btn:hover, .btn:focus,\n  .navbar .navbar-nav > a.btn:hover,\n  .navbar .navbar-nav > a.btn:focus {\n    opacity: 1;\n    filter: alpha(opacity=100);\n    outline: 0 !important; }\n  .btn:active, .btn.active,\n  .open > .btn.dropdown-toggle,\n  .navbar .navbar-nav > a.btn:active,\n  .navbar .navbar-nav > a.btn.active,\n  .open >\n  .navbar .navbar-nav > a.btn.dropdown-toggle {\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    outline: 0 !important; }\n  .btn .badge,\n  .navbar .navbar-nav > a.btn .badge {\n    margin: 0; }\n  .btn.btn-icon,\n  .navbar .navbar-nav > a.btn.btn-icon {\n    height: 2.375rem;\n    min-width: 2.375rem;\n    width: 2.375rem;\n    padding: 0;\n    font-size: 0.9375rem;\n    overflow: hidden;\n    position: relative;\n    line-height: normal; }\n    .btn.btn-icon.btn-simple,\n    .navbar .navbar-nav > a.btn.btn-icon.btn-simple {\n      padding: 0; }\n    .btn.btn-icon.btn-sm,\n    .navbar .navbar-nav > a.btn.btn-icon.btn-sm {\n      height: 1.875rem;\n      min-width: 1.875rem;\n      width: 1.875rem; }\n      .btn.btn-icon.btn-sm .fa,\n      .btn.btn-icon.btn-sm .far,\n      .btn.btn-icon.btn-sm .fas,\n      .btn.btn-icon.btn-sm .nc-icon,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-sm .fa,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-sm .far,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-sm .fas,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-sm .nc-icon {\n        font-size: 0.6875rem; }\n    .btn.btn-icon.btn-lg,\n    .navbar .navbar-nav > a.btn.btn-icon.btn-lg {\n      height: 3.6rem;\n      min-width: 3.6rem;\n      width: 3.6rem; }\n      .btn.btn-icon.btn-lg .fa,\n      .btn.btn-icon.btn-lg .far,\n      .btn.btn-icon.btn-lg .fas,\n      .btn.btn-icon.btn-lg .nc-icon,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-lg .fa,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-lg .far,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-lg .fas,\n      .navbar .navbar-nav > a.btn.btn-icon.btn-lg .nc-icon {\n        font-size: 1.325rem; }\n    .btn.btn-icon:not(.btn-footer) .nc-icon,\n    .btn.btn-icon:not(.btn-footer) .fa,\n    .btn.btn-icon:not(.btn-footer) .far,\n    .btn.btn-icon:not(.btn-footer) .fas,\n    .navbar .navbar-nav > a.btn.btn-icon:not(.btn-footer) .nc-icon,\n    .navbar .navbar-nav > a.btn.btn-icon:not(.btn-footer) .fa,\n    .navbar .navbar-nav > a.btn.btn-icon:not(.btn-footer) .far,\n    .navbar .navbar-nav > a.btn.btn-icon:not(.btn-footer) .fas {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-12px, -12px);\n      line-height: 1.5626rem;\n      width: 24px; }\n    .btn.btn-icon.btn-neutral,\n    .navbar .navbar-nav > a.btn.btn-icon.btn-neutral {\n      font-size: 20px; }\n  .btn:not(.btn-icon) .nc-icon,\n  .navbar .navbar-nav > a.btn:not(.btn-icon) .nc-icon {\n    position: relative;\n    top: 1px; }\n\n.btn-primary {\n  background-color: #51cbce;\n  color: #FFFFFF; }\n  .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary:active:focus, .btn-primary:active:hover, .btn-primary.active:focus, .btn-primary.active:hover,\n  .show > .btn-primary.dropdown-toggle,\n  .show > .btn-primary.dropdown-toggle:focus,\n  .show > .btn-primary.dropdown-toggle:hover {\n    background-color: #34b5b8 !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-primary:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary:disabled, .btn-primary:disabled:hover, .btn-primary:disabled:focus, .btn-primary:disabled.focus, .btn-primary:disabled:active, .btn-primary:disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, .btn-primary[disabled]:active, .btn-primary[disabled].active,\n  fieldset[disabled] .btn-primary,\n  fieldset[disabled] .btn-primary:hover,\n  fieldset[disabled] .btn-primary:focus,\n  fieldset[disabled] .btn-primary.focus,\n  fieldset[disabled] .btn-primary:active,\n  fieldset[disabled] .btn-primary.active {\n    background-color: #51cbce;\n    border-color: #51cbce; }\n  .btn-primary.btn-simple {\n    color: #51cbce;\n    border-color: #51cbce; }\n    .btn-primary.btn-simple:hover, .btn-primary.btn-simple:focus, .btn-primary.btn-simple:active {\n      background-color: transparent;\n      color: #34b5b8;\n      border-color: #34b5b8;\n      box-shadow: none; }\n  .btn-primary.btn-link {\n    color: #51cbce; }\n    .btn-primary.btn-link:hover, .btn-primary.btn-link:focus, .btn-primary.btn-link:active, .btn-primary.btn-link:active:focus {\n      background-color: transparent;\n      color: #34b5b8;\n      text-decoration: none;\n      box-shadow: none; }\n\n.btn-success {\n  background-color: #6bd098;\n  color: #FFFFFF; }\n  .btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success:active:focus, .btn-success:active:hover, .btn-success.active:focus, .btn-success.active:hover,\n  .show > .btn-success.dropdown-toggle,\n  .show > .btn-success.dropdown-toggle:focus,\n  .show > .btn-success.dropdown-toggle:hover {\n    background-color: #44c47d !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-success:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success:disabled, .btn-success:disabled:hover, .btn-success:disabled:focus, .btn-success:disabled.focus, .btn-success:disabled:active, .btn-success:disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, .btn-success[disabled]:active, .btn-success[disabled].active,\n  fieldset[disabled] .btn-success,\n  fieldset[disabled] .btn-success:hover,\n  fieldset[disabled] .btn-success:focus,\n  fieldset[disabled] .btn-success.focus,\n  fieldset[disabled] .btn-success:active,\n  fieldset[disabled] .btn-success.active {\n    background-color: #6bd098;\n    border-color: #6bd098; }\n  .btn-success.btn-simple {\n    color: #6bd098;\n    border-color: #6bd098; }\n    .btn-success.btn-simple:hover, .btn-success.btn-simple:focus, .btn-success.btn-simple:active {\n      background-color: transparent;\n      color: #44c47d;\n      border-color: #44c47d;\n      box-shadow: none; }\n  .btn-success.btn-link {\n    color: #6bd098; }\n    .btn-success.btn-link:hover, .btn-success.btn-link:focus, .btn-success.btn-link:active, .btn-success.btn-link:active:focus {\n      background-color: transparent;\n      color: #44c47d;\n      text-decoration: none;\n      box-shadow: none; }\n\n.btn-info {\n  background-color: #51bcda;\n  color: #FFFFFF; }\n  .btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info:active:focus, .btn-info:active:hover, .btn-info.active:focus, .btn-info.active:hover,\n  .show > .btn-info.dropdown-toggle,\n  .show > .btn-info.dropdown-toggle:focus,\n  .show > .btn-info.dropdown-toggle:hover {\n    background-color: #2ba9cd !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-info:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info:disabled, .btn-info:disabled:hover, .btn-info:disabled:focus, .btn-info:disabled.focus, .btn-info:disabled:active, .btn-info:disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, .btn-info[disabled]:active, .btn-info[disabled].active,\n  fieldset[disabled] .btn-info,\n  fieldset[disabled] .btn-info:hover,\n  fieldset[disabled] .btn-info:focus,\n  fieldset[disabled] .btn-info.focus,\n  fieldset[disabled] .btn-info:active,\n  fieldset[disabled] .btn-info.active {\n    background-color: #51bcda;\n    border-color: #51bcda; }\n  .btn-info.btn-simple {\n    color: #51bcda;\n    border-color: #51bcda; }\n    .btn-info.btn-simple:hover, .btn-info.btn-simple:focus, .btn-info.btn-simple:active {\n      background-color: transparent;\n      color: #2ba9cd;\n      border-color: #2ba9cd;\n      box-shadow: none; }\n  .btn-info.btn-link {\n    color: #51bcda; }\n    .btn-info.btn-link:hover, .btn-info.btn-link:focus, .btn-info.btn-link:active, .btn-info.btn-link:active:focus {\n      background-color: transparent;\n      color: #2ba9cd;\n      text-decoration: none;\n      box-shadow: none; }\n\n.btn-warning {\n  background-color: #fbc658;\n  color: #FFFFFF; }\n  .btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning:active:focus, .btn-warning:active:hover, .btn-warning.active:focus, .btn-warning.active:hover,\n  .show > .btn-warning.dropdown-toggle,\n  .show > .btn-warning.dropdown-toggle:focus,\n  .show > .btn-warning.dropdown-toggle:hover {\n    background-color: #fab526 !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-warning:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning:disabled, .btn-warning:disabled:hover, .btn-warning:disabled:focus, .btn-warning:disabled.focus, .btn-warning:disabled:active, .btn-warning:disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, .btn-warning[disabled]:active, .btn-warning[disabled].active,\n  fieldset[disabled] .btn-warning,\n  fieldset[disabled] .btn-warning:hover,\n  fieldset[disabled] .btn-warning:focus,\n  fieldset[disabled] .btn-warning.focus,\n  fieldset[disabled] .btn-warning:active,\n  fieldset[disabled] .btn-warning.active {\n    background-color: #fbc658;\n    border-color: #fbc658; }\n  .btn-warning.btn-simple {\n    color: #fbc658;\n    border-color: #fbc658; }\n    .btn-warning.btn-simple:hover, .btn-warning.btn-simple:focus, .btn-warning.btn-simple:active {\n      background-color: transparent;\n      color: #fab526;\n      border-color: #fab526;\n      box-shadow: none; }\n  .btn-warning.btn-link {\n    color: #fbc658; }\n    .btn-warning.btn-link:hover, .btn-warning.btn-link:focus, .btn-warning.btn-link:active, .btn-warning.btn-link:active:focus {\n      background-color: transparent;\n      color: #fab526;\n      text-decoration: none;\n      box-shadow: none; }\n\n.btn-danger {\n  background-color: #ef8157;\n  color: #FFFFFF; }\n  .btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active, .btn-danger:active:focus, .btn-danger:active:hover, .btn-danger.active:focus, .btn-danger.active:hover,\n  .show > .btn-danger.dropdown-toggle,\n  .show > .btn-danger.dropdown-toggle:focus,\n  .show > .btn-danger.dropdown-toggle:hover {\n    background-color: #eb6532 !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-danger:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger:disabled, .btn-danger:disabled:hover, .btn-danger:disabled:focus, .btn-danger:disabled.focus, .btn-danger:disabled:active, .btn-danger:disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, .btn-danger[disabled]:active, .btn-danger[disabled].active,\n  fieldset[disabled] .btn-danger,\n  fieldset[disabled] .btn-danger:hover,\n  fieldset[disabled] .btn-danger:focus,\n  fieldset[disabled] .btn-danger.focus,\n  fieldset[disabled] .btn-danger:active,\n  fieldset[disabled] .btn-danger.active {\n    background-color: #ef8157;\n    border-color: #ef8157; }\n  .btn-danger.btn-simple {\n    color: #ef8157;\n    border-color: #ef8157; }\n    .btn-danger.btn-simple:hover, .btn-danger.btn-simple:focus, .btn-danger.btn-simple:active {\n      background-color: transparent;\n      color: #eb6532;\n      border-color: #eb6532;\n      box-shadow: none; }\n  .btn-danger.btn-link {\n    color: #ef8157; }\n    .btn-danger.btn-link:hover, .btn-danger.btn-link:focus, .btn-danger.btn-link:active, .btn-danger.btn-link:active:focus {\n      background-color: transparent;\n      color: #eb6532;\n      text-decoration: none;\n      box-shadow: none; }\n\n.btn-outline-default {\n  background: transparent;\n  border: 2px solid #66615B !important;\n  color: #66615B;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-default:hover, .btn-outline-default:focus, .btn-outline-default:active, .btn-outline-default:focus:active, .btn-outline-default.active,\n  .open > .btn-outline-default.dropdown-toggle {\n    background-color: #66615B !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #66615B !important; }\n    .btn-outline-default:hover .caret, .btn-outline-default:focus .caret, .btn-outline-default:active .caret, .btn-outline-default:focus:active .caret, .btn-outline-default.active .caret,\n    .open > .btn-outline-default.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-default .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-default.disabled, .btn-outline-default.disabled:hover, .btn-outline-default.disabled:focus, .btn-outline-default.disabled.focus, .btn-outline-default.disabled:active, .btn-outline-default.disabled.active, .btn-outline-default:disabled, .btn-outline-default:disabled:hover, .btn-outline-default:disabled:focus, .btn-outline-default:disabled.focus, .btn-outline-default:disabled:active, .btn-outline-default:disabled.active, .btn-outline-default[disabled], .btn-outline-default[disabled]:hover, .btn-outline-default[disabled]:focus, .btn-outline-default[disabled].focus, .btn-outline-default[disabled]:active, .btn-outline-default[disabled].active,\n  fieldset[disabled] .btn-outline-default,\n  fieldset[disabled] .btn-outline-default:hover,\n  fieldset[disabled] .btn-outline-default:focus,\n  fieldset[disabled] .btn-outline-default.focus,\n  fieldset[disabled] .btn-outline-default:active,\n  fieldset[disabled] .btn-outline-default.active {\n    background-color: transparent !important;\n    border-color: #66615B !important; }\n\n.btn-outline-primary {\n  background: transparent;\n  border: 2px solid #51cbce !important;\n  color: #51cbce;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary:focus:active, .btn-outline-primary.active,\n  .open > .btn-outline-primary.dropdown-toggle {\n    background-color: #51cbce !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #51cbce !important; }\n    .btn-outline-primary:hover .caret, .btn-outline-primary:focus .caret, .btn-outline-primary:active .caret, .btn-outline-primary:focus:active .caret, .btn-outline-primary.active .caret,\n    .open > .btn-outline-primary.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-primary .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-primary.disabled, .btn-outline-primary.disabled:hover, .btn-outline-primary.disabled:focus, .btn-outline-primary.disabled.focus, .btn-outline-primary.disabled:active, .btn-outline-primary.disabled.active, .btn-outline-primary:disabled, .btn-outline-primary:disabled:hover, .btn-outline-primary:disabled:focus, .btn-outline-primary:disabled.focus, .btn-outline-primary:disabled:active, .btn-outline-primary:disabled.active, .btn-outline-primary[disabled], .btn-outline-primary[disabled]:hover, .btn-outline-primary[disabled]:focus, .btn-outline-primary[disabled].focus, .btn-outline-primary[disabled]:active, .btn-outline-primary[disabled].active,\n  fieldset[disabled] .btn-outline-primary,\n  fieldset[disabled] .btn-outline-primary:hover,\n  fieldset[disabled] .btn-outline-primary:focus,\n  fieldset[disabled] .btn-outline-primary.focus,\n  fieldset[disabled] .btn-outline-primary:active,\n  fieldset[disabled] .btn-outline-primary.active {\n    background-color: transparent !important;\n    border-color: #51cbce !important; }\n\n.btn-outline-success {\n  background: transparent;\n  border: 2px solid #6bd098 !important;\n  color: #6bd098;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-success:hover, .btn-outline-success:focus, .btn-outline-success:active, .btn-outline-success:focus:active, .btn-outline-success.active,\n  .open > .btn-outline-success.dropdown-toggle {\n    background-color: #6bd098 !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #6bd098 !important; }\n    .btn-outline-success:hover .caret, .btn-outline-success:focus .caret, .btn-outline-success:active .caret, .btn-outline-success:focus:active .caret, .btn-outline-success.active .caret,\n    .open > .btn-outline-success.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-success .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-success.disabled, .btn-outline-success.disabled:hover, .btn-outline-success.disabled:focus, .btn-outline-success.disabled.focus, .btn-outline-success.disabled:active, .btn-outline-success.disabled.active, .btn-outline-success:disabled, .btn-outline-success:disabled:hover, .btn-outline-success:disabled:focus, .btn-outline-success:disabled.focus, .btn-outline-success:disabled:active, .btn-outline-success:disabled.active, .btn-outline-success[disabled], .btn-outline-success[disabled]:hover, .btn-outline-success[disabled]:focus, .btn-outline-success[disabled].focus, .btn-outline-success[disabled]:active, .btn-outline-success[disabled].active,\n  fieldset[disabled] .btn-outline-success,\n  fieldset[disabled] .btn-outline-success:hover,\n  fieldset[disabled] .btn-outline-success:focus,\n  fieldset[disabled] .btn-outline-success.focus,\n  fieldset[disabled] .btn-outline-success:active,\n  fieldset[disabled] .btn-outline-success.active {\n    background-color: transparent !important;\n    border-color: #6bd098 !important; }\n\n.btn-outline-info {\n  background: transparent;\n  border: 2px solid #51bcda !important;\n  color: #51bcda;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-info:hover, .btn-outline-info:focus, .btn-outline-info:active, .btn-outline-info:focus:active, .btn-outline-info.active,\n  .open > .btn-outline-info.dropdown-toggle {\n    background-color: #51bcda !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #51bcda !important; }\n    .btn-outline-info:hover .caret, .btn-outline-info:focus .caret, .btn-outline-info:active .caret, .btn-outline-info:focus:active .caret, .btn-outline-info.active .caret,\n    .open > .btn-outline-info.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-info .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-info.disabled, .btn-outline-info.disabled:hover, .btn-outline-info.disabled:focus, .btn-outline-info.disabled.focus, .btn-outline-info.disabled:active, .btn-outline-info.disabled.active, .btn-outline-info:disabled, .btn-outline-info:disabled:hover, .btn-outline-info:disabled:focus, .btn-outline-info:disabled.focus, .btn-outline-info:disabled:active, .btn-outline-info:disabled.active, .btn-outline-info[disabled], .btn-outline-info[disabled]:hover, .btn-outline-info[disabled]:focus, .btn-outline-info[disabled].focus, .btn-outline-info[disabled]:active, .btn-outline-info[disabled].active,\n  fieldset[disabled] .btn-outline-info,\n  fieldset[disabled] .btn-outline-info:hover,\n  fieldset[disabled] .btn-outline-info:focus,\n  fieldset[disabled] .btn-outline-info.focus,\n  fieldset[disabled] .btn-outline-info:active,\n  fieldset[disabled] .btn-outline-info.active {\n    background-color: transparent !important;\n    border-color: #51bcda !important; }\n\n.btn-outline-warning {\n  background: transparent;\n  border: 2px solid #fbc658 !important;\n  color: #fbc658;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-warning:hover, .btn-outline-warning:focus, .btn-outline-warning:active, .btn-outline-warning:focus:active, .btn-outline-warning.active,\n  .open > .btn-outline-warning.dropdown-toggle {\n    background-color: #fbc658 !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #fbc658 !important; }\n    .btn-outline-warning:hover .caret, .btn-outline-warning:focus .caret, .btn-outline-warning:active .caret, .btn-outline-warning:focus:active .caret, .btn-outline-warning.active .caret,\n    .open > .btn-outline-warning.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-warning .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-warning.disabled, .btn-outline-warning.disabled:hover, .btn-outline-warning.disabled:focus, .btn-outline-warning.disabled.focus, .btn-outline-warning.disabled:active, .btn-outline-warning.disabled.active, .btn-outline-warning:disabled, .btn-outline-warning:disabled:hover, .btn-outline-warning:disabled:focus, .btn-outline-warning:disabled.focus, .btn-outline-warning:disabled:active, .btn-outline-warning:disabled.active, .btn-outline-warning[disabled], .btn-outline-warning[disabled]:hover, .btn-outline-warning[disabled]:focus, .btn-outline-warning[disabled].focus, .btn-outline-warning[disabled]:active, .btn-outline-warning[disabled].active,\n  fieldset[disabled] .btn-outline-warning,\n  fieldset[disabled] .btn-outline-warning:hover,\n  fieldset[disabled] .btn-outline-warning:focus,\n  fieldset[disabled] .btn-outline-warning.focus,\n  fieldset[disabled] .btn-outline-warning:active,\n  fieldset[disabled] .btn-outline-warning.active {\n    background-color: transparent !important;\n    border-color: #fbc658 !important; }\n\n.btn-outline-danger {\n  background: transparent;\n  border: 2px solid #ef8157 !important;\n  color: #ef8157;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-danger:hover, .btn-outline-danger:focus, .btn-outline-danger:active, .btn-outline-danger:focus:active, .btn-outline-danger.active,\n  .open > .btn-outline-danger.dropdown-toggle {\n    background-color: #ef8157 !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #ef8157 !important; }\n    .btn-outline-danger:hover .caret, .btn-outline-danger:focus .caret, .btn-outline-danger:active .caret, .btn-outline-danger:focus:active .caret, .btn-outline-danger.active .caret,\n    .open > .btn-outline-danger.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-danger .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-danger.disabled, .btn-outline-danger.disabled:hover, .btn-outline-danger.disabled:focus, .btn-outline-danger.disabled.focus, .btn-outline-danger.disabled:active, .btn-outline-danger.disabled.active, .btn-outline-danger:disabled, .btn-outline-danger:disabled:hover, .btn-outline-danger:disabled:focus, .btn-outline-danger:disabled.focus, .btn-outline-danger:disabled:active, .btn-outline-danger:disabled.active, .btn-outline-danger[disabled], .btn-outline-danger[disabled]:hover, .btn-outline-danger[disabled]:focus, .btn-outline-danger[disabled].focus, .btn-outline-danger[disabled]:active, .btn-outline-danger[disabled].active,\n  fieldset[disabled] .btn-outline-danger,\n  fieldset[disabled] .btn-outline-danger:hover,\n  fieldset[disabled] .btn-outline-danger:focus,\n  fieldset[disabled] .btn-outline-danger.focus,\n  fieldset[disabled] .btn-outline-danger:active,\n  fieldset[disabled] .btn-outline-danger.active {\n    background-color: transparent !important;\n    border-color: #ef8157 !important; }\n\n.btn-outline-neutral {\n  background: transparent;\n  border: 2px solid #FFFFFF !important;\n  color: #FFFFFF;\n  opacity: 1;\n  filter: alpha(opacity=100); }\n  .btn-outline-neutral:hover, .btn-outline-neutral:focus, .btn-outline-neutral:active, .btn-outline-neutral:focus:active, .btn-outline-neutral.active,\n  .open > .btn-outline-neutral.dropdown-toggle {\n    background-color: #FFFFFF !important;\n    color: rgba(255, 255, 255, 0.8) !important;\n    border-color: #FFFFFF !important; }\n    .btn-outline-neutral:hover .caret, .btn-outline-neutral:focus .caret, .btn-outline-neutral:active .caret, .btn-outline-neutral:focus:active .caret, .btn-outline-neutral.active .caret,\n    .open > .btn-outline-neutral.dropdown-toggle .caret {\n      border-top-color: rgba(255, 255, 255, 0.8) !important; }\n  .btn-outline-neutral .caret {\n    border-top-color: #FFFFFF !important; }\n  .btn-outline-neutral.disabled, .btn-outline-neutral.disabled:hover, .btn-outline-neutral.disabled:focus, .btn-outline-neutral.disabled.focus, .btn-outline-neutral.disabled:active, .btn-outline-neutral.disabled.active, .btn-outline-neutral:disabled, .btn-outline-neutral:disabled:hover, .btn-outline-neutral:disabled:focus, .btn-outline-neutral:disabled.focus, .btn-outline-neutral:disabled:active, .btn-outline-neutral:disabled.active, .btn-outline-neutral[disabled], .btn-outline-neutral[disabled]:hover, .btn-outline-neutral[disabled]:focus, .btn-outline-neutral[disabled].focus, .btn-outline-neutral[disabled]:active, .btn-outline-neutral[disabled].active,\n  fieldset[disabled] .btn-outline-neutral,\n  fieldset[disabled] .btn-outline-neutral:hover,\n  fieldset[disabled] .btn-outline-neutral:focus,\n  fieldset[disabled] .btn-outline-neutral.focus,\n  fieldset[disabled] .btn-outline-neutral:active,\n  fieldset[disabled] .btn-outline-neutral.active {\n    background-color: transparent !important;\n    border-color: #FFFFFF !important; }\n  .btn-outline-neutral:hover, .btn-outline-neutral:focus {\n    color: #403D39;\n    background-color: #FFFFFF; }\n\n.btn-neutral {\n  background-color: #FFFFFF;\n  color: #51cbce;\n  color: #66615B; }\n  .btn-neutral:hover, .btn-neutral:focus, .btn-neutral:active, .btn-neutral.active, .btn-neutral:active:focus, .btn-neutral:active:hover, .btn-neutral.active:focus, .btn-neutral.active:hover,\n  .show > .btn-neutral.dropdown-toggle,\n  .show > .btn-neutral.dropdown-toggle:focus,\n  .show > .btn-neutral.dropdown-toggle:hover {\n    background-color: #FFFFFF !important;\n    color: #FFFFFF !important;\n    box-shadow: none !important; }\n  .btn-neutral:not([data-action]):hover {\n    box-shadow: none; }\n  .btn-neutral.disabled, .btn-neutral.disabled:hover, .btn-neutral.disabled:focus, .btn-neutral.disabled.focus, .btn-neutral.disabled:active, .btn-neutral.disabled.active, .btn-neutral:disabled, .btn-neutral:disabled:hover, .btn-neutral:disabled:focus, .btn-neutral:disabled.focus, .btn-neutral:disabled:active, .btn-neutral:disabled.active, .btn-neutral[disabled], .btn-neutral[disabled]:hover, .btn-neutral[disabled]:focus, .btn-neutral[disabled].focus, .btn-neutral[disabled]:active, .btn-neutral[disabled].active,\n  fieldset[disabled] .btn-neutral,\n  fieldset[disabled] .btn-neutral:hover,\n  fieldset[disabled] .btn-neutral:focus,\n  fieldset[disabled] .btn-neutral.focus,\n  fieldset[disabled] .btn-neutral:active,\n  fieldset[disabled] .btn-neutral.active {\n    background-color: #FFFFFF;\n    border-color: #FFFFFF; }\n  .btn-neutral.btn-danger {\n    color: #ef8157; }\n    .btn-neutral.btn-danger:hover, .btn-neutral.btn-danger:focus, .btn-neutral.btn-danger:active, .btn-neutral.btn-danger:active:focus {\n      color: #eb6532 !important; }\n  .btn-neutral.btn-info {\n    color: #51bcda !important; }\n    .btn-neutral.btn-info:hover, .btn-neutral.btn-info:focus, .btn-neutral.btn-info:active, .btn-neutral.btn-info:active:focus {\n      color: #2ba9cd !important; }\n  .btn-neutral.btn-warning {\n    color: #fbc658 !important; }\n    .btn-neutral.btn-warning:hover, .btn-neutral.btn-warning:focus, .btn-neutral.btn-warning:active, .btn-neutral.btn-warning:active:focus {\n      color: #fab526 !important; }\n  .btn-neutral.btn-success {\n    color: #6bd098 !important; }\n    .btn-neutral.btn-success:hover, .btn-neutral.btn-success:focus, .btn-neutral.btn-success:active, .btn-neutral.btn-success:active:focus {\n      color: #44c47d !important; }\n  .btn-neutral.btn-default {\n    color: #66615B !important; }\n    .btn-neutral.btn-default:hover, .btn-neutral.btn-default:focus, .btn-neutral.btn-default:active, .btn-neutral.btn-default:active:focus {\n      color: #403D39 !important; }\n  .btn-neutral.active, .btn-neutral:active, .btn-neutral:active:focus, .btn-neutral:active:hover, .btn-neutral.active:focus, .btn-neutral.active:hover,\n  .show > .btn-neutral.dropdown-toggle,\n  .show > .btn-neutral.dropdown-toggle:focus,\n  .show > .btn-neutral.dropdown-toggle:hover {\n    background-color: #FFFFFF !important;\n    color: #34b5b8 !important;\n    box-shadow: none !important; }\n  .btn-neutral:hover, .btn-neutral:focus {\n    color: #34b5b8 !important; }\n    .btn-neutral:hover:not(.nav-link), .btn-neutral:focus:not(.nav-link) {\n      box-shadow: none; }\n  .btn-neutral.btn-simple {\n    color: #FFFFFF;\n    border-color: #FFFFFF; }\n    .btn-neutral.btn-simple:hover, .btn-neutral.btn-simple:focus, .btn-neutral.btn-simple:active {\n      background-color: transparent;\n      color: #FFFFFF;\n      border-color: #FFFFFF;\n      box-shadow: none; }\n  .btn-neutral.btn-link {\n    color: #FFFFFF; }\n    .btn-neutral.btn-link:hover, .btn-neutral.btn-link:focus, .btn-neutral.btn-link:active, .btn-neutral.btn-link:active:focus {\n      background-color: transparent;\n      color: #FFFFFF;\n      text-decoration: none;\n      box-shadow: none; }\n  .btn-neutral:hover, .btn-neutral:focus {\n    color: #403D39; }\n  .btn-neutral.btn-border:hover, .btn-neutral.btn-border:focus {\n    color: #66615B; }\n  .btn-neutral.btn-border:active, .btn-neutral.btn-border.active,\n  .open > .btn-neutral.btn-border.dropdown-toggle {\n    background-color: #FFFFFF;\n    color: #66615B; }\n  .btn-neutral.btn-link:active, .btn-neutral.btn-link.active {\n    background-color: transparent; }\n\n.btn:disabled, .btn[disabled], .btn.disabled {\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n  pointer-events: none; }\n\n.btn-simple {\n  border: 1px solid;\n  border-color: #66615B;\n  padding: 10px 22px;\n  background-color: transparent; }\n\n.btn-simple.disabled, .btn-simple.disabled:hover, .btn-simple.disabled:focus, .btn-simple.disabled.focus, .btn-simple.disabled:active, .btn-simple.disabled.active, .btn-simple:disabled, .btn-simple:disabled:hover, .btn-simple:disabled:focus, .btn-simple:disabled.focus, .btn-simple:disabled:active, .btn-simple:disabled.active, .btn-simple[disabled], .btn-simple[disabled]:hover, .btn-simple[disabled]:focus, .btn-simple[disabled].focus, .btn-simple[disabled]:active, .btn-simple[disabled].active,\nfieldset[disabled] .btn-simple,\nfieldset[disabled] .btn-simple:hover,\nfieldset[disabled] .btn-simple:focus,\nfieldset[disabled] .btn-simple.focus,\nfieldset[disabled] .btn-simple:active,\nfieldset[disabled] .btn-simple.active,\n.btn-link.disabled,\n.btn-link.disabled:hover,\n.btn-link.disabled:focus,\n.btn-link.disabled.focus,\n.btn-link.disabled:active,\n.btn-link.disabled.active,\n.btn-link:disabled,\n.btn-link:disabled:hover,\n.btn-link:disabled:focus,\n.btn-link:disabled.focus,\n.btn-link:disabled:active,\n.btn-link:disabled.active,\n.btn-link[disabled],\n.btn-link[disabled]:hover,\n.btn-link[disabled]:focus,\n.btn-link[disabled].focus,\n.btn-link[disabled]:active,\n.btn-link[disabled].active,\nfieldset[disabled]\n.btn-link,\nfieldset[disabled]\n.btn-link:hover,\nfieldset[disabled]\n.btn-link:focus,\nfieldset[disabled]\n.btn-link.focus,\nfieldset[disabled]\n.btn-link:active,\nfieldset[disabled]\n.btn-link.active {\n  background-color: transparent; }\n\n.btn-link {\n  border: 0;\n  padding: 0.5rem 0.7rem;\n  background-color: transparent; }\n\n.btn-lg {\n  font-size: 1rem;\n  border-radius: 6px;\n  padding: 15px 48px; }\n  .btn-lg.btn-simple {\n    padding: 14px 47px; }\n\n.btn-sm {\n  font-size: 0.8571em;\n  border-radius: 3px;\n  padding: 5px 15px; }\n  .btn-sm.btn-simple {\n    padding: 4px 14px; }\n\n.btn-wd {\n  min-width: 140px; }\n\n.btn-group.select {\n  width: 100%; }\n\n.btn-group.select .btn {\n  text-align: left; }\n\n.btn-group.select .caret {\n  position: absolute;\n  top: 50%;\n  margin-top: -1px;\n  right: 8px; }\n\n.btn-group .btn + .btn {\n  margin-left: -3px; }\n\n.btn-group .btn:focus {\n  background-color: #51bcda !important; }\n\n.btn-round {\n  border-width: 1px;\n  border-radius: 30px;\n  padding-right: 23px;\n  padding-left: 23px; }\n  .btn-round.btn-simple {\n    padding: 10px 22px; }\n\n.no-caret.dropdown-toggle::after {\n  display: none; }\n\n::-moz-placeholder {\n  color: #9A9A9A; }\n\n:-ms-input-placeholder {\n  color: #9A9A9A; }\n\n::-webkit-input-placeholder {\n  color: #9A9A9A; }\n\n.form-control {\n  background-color: #FFFFFF;\n  border: 1px solid #DDDDDD;\n  border-radius: 4px;\n  color: #66615b;\n  line-height: normal;\n  height: auto;\n  font-size: 14px;\n  -webkit-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -moz-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -o-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -ms-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -webkit-box-shadow: none;\n  box-shadow: none; }\n  .form-control:focus {\n    border: 1px solid #9A9A9A;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    outline: 0 !important;\n    color: #66615B; }\n    .form-control:focus + .input-group-append .input-group-text,\n    .form-control:focus ~ .input-group-append .input-group-text,\n    .form-control:focus + .input-group-prepend .input-group-text,\n    .form-control:focus ~ .input-group-prepend .input-group-text {\n      border: 1px solid #ccc;\n      border-left: none;\n      background-color: transparent; }\n  .has-success .form-control,\n  .has-error .form-control,\n  .has-success .form-control:focus,\n  .has-error .form-control:focus {\n    -webkit-box-shadow: none;\n    box-shadow: none; }\n  .has-success .form-control {\n    border: 1px solid #ccc;\n    color: #66615b; }\n    .has-success .form-control.form-control-success {\n      padding-right: 2.5em !important; }\n  .has-success .form-control:focus {\n    border: 1px solid #6bd098;\n    color: #6bd098; }\n  .has-danger .form-control {\n    background-color: #FFC0A4;\n    border: 1px solid #ef8157;\n    color: #ef8157; }\n    .has-danger .form-control.form-control-danger {\n      padding-right: 2.5em !important; }\n  .has-danger .form-control:focus {\n    background-color: #FFFFFF;\n    border: 1px solid #ef8157; }\n  .form-control + .form-control-feedback {\n    border-radius: 6px;\n    font-size: 14px;\n    margin-top: -7px;\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    vertical-align: middle; }\n  .open .form-control {\n    border-radius: 6px 6px 0 0;\n    border-bottom-color: transparent; }\n  .form-control + .input-group-append .input-group-text,\n  .form-control + .input-group-prepend .input-group-text {\n    background-color: #FFFFFF; }\n\n.form-group.no-border.form-control-lg .input-group-append .input-group-text,\n.input-group.no-border.form-control-lg .input-group-append .input-group-text {\n  padding: 15px 0 15px 11px; }\n\n.form-group.no-border.form-control-lg .form-control,\n.input-group.no-border.form-control-lg .form-control {\n  padding: 15px 11px; }\n  .form-group.no-border.form-control-lg .form-control + .input-group-prepend .input-group-text,\n  .form-group.no-border.form-control-lg .form-control + .input-group-append .input-group-text,\n  .input-group.no-border.form-control-lg .form-control + .input-group-prepend .input-group-text,\n  .input-group.no-border.form-control-lg .form-control + .input-group-append .input-group-text {\n    padding: 15px 11px 15px 0; }\n\n.form-group.form-control-lg .form-control,\n.input-group.form-control-lg .form-control {\n  padding: 14px 10px; }\n  .form-group.form-control-lg .form-control + .input-group-prepend .input-group-text,\n  .form-group.form-control-lg .form-control + .input-group-append .input-group-text,\n  .input-group.form-control-lg .form-control + .input-group-prepend .input-group-text,\n  .input-group.form-control-lg .form-control + .input-group-append .input-group-text {\n    padding: 14px 10px 14px 0; }\n\n.form-group.form-control-lg .input-group-prepend .input-group-text,\n.form-group.form-control-lg .input-group-append .input-group-text,\n.input-group.form-control-lg .input-group-prepend .input-group-text,\n.input-group.form-control-lg .input-group-append .input-group-text {\n  padding: 14px 0 15px 10px; }\n  .form-group.form-control-lg .input-group-prepend .input-group-text + .form-control,\n  .form-group.form-control-lg .input-group-append .input-group-text + .form-control,\n  .input-group.form-control-lg .input-group-prepend .input-group-text + .form-control,\n  .input-group.form-control-lg .input-group-append .input-group-text + .form-control {\n    padding: 15px 10px 15px 8px; }\n\n.form-group.no-border .form-control,\n.input-group.no-border .form-control {\n  padding: 11px 11px; }\n  .form-group.no-border .form-control + .input-group-prepend .input-group-text,\n  .form-group.no-border .form-control + .input-group-append .input-group-text,\n  .input-group.no-border .form-control + .input-group-prepend .input-group-text,\n  .input-group.no-border .form-control + .input-group-append .input-group-text {\n    padding: 11px 11px 11px 0; }\n\n.form-group.no-border .input-group-prepend .input-group-text,\n.form-group.no-border .input-group-append .input-group-text,\n.input-group.no-border .input-group-prepend .input-group-text,\n.input-group.no-border .input-group-append .input-group-text {\n  padding: 11px 0 11px 11px; }\n\n.form-group .form-control,\n.input-group .form-control {\n  padding: 10px 10px 10px 10px; }\n  .form-group .form-control + .input-group-prepend .input-group-text,\n  .form-group .form-control + .input-group-append .input-group-text,\n  .input-group .form-control + .input-group-prepend .input-group-text,\n  .input-group .form-control + .input-group-append .input-group-text {\n    padding: 10px 10px 10px 0; }\n\n.form-group .input-group-prepend .input-group-text,\n.form-group .input-group-append .input-group-text,\n.input-group .input-group-prepend .input-group-text,\n.input-group .input-group-append .input-group-text {\n  padding: 10px 0 10px 10px; }\n  .form-group .input-group-prepend .input-group-text + .form-control,\n  .form-group .input-group-prepend .input-group-text ~ .form-control,\n  .form-group .input-group-append .input-group-text + .form-control,\n  .form-group .input-group-append .input-group-text ~ .form-control,\n  .input-group .input-group-prepend .input-group-text + .form-control,\n  .input-group .input-group-prepend .input-group-text ~ .form-control,\n  .input-group .input-group-append .input-group-text + .form-control,\n  .input-group .input-group-append .input-group-text ~ .form-control {\n    padding: 10px 11px 11px 8px; }\n\n.input-group.has-success .input-group-prepend .input-group-text,\n.input-group.has-success .input-group-append .input-group-text {\n  border: 1px solid #ccc;\n  color: #66615b;\n  background-color: #FFFFFF;\n  border-right: none; }\n\n.form-group.no-border .form-control,\n.form-group.no-border .form-control + .input-group-prepend .input-group-text,\n.form-group.no-border .form-control + .input-group-append .input-group-text,\n.input-group.no-border .form-control,\n.input-group.no-border .form-control + .input-group-prepend .input-group-text,\n.input-group.no-border .form-control + .input-group-append .input-group-text {\n  background-color: rgba(222, 222, 222, 0.3);\n  border: medium none; }\n  .form-group.no-border .form-control:focus, .form-group.no-border .form-control:active, .form-group.no-border .form-control:active,\n  .form-group.no-border .form-control + .input-group-prepend .input-group-text:focus,\n  .form-group.no-border .form-control + .input-group-prepend .input-group-text:active,\n  .form-group.no-border .form-control + .input-group-prepend .input-group-text:active,\n  .form-group.no-border .form-control + .input-group-append .input-group-text:focus,\n  .form-group.no-border .form-control + .input-group-append .input-group-text:active,\n  .form-group.no-border .form-control + .input-group-append .input-group-text:active,\n  .input-group.no-border .form-control:focus,\n  .input-group.no-border .form-control:active,\n  .input-group.no-border .form-control:active,\n  .input-group.no-border .form-control + .input-group-prepend .input-group-text:focus,\n  .input-group.no-border .form-control + .input-group-prepend .input-group-text:active,\n  .input-group.no-border .form-control + .input-group-prepend .input-group-text:active,\n  .input-group.no-border .form-control + .input-group-append .input-group-text:focus,\n  .input-group.no-border .form-control + .input-group-append .input-group-text:active,\n  .input-group.no-border .form-control + .input-group-append .input-group-text:active {\n    border: medium none;\n    background-color: rgba(222, 222, 222, 0.5); }\n\n.form-group.no-border .form-control:focus + .input-group-prepend .input-group-text,\n.form-group.no-border .form-control:focus + .input-group-append .input-group-text,\n.input-group.no-border .form-control:focus + .input-group-prepend .input-group-text,\n.input-group.no-border .form-control:focus + .input-group-append .input-group-text {\n  background-color: rgba(222, 222, 222, 0.5); }\n\n.form-group.no-border .input-group-prepend .input-group-text,\n.form-group.no-border .input-group-append .input-group-text,\n.input-group.no-border .input-group-prepend .input-group-text,\n.input-group.no-border .input-group-append .input-group-text {\n  background-color: rgba(222, 222, 222, 0.3);\n  border: none; }\n\n.has-error .form-control-feedback, .has-error .control-label {\n  color: #ef8157; }\n\n.has-success .form-control-feedback, .has-success .control-label {\n  color: #6bd098; }\n\n.input-group.has-danger .input-group-prepend {\n  border-radius: 4px; }\n  .input-group.has-danger .input-group-prepend .input-group-text {\n    border: 1px solid #ef8157;\n    border-right: none; }\n\n.input-group.has-danger .error {\n  display: block;\n  width: 100%;\n  color: #ef8157;\n  margin-top: 3px; }\n\n.input-group.has-success .input-group-prepend {\n  border-radius: 4px; }\n  .input-group.has-success .input-group-prepend .input-group-text {\n    border-right: none; }\n\n.input-group-focus .input-group-prepend .input-group-text,\n.input-group-focus .input-group-append .input-group-text {\n  background-color: #FFFFFF;\n  border-color: #9A9A9A; }\n\n.input-group-focus.no-border .input-group-prepend .input-group-text,\n.input-group-focus.no-border .input-group-append .input-group-text {\n  background-color: rgba(222, 222, 222, 0.5); }\n\n.input-group-focus.has-danger .input-group-append .input-group-text,\n.input-group-focus.has-danger .input-group-prepend .input-group-text {\n  background-color: #FFC0A4; }\n\n.input-group-focus.has-success .input-group-append .input-group-text,\n.input-group-focus.has-success .input-group-prepend .input-group-text {\n  background-color: #ABF3CB;\n  border: 1px solid #6bd098;\n  border-right: none; }\n\n.input-group-append .input-group-text,\n.input-group-prepend .input-group-text {\n  background-color: transparent;\n  border: 1px solid #E3E3E3;\n  color: #66615B;\n  border-top-right-radius: 4px;\n  border-bottom-right-radius: 4px;\n  -webkit-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -moz-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -o-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  -ms-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n  transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out; }\n  .input-group-append .input-group-text i,\n  .input-group-prepend .input-group-text i {\n    opacity: .5; }\n  .has-danger .input-group-append .input-group-text, .has-danger\n  .input-group-prepend .input-group-text {\n    background-color: #FFC0A4; }\n  .has-success .input-group-append .input-group-text, .has-success\n  .input-group-prepend .input-group-text {\n    background-color: #ABF3CB; }\n  .has-danger.input-group-focus .input-group-append .input-group-text, .has-danger.input-group-focus\n  .input-group-prepend .input-group-text {\n    background-color: #FFFFFF;\n    color: #ef8157; }\n  .has-success.input-group-focus .input-group-append .input-group-text, .has-success.input-group-focus\n  .input-group-prepend .input-group-text {\n    background-color: #FFFFFF;\n    color: #6bd098; }\n  .has-danger .form-control:focus + .input-group-append .input-group-text, .has-danger .form-control:focus +\n  .input-group-prepend .input-group-text {\n    color: #ef8157; }\n  .has-success .form-control:focus + .input-group-append .input-group-text, .has-success .form-control:focus +\n  .input-group-prepend .input-group-text {\n    color: #6bd098; }\n  .input-group-append .input-group-text + .form-control,\n  .input-group-append .input-group-text ~ .form-control,\n  .input-group-prepend .input-group-text + .form-control,\n  .input-group-prepend .input-group-text ~ .form-control {\n    padding: -0.5rem 0.7rem;\n    padding-left: 18px; }\n  .input-group-append .input-group-text i,\n  .input-group-prepend .input-group-text i {\n    width: 17px; }\n\n.input-group-append,\n.input-group-prepend {\n  margin: 0; }\n\n.input-group-append .input-group-text {\n  border-left: none; }\n\n.input-group-prepend .input-group-text {\n  border-right: none; }\n\n.input-group,\n.form-group {\n  margin-bottom: 10px;\n  position: relative; }\n  .input-group .form-control-static,\n  .form-group .form-control-static {\n    margin-top: 9px; }\n  .input-group.has-danger .error,\n  .form-group.has-danger .error {\n    color: #ef8157; }\n\n.input-group[disabled] .input-group-prepend .input-group-text,\n.input-group[disabled] .input-group-append .input-group-text {\n  background-color: #E3E3E3; }\n\n.input-group .form-control:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child) {\n  border-radius: 4px;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-left: 0 none; }\n\n.input-group .form-control:first-child,\n.input-group-btn:first-child > .dropdown-toggle,\n.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {\n  border-right: 0 none; }\n\n.input-group .form-control:last-child,\n.input-group-btn:last-child > .dropdown-toggle,\n.input-group-btn:first-child > .btn:not(:first-child) {\n  border-left: 0 none; }\n\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\n  background-color: #E3E3E3;\n  color: #66615B;\n  cursor: not-allowed; }\n\n.input-group-btn .btn {\n  border-width: 1px;\n  padding: 11px 0.7rem; }\n\n.input-group-btn .btn-default:not(.btn-fill) {\n  border-color: #DDDDDD; }\n\n.input-group-btn:last-child > .btn {\n  margin-left: 0; }\n\ntextarea.form-control {\n  max-width: 100%;\n  max-height: 80px;\n  padding: 10px 10px 0 0;\n  resize: none;\n  border: none;\n  border: 1px solid #E3E3E3;\n  border-radius: 4px;\n  line-height: 2; }\n\n.has-success.form-group .form-control,\n.has-success.form-group.no-border .form-control,\n.has-danger.form-group .form-control,\n.has-danger.form-group.no-border .form-control {\n  padding-right: 32px; }\n\n.form.form-newsletter .form-group {\n  float: left;\n  width: 78%;\n  margin-right: 2%;\n  margin-top: 9px; }\n\n.input-group .input-group-btn {\n  padding: 0 12px; }\n\n.form-group input[type=file] {\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 100; }\n\n.form-text {\n  font-size: 0.8571em; }\n\n.form-control-lg {\n  padding: 0;\n  font-size: inherit;\n  line-height: 0;\n  border-radius: 0; }\n\n.form-horizontal .col-form-label,\n.form-horizontal .label-on-right {\n  padding: 10px 5px 0 15px;\n  text-align: right;\n  max-width: 180px; }\n\n.form-horizontal .checkbox-radios {\n  margin-bottom: 15px; }\n  .form-horizontal .checkbox-radios .form-check:first-child {\n    margin-top: 8px; }\n\n.form-horizontal .label-on-right {\n  text-align: left;\n  padding: 10px 15px 0 5px; }\n\n.form-horizontal .form-check-inline {\n  margin-top: 6px; }\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: \"Montserrat\", \"Helvetica Neue\", Arial, sans-serif; }\n\nh1, h2, h3, h4, h5, h6 {\n  font-weight: 400; }\n\na {\n  color: #51cbce; }\n  a:hover, a:focus {\n    color: #51cbce; }\n\nh1, .h1 {\n  font-size: 3.5em;\n  line-height: 1.15;\n  margin-bottom: 30px; }\n  h1 small, .h1 small {\n    font-weight: 700;\n    text-transform: uppercase;\n    opacity: .8; }\n\nh2, .h2 {\n  font-size: 2.5em;\n  margin-bottom: 30px; }\n\nh3, .h3 {\n  font-size: 2em;\n  margin-bottom: 30px;\n  line-height: 1.4em; }\n\nh4, .h4 {\n  font-size: 1.714em;\n  line-height: 1.45em;\n  margin-top: 30px;\n  margin-bottom: 15px; }\n  h4 + .category,\n  h4.title + .category, .h4 + .category,\n  .h4.title + .category {\n    margin-top: -10px; }\n\nh5, .h5 {\n  font-size: 1.57em;\n  line-height: 1.4em;\n  margin-bottom: 15px; }\n\nh6, .h6 {\n  font-size: 1em;\n  font-weight: 700;\n  text-transform: uppercase; }\n\np.description {\n  font-size: 1.14em; }\n\n.title {\n  font-weight: 700; }\n  .title.title-up {\n    text-transform: uppercase; }\n    .title.title-up a {\n      color: #2c2c2c;\n      text-decoration: none; }\n  .title + .category {\n    margin-top: -10px; }\n\n.description,\n.card-description,\n.footer-big p,\n.card .footer .stats {\n  color: #9A9A9A;\n  font-weight: 300; }\n\n.category,\n.card-category {\n  text-transform: capitalize;\n  font-weight: 400;\n  color: #9A9A9A;\n  font-size: 0.7142em; }\n\n.card-category {\n  font-size: 1em; }\n\n.text-primary,\na.text-primary:focus, a.text-primary:hover {\n  color: #51cbce !important; }\n\n.text-info,\na.text-info:focus, a.text-info:hover {\n  color: #51bcda !important; }\n\n.text-success,\na.text-success:focus, a.text-success:hover {\n  color: #6bd098 !important; }\n\n.text-warning,\na.text-warning:focus, a.text-warning:hover {\n  color: #fbc658 !important; }\n\n.text-danger,\na.text-danger:focus, a.text-danger:hover {\n  color: #ef8157 !important; }\n\n.text-gray,\na.text-gray:focus, a.text-gray:hover {\n  color: #E3E3E3 !important; }\n\n.blockquote {\n  border-left: none;\n  border: 1px solid #66615B;\n  padding: 20px;\n  font-size: 1.1em;\n  line-height: 1.8; }\n  .blockquote small {\n    color: #66615B;\n    font-size: 0.8571em;\n    text-transform: uppercase; }\n  .blockquote.blockquote-primary {\n    border-color: #51cbce;\n    color: #51cbce; }\n    .blockquote.blockquote-primary small {\n      color: #51cbce; }\n  .blockquote.blockquote-danger {\n    border-color: #ef8157;\n    color: #ef8157; }\n    .blockquote.blockquote-danger small {\n      color: #ef8157; }\n  .blockquote.blockquote-white {\n    border-color: rgba(255, 255, 255, 0.8);\n    color: #FFFFFF; }\n    .blockquote.blockquote-white small {\n      color: rgba(255, 255, 255, 0.8); }\n\nbody {\n  color: #2c2c2c;\n  font-size: 14px;\n  font-family: \"Montserrat\", \"Helvetica Neue\", Arial, sans-serif;\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased; }\n\n.main {\n  position: relative;\n  background: #FFFFFF; }\n\n/* Animations */\n.nav-pills .nav-link,\n.navbar,\n.nav-tabs .nav-link,\n.sidebar .nav a,\n.sidebar .nav a i,\n.animation-transition-general,\n.sidebar .navbar-minimize,\n.off-canvas-sidebar .navbar-minimize,\n.sidebar .logo a.logo-mini,\n.sidebar .logo a.logo-normal,\n.off-canvas-sidebar .logo a.logo-mini,\n.off-canvas-sidebar .logo a.logo-normal,\n.tag,\n.tag [data-role=\"remove\"],\n.animation-transition-general,\n.sidebar .navbar-minimize,\n.off-canvas-sidebar .navbar-minimize,\n.sidebar .logo a.logo-mini,\n.sidebar .logo a.logo-normal,\n.off-canvas-sidebar .logo a.logo-mini,\n.off-canvas-sidebar .logo a.logo-normal {\n  -webkit-transition: all 300ms ease 0s;\n  -moz-transition: all 300ms ease 0s;\n  -o-transition: all 300ms ease 0s;\n  -ms-transition: all 300ms ease 0s;\n  transition: all 300ms ease 0s; }\n\n.dropdown-toggle:after,\n.bootstrap-switch-label:before,\n.caret {\n  -webkit-transition: all 150ms ease 0s;\n  -moz-transition: all 150ms ease 0s;\n  -o-transition: all 150ms ease 0s;\n  -ms-transition: all 150ms ease 0s;\n  transition: all 150ms ease 0s; }\n\n.dropdown-toggle[aria-expanded=\"true\"]:after,\na[data-toggle=\"collapse\"][aria-expanded=\"true\"] .caret,\n.card-collapse .card a[data-toggle=\"collapse\"][aria-expanded=\"true\"] i,\n.card-collapse .card a[data-toggle=\"collapse\"].expanded i {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n  -webkit-transform: rotate(180deg);\n  -ms-transform: rotate(180deg);\n  transform: rotate(180deg); }\n\n.button-bar {\n  display: block;\n  position: relative;\n  width: 22px;\n  height: 1px;\n  border-radius: 1px;\n  background: #FFFFFF; }\n  .button-bar + .button-bar {\n    margin-top: 7px; }\n  .button-bar:nth-child(2) {\n    width: 17px; }\n\n.caret {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 2px;\n  vertical-align: middle;\n  border-top: 4px dashed;\n  border-top: 4px solid\\9;\n  border-right: 4px solid transparent;\n  border-left: 4px solid transparent; }\n\n.pull-left {\n  float: left; }\n\n.pull-right {\n  float: right; }\n\n.offline-doc .navbar.navbar-transparent {\n  padding-top: 25px;\n  border-bottom: none; }\n  .offline-doc .navbar.navbar-transparent .navbar-minimize {\n    display: none; }\n  .offline-doc .navbar.navbar-transparent .navbar-brand,\n  .offline-doc .navbar.navbar-transparent .collapse .navbar-nav .nav-link {\n    color: #FFFFFF !important; }\n\n.offline-doc .footer {\n  z-index: 3 !important; }\n\n.offline-doc .page-header .page-header-image {\n  position: absolute;\n  background-size: cover;\n  background-position: center center;\n  width: 100%;\n  height: 100%;\n  z-index: -1; }\n\n.offline-doc .page-header .container {\n  z-index: 3; }\n\n.offline-doc .page-header:after {\n  background-color: rgba(0, 0, 0, 0.5);\n  content: \"\";\n  display: block;\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n  z-index: 2; }\n\n.fixed-plugin .dropdown-menu li {\n  padding: 2px !important; }\n\n.badge.badge-default {\n  border-color: #66615B;\n  background-color: #66615B; }\n\n.badge.badge-primary {\n  border-color: #51cbce;\n  background-color: #51cbce; }\n\n.badge.badge-info {\n  border-color: #51bcda;\n  background-color: #51bcda; }\n\n.badge.badge-success {\n  border-color: #6bd098;\n  background-color: #6bd098; }\n\n.badge.badge-warning {\n  border-color: #fbc658;\n  background-color: #fbc658; }\n\n.badge.badge-danger {\n  border-color: #ef8157;\n  background-color: #ef8157; }\n\n.badge.badge-neutral {\n  border-color: #FFFFFF;\n  background-color: #FFFFFF;\n  color: inherit; }\n\n.card-user form .form-group {\n  margin-bottom: 20px; }\n\n.from-check,\n.form-check-radio {\n  margin-bottom: 12px;\n  position: relative; }\n\n.form-check {\n  padding-left: 0;\n  margin-bottom: .5rem; }\n  .form-check .form-check-label {\n    display: inline-block;\n    position: relative;\n    cursor: pointer;\n    padding-left: 35px;\n    line-height: 26px;\n    margin-bottom: 0; }\n  .form-check .form-check-sign::before,\n  .form-check .form-check-sign::after {\n    content: \" \";\n    display: inline-block;\n    position: absolute;\n    width: 24px;\n    height: 24px;\n    left: 0;\n    cursor: pointer;\n    border-radius: 6px;\n    top: 0;\n    background-color: #AAA7A4;\n    -webkit-transition: opacity 0.3s linear;\n    -moz-transition: opacity 0.3s linear;\n    -o-transition: opacity 0.3s linear;\n    -ms-transition: opacity 0.3s linear;\n    transition: opacity 0.3s linear; }\n  .form-check .form-check-sign::after {\n    font-family: 'FontAwesome';\n    content: \"\\f00c\";\n    top: -1px;\n    text-align: center;\n    font-size: 15px;\n    opacity: 0;\n    color: #FFF;\n    border: 0;\n    background-color: inherit; }\n  .form-check.disabled .form-check-label {\n    color: #9A9A9A;\n    opacity: .5;\n    cursor: not-allowed; }\n\n.form-check input[type=\"checkbox\"],\n.form-check-radio input[type=\"radio\"] {\n  opacity: 0;\n  position: absolute;\n  visibility: hidden; }\n\n.form-check input[type=\"checkbox\"]:checked + .form-check-sign::after {\n  opacity: 1; }\n\n.form-control input[type=\"checkbox\"]:disabled + .form-check-sign::before,\n.checkbox input[type=\"checkbox\"]:disabled + .form-check-sign::after {\n  cursor: not-allowed; }\n\n.form-check .form-check-label input[type=\"checkbox\"]:disabled + .form-check-sign,\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign {\n  pointer-events: none !important; }\n\n.form-check-radio {\n  margin-left: -3px; }\n  .form-check-radio .form-check-label {\n    padding-left: 2rem; }\n  .form-check-radio.disabled .form-check-label {\n    color: #9A9A9A;\n    opacity: .5;\n    cursor: not-allowed; }\n\n.form-check-radio .form-check-sign::before {\n  font-family: 'FontAwesome';\n  content: \"\\f10c\";\n  font-size: 22px;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  display: inline-block;\n  position: absolute;\n  opacity: .50;\n  left: 5px;\n  top: -5px; }\n\n.form-check-label input[type=\"checkbox\"]:checked + .form-check-sign:before {\n  background-color: #66615B; }\n\n.form-check-radio input[type=\"radio\"] + .form-check-sign:after,\n.form-check-radio input[type=\"radio\"] {\n  opacity: 0;\n  -webkit-transition: opacity 0.3s linear;\n  -moz-transition: opacity 0.3s linear;\n  -o-transition: opacity 0.3s linear;\n  -ms-transition: opacity 0.3s linear;\n  transition: opacity 0.3s linear;\n  content: \" \";\n  display: block; }\n\n.form-check-radio input[type=\"radio\"]:checked + .form-check-sign::after {\n  font-family: 'FontAwesome';\n  content: \"\\f192\";\n  top: -5px;\n  position: absolute;\n  left: 5px;\n  opacity: 1;\n  font-size: 22px; }\n\n.form-check-radio input[type=\"radio\"]:checked + .form-check-sign::after {\n  opacity: 1; }\n\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign::before,\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign::after {\n  color: #9A9A9A; }\n\n.navbar {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n  min-height: 53px;\n  margin-bottom: 20px; }\n  .navbar a {\n    vertical-align: middle; }\n    .navbar a:not(.btn):not(.dropdown-item) {\n      color: #FFFFFF; }\n    .navbar a.dropdown-item {\n      color: #66615B; }\n  .navbar.bg-white .input-group .form-control,\n  .navbar.bg-white .input-group.no-border .form-control {\n    color: #66615B; }\n    .navbar.bg-white .input-group .form-control::-moz-placeholder,\n    .navbar.bg-white .input-group.no-border .form-control::-moz-placeholder {\n      color: #66615B; }\n    .navbar.bg-white .input-group .form-control:-ms-input-placeholder,\n    .navbar.bg-white .input-group.no-border .form-control:-ms-input-placeholder {\n      color: #66615B; }\n    .navbar.bg-white .input-group .form-control::-webkit-input-placeholder,\n    .navbar.bg-white .input-group.no-border .form-control::-webkit-input-placeholder {\n      color: #66615B; }\n  .navbar.bg-white .input-group-prepend .input-group-text i,\n  .navbar.bg-white .input-group-append .input-group-text i {\n    color: #66615B;\n    opacity: .5; }\n  .navbar .form-group,\n  .navbar .input-group {\n    margin: 0;\n    margin-left: -3px;\n    margin-right: 5px; }\n    .navbar .form-group .form-group-addon,\n    .navbar .form-group .input-group-prepend .input-group-text,\n    .navbar .form-group .input-group-append .input-group-text,\n    .navbar .input-group .form-group-addon,\n    .navbar .input-group .input-group-prepend .input-group-text,\n    .navbar .input-group .input-group-append .input-group-text {\n      color: #66615B; }\n      .navbar .form-group .form-group-addon i,\n      .navbar .form-group .input-group-prepend .input-group-text i,\n      .navbar .form-group .input-group-append .input-group-text i,\n      .navbar .input-group .form-group-addon i,\n      .navbar .input-group .input-group-prepend .input-group-text i,\n      .navbar .input-group .input-group-append .input-group-text i {\n        opacity: 1; }\n    .navbar .form-group.no-border .form-control,\n    .navbar .input-group.no-border .form-control {\n      color: #66615B; }\n      .navbar .form-group.no-border .form-control::-moz-placeholder,\n      .navbar .input-group.no-border .form-control::-moz-placeholder {\n        color: #66615B; }\n      .navbar .form-group.no-border .form-control:-ms-input-placeholder,\n      .navbar .input-group.no-border .form-control:-ms-input-placeholder {\n        color: #66615B; }\n      .navbar .form-group.no-border .form-control::-webkit-input-placeholder,\n      .navbar .input-group.no-border .form-control::-webkit-input-placeholder {\n        color: #66615B; }\n  .navbar p {\n    display: inline-block;\n    margin: 0;\n    line-height: 1.8em;\n    font-size: 1em;\n    font-weight: 400; }\n  .navbar.navbar-absolute {\n    position: absolute;\n    width: 100%;\n    padding-top: 10px;\n    z-index: 1029; }\n  .documentation .navbar.fixed-top {\n    left: 0;\n    width: initial; }\n  .navbar .navbar-wrapper {\n    display: inline-flex;\n    align-items: center; }\n    .navbar .navbar-wrapper .navbar-minimize {\n      padding-right: 10px; }\n      .navbar .navbar-wrapper .navbar-minimize .btn {\n        margin: 0; }\n    .navbar .navbar-wrapper .navbar-toggle .navbar-toggler {\n      padding-left: 0; }\n    .navbar .navbar-wrapper .navbar-toggle:hover .navbar-toggler-bar.bar2 {\n      width: 22px; }\n  .navbar .navbar-nav.navbar-logo {\n    position: absolute;\n    left: 0;\n    right: 0;\n    margin: 0 auto;\n    width: 49px;\n    top: -4px; }\n  .navbar .navbar-nav .nav-link.btn {\n    padding: 11px 22px; }\n    .navbar .navbar-nav .nav-link.btn.btn-lg {\n      padding: 15px 48px; }\n    .navbar .navbar-nav .nav-link.btn.btn-sm {\n      padding: 5px 15px; }\n  .navbar .navbar-nav .nav-link {\n    text-transform: uppercase;\n    font-size: 0.7142em;\n    padding: 0.5rem 0.7rem;\n    line-height: 1.625rem;\n    margin-right: 3px; }\n    .navbar .navbar-nav .nav-link i.fa + p,\n    .navbar .navbar-nav .nav-link i.nc-icon + p {\n      margin-left: 3px; }\n    .navbar .navbar-nav .nav-link i.fa,\n    .navbar .navbar-nav .nav-link i.nc-icon {\n      font-size: 18px;\n      position: relative;\n      top: 3px;\n      text-align: center;\n      width: 21px; }\n    .navbar .navbar-nav .nav-link i.nc-icon {\n      top: 4px;\n      font-size: 16px; }\n    .navbar .navbar-nav .nav-link.profile-photo .profile-photo-small {\n      width: 27px;\n      height: 27px; }\n    .navbar .navbar-nav .nav-link.disabled {\n      opacity: .5;\n      color: #FFFFFF; }\n  .navbar .navbar-nav .nav-item.active .nav-link:not(.btn),\n  .navbar .navbar-nav .nav-item .nav-link:not(.btn):focus,\n  .navbar .navbar-nav .nav-item .nav-link:not(.btn):hover,\n  .navbar .navbar-nav .nav-item .nav-link:not(.btn):active {\n    border-radius: 3px;\n    color: #66615B; }\n  .navbar .logo-container {\n    width: 27px;\n    height: 27px;\n    overflow: hidden;\n    margin: 0 auto;\n    border-radius: 50%;\n    border: 1px solid transparent; }\n  .navbar .navbar-brand {\n    text-transform: capitalize;\n    font-size: 20px;\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n    line-height: 1.625rem; }\n  .navbar .navbar-toggler {\n    width: 37px;\n    height: 27px;\n    vertical-align: middle;\n    outline: 0;\n    cursor: pointer; }\n    .navbar .navbar-toggler .navbar-toggler-bar.navbar-kebab {\n      width: 3px;\n      height: 3px;\n      border-radius: 50%;\n      margin: 0 auto; }\n  .navbar .button-dropdown .navbar-toggler-bar:nth-child(2) {\n    width: 17px; }\n  .navbar.navbar-transparent {\n    background-color: transparent !important;\n    box-shadow: none;\n    border-bottom: 1px solid #ddd; }\n    .navbar.navbar-transparent a:not(.dropdown-item):not(.btn) {\n      color: #66615B; }\n      .navbar.navbar-transparent a:not(.dropdown-item):not(.btn).disabled {\n        opacity: .5;\n        color: #66615B; }\n    .navbar.navbar-transparent .button-bar {\n      background: #66615B; }\n    .navbar.navbar-transparent .nav-item .nav-link:not(.btn) {\n      color: #66615B; }\n    .navbar.navbar-transparent .nav-item.active .nav-link:not(.btn),\n    .navbar.navbar-transparent .nav-item .nav-link:not(.btn):focus,\n    .navbar.navbar-transparent .nav-item .nav-link:not(.btn):hover,\n    .navbar.navbar-transparent .nav-item .nav-link:not(.btn):focus:hover,\n    .navbar.navbar-transparent .nav-item .nav-link:not(.btn):active {\n      color: #51cbce; }\n  .navbar.bg-white a:not(.dropdown-item):not(.btn) {\n    color: #66615B; }\n    .navbar.bg-white a:not(.dropdown-item):not(.btn).disabled {\n      opacity: .5;\n      color: #66615B; }\n  .navbar.bg-white .button-bar {\n    background: #66615B; }\n  .navbar.bg-white .nav-item.active .nav-link:not(.btn),\n  .navbar.bg-white .nav-item .nav-link:not(.btn):focus,\n  .navbar.bg-white .nav-item .nav-link:not(.btn):hover,\n  .navbar.bg-white .nav-item .nav-link:not(.btn):active {\n    color: #51bcda; }\n  .navbar.bg-white .logo-container {\n    border: 1px solid #66615B; }\n  .navbar .navbar-collapse .nav-item a {\n    font-size: 14px; }\n\n.bg-default {\n  background-color: #66615B !important; }\n\n.bg-primary {\n  background-color: #51cbce !important; }\n\n.bg-info {\n  background-color: #51bcda !important; }\n\n.bg-success {\n  background-color: #6bd098 !important; }\n\n.bg-danger {\n  background-color: #ef8157 !important; }\n\n.bg-warning {\n  background-color: #fbc658 !important; }\n\n.bg-white {\n  background-color: #FFFFFF !important; }\n\n.page-header {\n  min-height: 100vh;\n  max-height: 1000px;\n  padding: 0;\n  color: #FFFFFF;\n  position: relative; }\n  .page-header .page-header-image {\n    position: absolute;\n    background-size: cover;\n    background-position: center center;\n    width: 100%;\n    height: 100%;\n    z-index: -1; }\n  .page-header .content-center {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    z-index: 2;\n    -ms-transform: translate(-50%, -50%);\n    -webkit-transform: translate(-50%, -50%);\n    transform: translate(-50%, -50%);\n    text-align: center;\n    color: #FFFFFF;\n    padding: 0 15px;\n    width: 100%;\n    max-width: 880px; }\n  .page-header footer {\n    position: absolute;\n    bottom: 0;\n    width: 100%; }\n  .page-header .container {\n    height: 100%;\n    z-index: 1; }\n  .page-header .category,\n  .page-header .description {\n    color: rgba(255, 255, 255, 0.8); }\n  .page-header.page-header-small {\n    min-height: 60vh;\n    max-height: 440px; }\n  .page-header.page-header-mini {\n    min-height: 40vh;\n    max-height: 340px; }\n  .page-header .title {\n    margin-bottom: 15px; }\n  .page-header .title + h4 {\n    margin-top: 10px; }\n  .page-header:after, .page-header:before {\n    position: absolute;\n    z-index: 0;\n    width: 100%;\n    height: 100%;\n    display: block;\n    left: 0;\n    top: 0;\n    content: \"\"; }\n  .page-header:before {\n    background-color: rgba(0, 0, 0, 0.3); }\n  .page-header[filter-color=\"orange\"] {\n    background: rgba(44, 44, 44, 0.2);\n    /* For browsers that do not support gradients */\n    background: -webkit-linear-gradient(90deg, rgba(44, 44, 44, 0.2), rgba(224, 23, 3, 0.6));\n    /* For Safari 5.1 to 6.0 */\n    background: -o-linear-gradient(90deg, rgba(44, 44, 44, 0.2), rgba(224, 23, 3, 0.6));\n    /* For Opera 11.1 to 12.0 */\n    background: -moz-linear-gradient(90deg, rgba(44, 44, 44, 0.2), rgba(224, 23, 3, 0.6));\n    /* For Firefox 3.6 to 15 */\n    background: linear-gradient(0deg, rgba(44, 44, 44, 0.2), rgba(224, 23, 3, 0.6));\n    /* Standard syntax */ }\n\n.dropdown > .dropdown-menu:first-of-type,\n.dropup > .dropdown-menu:first-of-type {\n  display: block;\n  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s; }\n\n.dropdown .dropdown-menu,\n.dropup .dropdown-menu {\n  opacity: 0;\n  filter: alpha(opacity=0);\n  -webkit-box-shadow: 1px 2px 7px 1px rgba(0, 0, 0, 0.125);\n  box-shadow: 1px 2px 7px 1px rgba(0, 0, 0, 0.125);\n  visibility: hidden;\n  position: absolute; }\n  .dropdown .dropdown-menu[x-placement=\"top-start\"],\n  .dropup .dropdown-menu[x-placement=\"top-start\"] {\n    -webkit-transform: translate3d(-20px, 0px, 0) !important;\n    -moz-transform: translate3d(-20px, 0px, 0) !important;\n    -o-transform: translate3d(-20px, 0px, 0) !important;\n    -ms-transform: translate3d(-20px, 0px, 0) !important;\n    transform: translate3d(-20px, 0px, 0) !important; }\n  .dropdown .dropdown-menu[x-placement=\"bottom-start\"],\n  .dropup .dropdown-menu[x-placement=\"bottom-start\"] {\n    -webkit-transform: translate3d(-20px, 0px, 0) !important;\n    -moz-transform: translate3d(-20px, 0px, 0) !important;\n    -o-transform: translate3d(-20px, 0px, 0) !important;\n    -ms-transform: translate3d(-20px, 0px, 0) !important;\n    transform: translate3d(-20px, 0px, 0) !important; }\n\n.dropdown.show .dropdown-menu,\n.dropup.show .dropdown-menu {\n  opacity: 1;\n  filter: alpha(opacity=100);\n  visibility: visible; }\n  .dropdown.show .dropdown-menu[x-placement=\"top-start\"],\n  .dropup.show .dropdown-menu[x-placement=\"top-start\"] {\n    -webkit-transform: translate3d(-20px, -50px, 0) !important;\n    -moz-transform: translate3d(-20px, -50px, 0) !important;\n    -o-transform: translate3d(-20px, -50px, 0) !important;\n    -ms-transform: translate3d(-20px, -50px, 0) !important;\n    transform: translate3d(-20px, -50px, 0) !important;\n    top: auto !important;\n    bottom: 0 !important; }\n  .dropdown.show .dropdown-menu[x-placement=\"bottom-start\"],\n  .dropup.show .dropdown-menu[x-placement=\"bottom-start\"] {\n    -webkit-transform: translate3d(-20px, 50px, 0) !important;\n    -moz-transform: translate3d(-20px, 50px, 0) !important;\n    -o-transform: translate3d(-20px, 50px, 0) !important;\n    -ms-transform: translate3d(-20px, 50px, 0) !important;\n    transform: translate3d(-20px, 50px, 0) !important;\n    bottom: auto !important;\n    top: 0 !important; }\n\n.bootstrap-select .dropdown-menu li.hidden {\n  display: none; }\n\n.bootstrap-select .bs-searchbox .form-control {\n  border-radius: 4px;\n  border-bottom-color: #ddd; }\n\n.dropup .dropdown-menu,\n.dropdown-btn .dropdown-menu {\n  -webkit-transform: translate3d(-20px, 0px, 0) !important;\n  -moz-transform: translate3d(-20px, 0px, 0) !important;\n  -o-transform: translate3d(-20px, 0px, 0) !important;\n  -ms-transform: translate3d(-20px, 0px, 0) !important;\n  transform: translate3d(-20px, 0px, 0) !important;\n  top: auto !important;\n  bottom: 0 !important; }\n\n.dropup.show .dropdown-menu,\n.dropdown-btn.show .dropdown-menu {\n  opacity: 1;\n  visibility: visible; }\n  .dropup.show .dropdown-menu.show,\n  .dropdown-btn.show .dropdown-menu.show {\n    -webkit-transform: translate3d(0, -50px, 0) !important;\n    -moz-transform: translate3d(0, -50px, 0) !important;\n    -o-transform: translate3d(0, -50px, 0) !important;\n    -ms-transform: translate3d(0, -50px, 0) !important;\n    transform: translate3d(0, -50px, 0) !important; }\n\n.bootstrap-select.show .dropdown-menu.show[x-placement=\"top-start\"] {\n  -webkit-transform: translate3d(-20px, -57px, 0) !important;\n  -moz-transform: translate3d(-20px, -57px, 0) !important;\n  -o-transform: translate3d(-20px, -57px, 0) !important;\n  -ms-transform: translate3d(-20px, -57px, 0) !important;\n  transform: translate3d(-20px, -57px, 0) !important;\n  top: auto !important;\n  bottom: 0 !important; }\n\n.bootstrap-select.show .dropdown-menu.show li:last-child a:hover {\n  border-radius: 0 0 12px 12px; }\n\n.bootstrap-select.dropup.show:before {\n  top: -1px !important; }\n\n.bootstrap-select.dropup.show:after {\n  top: -2px !important; }\n\n.dropdown-menu {\n  background-color: #FFFFFF;\n  border: 0 none;\n  border-radius: 12px;\n  margin-top: 10px;\n  padding: 0px; }\n  .dropdown-menu .divider {\n    background-color: #F1EAE0;\n    margin: 0px; }\n  .dropdown-menu .dropdown-header {\n    color: #9A9A9A;\n    font-size: 0.8571em;\n    padding: 10px 15px; }\n  .dropdown-menu .no-notification {\n    color: #9A9A9A;\n    font-size: 1.2em;\n    padding: 30px 30px;\n    text-align: center; }\n  .dropdown-menu .dropdown-item {\n    color: #66615b;\n    font-size: 14px;\n    padding: 10px 45px 10px 15px;\n    clear: both;\n    white-space: nowrap;\n    width: 100%;\n    display: block; }\n    .dropdown-menu .dropdown-item img {\n      margin-top: -3px; }\n  .dropdown-menu .dropdown-item:focus {\n    outline: 0 !important; }\n  .btn-group.select .dropdown-menu {\n    min-width: 100%; }\n  .dropdown-menu .dropdown-item:first-child {\n    border-top-left-radius: 12px;\n    border-top-right-radius: 12px; }\n  .dropdown-menu .dropdown-item:last-child {\n    border-bottom-left-radius: 12px;\n    border-bottom-right-radius: 12px; }\n  .select .dropdown-menu .dropdown-item:first-child {\n    border-radius: 0;\n    border-bottom: 0 none; }\n  .dropdown-menu .dropdown-item:hover,\n  .dropdown-menu .dropdown-item:focus {\n    color: #FFFFFF !important;\n    opacity: 1;\n    text-decoration: none; }\n  .dropdown-menu .dropdown-item:hover,\n  .dropdown-menu .dropdown-item:focus {\n    background-color: #66615B; }\n  .dropdown-menu.dropdown-primary .dropdown-item:hover,\n  .dropdown-menu.dropdown-primary .dropdown-item:focus {\n    background-color: #6dd3d6; }\n  .dropdown-menu.dropdown-info .dropdown-item:hover,\n  .dropdown-menu.dropdown-info .dropdown-item:focus {\n    background-color: #6ec7e0; }\n  .dropdown-menu.dropdown-success .dropdown-item:hover,\n  .dropdown-menu.dropdown-success .dropdown-item:focus {\n    background-color: #86d9ab; }\n  .dropdown-menu.dropdown-warning .dropdown-item:hover,\n  .dropdown-menu.dropdown-warning .dropdown-item:focus {\n    background-color: #fcd27b; }\n  .dropdown-menu.dropdown-danger .dropdown-item:hover,\n  .dropdown-menu.dropdown-danger .dropdown-item:focus {\n    background-color: #f29978; }\n\n.dropdown-divider {\n  margin: 0 !important; }\n\n.btn-group.select.open {\n  overflow: visible; }\n\n.dropdown-menu-right {\n  right: -2px;\n  left: auto; }\n\n.navbar-nav .dropdown-menu:before,\n.dropdown .dropdown-menu[x-placement=\"bottom-start\"]:before,\n.dropdown .dropdown-menu[x-placement=\"bottom-end\"]:before,\n.card.card-just-text .dropdown .dropdown-menu:before,\n.card-just-text .dropdown .dropdown-menu:before,\n.dropdown-btn .dropdown-menu:before {\n  border-bottom: 11px solid #F1EAE0;\n  border-left: 11px solid rgba(0, 0, 0, 0);\n  border-right: 11px solid rgba(0, 0, 0, 0);\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  right: 12px;\n  top: -12px; }\n\n.navbar-nav .dropdown-menu:after,\n.dropdown .dropdown-menu[x-placement=\"bottom-start\"]:after,\n.dropdown .dropdown-menu[x-placement=\"bottom-end\"]:after,\n.card.card-just-text .dropdown .dropdown-menu:after,\n.card-just-text .dropdown .dropdown-menu:after,\n.dropdown-btn .dropdown-menu:after {\n  border-bottom: 11px solid #FFFFFF;\n  border-left: 11px solid rgba(0, 0, 0, 0);\n  border-right: 11px solid rgba(0, 0, 0, 0);\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  right: 12px;\n  top: -11px; }\n\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"top-start\"]:before,\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"bottom-start\"]:before {\n  left: 30px !important;\n  right: auto; }\n\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"top-start\"]:after,\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"bottom-start\"]:after {\n  left: 30px !important;\n  right: auto; }\n\n@media screen and (min-width: 768px) {\n  .navbar-form {\n    margin-top: 21px;\n    margin-bottom: 21px;\n    padding-left: 5px;\n    padding-right: 5px; }\n  .navbar-search-form {\n    display: none; }\n  .navbar-nav .dropdown-item .dropdown-menu,\n  .dropdown .dropdown-menu,\n  .dropdown-btn .dropdown-menu {\n    transform: translate3d(0px, -40px, 0px);\n    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s; }\n  .navbar-nav .dropdown-item.show .dropdown-menu,\n  .dropdown.show .dropdown-menu,\n  .dropdown-btn.show .dropdown-menu {\n    transform: translate3d(0px, 0px, 0px);\n    visibility: visible !important; }\n  .bootstrap-select .dropdown-menu {\n    -webkit-transition: all 150ms linear;\n    -moz-transition: all 150ms linear;\n    -o-transition: all 150ms linear;\n    -ms-transition: all 150ms linear;\n    transition: all 150ms linear; }\n  .bootstrap-datetimepicker-widget {\n    visibility: visible !important; }\n  .bootstrap-select .show .dropdown-menu {\n    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;\n    transform: translate3d(0px, 0px, 0px); }\n  .navbar-nav.navbar-right li .dropdown-menu:before,\n  .navbar-nav.navbar-right li .dropdown-menu:after {\n    left: auto;\n    right: 12px; }\n  .footer:not(.footer-big) nav ul li:first-child {\n    margin-left: 0; }\n  body > .navbar-collapse.collapse {\n    display: none !important; } }\n\n.dropdown-sharing .dropup-item {\n  color: #66615b;\n  font-size: 14px; }\n  .dropdown-sharing .dropup-item .social-line {\n    line-height: 28px;\n    padding: 10px 20px 5px 20px !important; }\n    .dropdown-sharing .dropup-item .social-line [class*=\"icon-\"] {\n      font-size: 20px; }\n  .dropdown-sharing .dropup-item:last-child {\n    margin: 0 13px;\n    display: block; }\n  .dropdown-sharing .dropup-item .btn {\n    margin: 10px; }\n\n.dropdown-sharing .dropup-item:hover .social-line,\n.dropdown-sharing .dropup-item:hover .action-line,\n.dropdown-sharing .dropup-item:focus .social-line,\n.dropdown-sharing .dropup-item:focus .action-line {\n  background-color: #FFFFFF;\n  color: #66615b;\n  opacity: 1;\n  text-decoration: none; }\n\n.show .dropdown-sharing,\n.show .dropdown-actions {\n  margin-bottom: 1px; }\n\n.dropdown-actions .dropdown-item {\n  margin: -15px 35px; }\n  .dropdown-actions .dropdown-item .action-line {\n    padding: 5px 10px;\n    line-height: 24px;\n    font-weight: bold; }\n    .dropdown-actions .dropdown-item .action-line [class*=\"icon-\"] {\n      font-size: 24px; }\n    .dropdown-actions .dropdown-item .action-line .col-sm-9 {\n      line-height: 34px; }\n  .dropdown-actions .dropdown-item .link-danger {\n    color: #ef8157; }\n    .dropdown-actions .dropdown-item .link-danger:hover, .dropdown-actions .dropdown-item .link-danger:active, .dropdown-actions .dropdown-item .link-danger:focus {\n      color: #ef8157; }\n\n.dropdown-actions li:hover a,\n.dropdown-actions li:focus a {\n  color: #66615b;\n  opacity: 1;\n  text-decoration: none; }\n\n.dropdown-actions .action-line .icon-simple {\n  margin-left: -15px; }\n\n.dropdown .dropdown-menu[x-placement=\"top-start\"]:before,\n.dropdown .dropdown-menu[x-placement=\"top-end\"]:before,\n.dropup .dropdown-menu:before {\n  border-top: 11px solid #DCD9D1;\n  border-left: 11px solid transparent;\n  border-right: 11px solid transparent;\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  right: 12px;\n  bottom: -12px; }\n\n.dropdown .dropdown-menu[x-placement=\"top-start\"]:after,\n.dropdown .dropdown-menu[x-placement=\"top-end\"]:after,\n.dropup .dropdown-menu:after {\n  border-top: 11px solid #FFF;\n  border-left: 11px solid transparent;\n  border-right: 11px solid transparent;\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  right: 12px;\n  bottom: -11px; }\n\n.dropup .dropdown-toggle:after,\n.dropdown .dropdown-toggle:after {\n  margin-left: 0; }\n\n.dropdown-notification .dropdown-notification-list .notification-item {\n  border-bottom: 1px solid #F1EAE0;\n  font-size: 16px;\n  color: #66615b; }\n  .dropdown-notification .dropdown-notification-list .notification-item .notification-text {\n    padding-left: 40px;\n    position: relative;\n    min-width: 330px;\n    min-height: 70px;\n    white-space: normal; }\n    .dropdown-notification .dropdown-notification-list .notification-item .notification-text .label {\n      display: block;\n      position: absolute;\n      top: 50%;\n      margin-top: -12px;\n      left: 7px; }\n    .dropdown-notification .dropdown-notification-list .notification-item .notification-text .message {\n      font-size: 0.9em;\n      line-height: 0.7;\n      margin-left: 10px; }\n    .dropdown-notification .dropdown-notification-list .notification-item .notification-text .time {\n      color: #9A9A9A;\n      font-size: 0.7em;\n      margin-left: 10px; }\n  .dropdown-notification .dropdown-notification-list .notification-item .read-notification {\n    font-size: 12px;\n    opacity: 0;\n    position: absolute;\n    right: 5px;\n    top: 50%;\n    margin-top: -12px; }\n  .dropdown-notification .dropdown-notification-list .notification-item:hover {\n    text-decoration: none; }\n    .dropdown-notification .dropdown-notification-list .notification-item:hover .notification-text {\n      color: #66615b;\n      background-color: #F0EFEB !important; }\n    .dropdown-notification .dropdown-notification-list .notification-item:hover .read-notification {\n      opacity: 1 !important; }\n\n.dropdown-notification .dropdown-footer {\n  background-color: #E8E7E3;\n  border-radius: 0 0 8px 8px; }\n  .dropdown-notification .dropdown-footer .dropdown-footer-menu {\n    list-style: outside none none;\n    padding: 0px 5px; }\n    .dropdown-notification .dropdown-footer .dropdown-footer-menu li {\n      display: inline-block;\n      text-align: left;\n      padding: 0 10px; }\n      .dropdown-notification .dropdown-footer .dropdown-footer-menu li a {\n        color: #9C9B99;\n        font-size: 0.9em;\n        line-height: 35px; }\n\n.alert {\n  border: 0;\n  border-radius: 3px;\n  color: #FFFFFF;\n  padding-top: .9rem;\n  padding-bottom: .9rem;\n  position: relative; }\n  .alert.alert-success {\n    background-color: #7ed6a5; }\n  .alert.alert-danger {\n    background-color: #f1926e; }\n  .alert.alert-warning {\n    background-color: #fccf71; }\n  .alert.alert-info {\n    background-color: #66c4de; }\n  .alert.alert-primary {\n    background-color: #65d1d4; }\n  .alert.alert-default {\n    background-color: #736e67; }\n  .alert .close {\n    color: #FFFFFF;\n    opacity: .9;\n    text-shadow: none;\n    line-height: 0;\n    outline: 0; }\n    .alert .close i.fa,\n    .alert .close i.nc-icon {\n      font-size: 14px !important; }\n    .alert .close:hover, .alert .close:focus {\n      opacity: 1; }\n  .alert span[data-notify=\"icon\"] {\n    font-size: 27px;\n    display: block;\n    left: 19px;\n    position: absolute;\n    top: 50%;\n    margin-top: -11px; }\n  .alert button.close {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    margin-top: -13px;\n    width: 25px;\n    height: 25px;\n    padding: 3px; }\n  .alert .close ~ span {\n    display: block;\n    max-width: 89%; }\n  .alert.alert-with-icon {\n    padding-left: 65px; }\n\nimg {\n  max-width: 100%;\n  border-radius: 3px; }\n\n.img-raised {\n  box-shadow: 0px 10px 25px 0px rgba(0, 0, 0, 0.3); }\n\n/*--------------------------------\n\nnucleo-icons Web Font - built using nucleoapp.com\nLicense - nucleoapp.com/license/\n\n-------------------------------- */\n@font-face {\n  font-family: 'nucleo-icons';\n  src: url(\"../fonts/nucleo-icons.eot\");\n  src: url(\"../fonts/nucleo-icons.eot\") format(\"embedded-opentype\"), url(\"../fonts/nucleo-icons.woff2\") format(\"woff2\"), url(\"../fonts/nucleo-icons.woff\") format(\"woff\"), url(\"../fonts/nucleo-icons.ttf\") format(\"truetype\"), url(\"../fonts/nucleo-icons.svg\") format(\"svg\");\n  font-weight: normal;\n  font-style: normal; }\n\n/*------------------------\n\tbase class definition\n-------------------------*/\n.nc-icon {\n  display: inline-block;\n  font: normal normal normal 14px/1 'nucleo-icons';\n  font-size: inherit;\n  speak: none;\n  text-transform: none;\n  /* Better Font Rendering */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale; }\n\n/*------------------------\n  change icon size\n-------------------------*/\n.nc-icon.lg {\n  font-size: 1.33333333em;\n  vertical-align: -16%; }\n\n.nc-icon.x2 {\n  font-size: 2em; }\n\n.nc-icon.x3 {\n  font-size: 3em; }\n\n/*----------------------------------\n  add a square/circle background\n-----------------------------------*/\n.nc-icon.square,\n.nc-icon.circle {\n  padding: 0.33333333em;\n  vertical-align: -16%;\n  background-color: #eee; }\n\n.nc-icon.circle {\n  border-radius: 50%; }\n\n/*------------------------\n  list icons\n-------------------------*/\n.nc-icon-ul {\n  padding-left: 0;\n  margin-left: 2.14285714em;\n  list-style-type: none; }\n\n.nc-icon-ul > li {\n  position: relative; }\n\n.nc-icon-ul > li > .nc-icon {\n  position: absolute;\n  left: -1.57142857em;\n  top: 0.14285714em;\n  text-align: center; }\n\n.nc-icon-ul > li > .nc-icon.lg {\n  top: 0;\n  left: -1.35714286em; }\n\n.nc-icon-ul > li > .nc-icon.circle,\n.nc-icon-ul > li > .nc-icon.square {\n  top: -0.19047619em;\n  left: -1.9047619em; }\n\n/*------------------------\n  spinning icons\n-------------------------*/\n.nc-icon.spin {\n  -webkit-animation: nc-icon-spin 2s infinite linear;\n  -moz-animation: nc-icon-spin 2s infinite linear;\n  animation: nc-icon-spin 2s infinite linear; }\n\n@-webkit-keyframes nc-icon-spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n@-moz-keyframes nc-icon-spin {\n  0% {\n    -moz-transform: rotate(0deg); }\n  100% {\n    -moz-transform: rotate(360deg); } }\n\n@keyframes nc-icon-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n    -moz-transform: rotate(0deg);\n    -ms-transform: rotate(0deg);\n    -o-transform: rotate(0deg);\n    transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg);\n    -moz-transform: rotate(360deg);\n    -ms-transform: rotate(360deg);\n    -o-transform: rotate(360deg);\n    transform: rotate(360deg); } }\n\n/*------------------------\n  rotated/flipped icons\n-------------------------*/\n.nc-icon.rotate-90 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);\n  -webkit-transform: rotate(90deg);\n  -moz-transform: rotate(90deg);\n  -ms-transform: rotate(90deg);\n  -o-transform: rotate(90deg);\n  transform: rotate(90deg); }\n\n.nc-icon.rotate-180 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n  -webkit-transform: rotate(180deg);\n  -moz-transform: rotate(180deg);\n  -ms-transform: rotate(180deg);\n  -o-transform: rotate(180deg);\n  transform: rotate(180deg); }\n\n.nc-icon.rotate-270 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);\n  -webkit-transform: rotate(270deg);\n  -moz-transform: rotate(270deg);\n  -ms-transform: rotate(270deg);\n  -o-transform: rotate(270deg);\n  transform: rotate(270deg); }\n\n.nc-icon.flip-y {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);\n  -webkit-transform: scale(-1, 1);\n  -moz-transform: scale(-1, 1);\n  -ms-transform: scale(-1, 1);\n  -o-transform: scale(-1, 1);\n  transform: scale(-1, 1); }\n\n.nc-icon.flip-x {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n  -webkit-transform: scale(1, -1);\n  -moz-transform: scale(1, -1);\n  -ms-transform: scale(1, -1);\n  -o-transform: scale(1, -1);\n  transform: scale(1, -1); }\n\n/*------------------------\n\tfont icons\n-------------------------*/\n.nc-air-baloon::before {\n  content: \"\\ea01\"; }\n\n.nc-album-2::before {\n  content: \"\\ea02\"; }\n\n.nc-alert-circle-i::before {\n  content: \"\\ea04\"; }\n\n.nc-align-center::before {\n  content: \"\\ea03\"; }\n\n.nc-align-left-2::before {\n  content: \"\\ea05\"; }\n\n.nc-ambulance::before {\n  content: \"\\ea06\"; }\n\n.nc-app::before {\n  content: \"\\ea07\"; }\n\n.nc-atom::before {\n  content: \"\\ea08\"; }\n\n.nc-badge::before {\n  content: \"\\ea09\"; }\n\n.nc-bag-16::before {\n  content: \"\\ea0a\"; }\n\n.nc-bank::before {\n  content: \"\\ea0b\"; }\n\n.nc-basket::before {\n  content: \"\\ea0c\"; }\n\n.nc-bell-55::before {\n  content: \"\\ea0d\"; }\n\n.nc-bold::before {\n  content: \"\\ea0e\"; }\n\n.nc-book-bookmark::before {\n  content: \"\\ea0f\"; }\n\n.nc-bookmark-2::before {\n  content: \"\\ea10\"; }\n\n.nc-box-2::before {\n  content: \"\\ea11\"; }\n\n.nc-box::before {\n  content: \"\\ea12\"; }\n\n.nc-briefcase-24::before {\n  content: \"\\ea13\"; }\n\n.nc-bulb-63::before {\n  content: \"\\ea14\"; }\n\n.nc-bullet-list-67::before {\n  content: \"\\ea15\"; }\n\n.nc-bus-front-12::before {\n  content: \"\\ea16\"; }\n\n.nc-button-pause::before {\n  content: \"\\ea17\"; }\n\n.nc-button-play::before {\n  content: \"\\ea18\"; }\n\n.nc-button-power::before {\n  content: \"\\ea19\"; }\n\n.nc-calendar-60::before {\n  content: \"\\ea1a\"; }\n\n.nc-camera-compact::before {\n  content: \"\\ea1b\"; }\n\n.nc-caps-small::before {\n  content: \"\\ea1c\"; }\n\n.nc-cart-simple::before {\n  content: \"\\ea1d\"; }\n\n.nc-chart-bar-32::before {\n  content: \"\\ea1e\"; }\n\n.nc-chart-pie-36::before {\n  content: \"\\ea1f\"; }\n\n.nc-chat-33::before {\n  content: \"\\ea20\"; }\n\n.nc-check-2::before {\n  content: \"\\ea21\"; }\n\n.nc-circle-10::before {\n  content: \"\\ea22\"; }\n\n.nc-cloud-download-93::before {\n  content: \"\\ea23\"; }\n\n.nc-cloud-upload-94::before {\n  content: \"\\ea24\"; }\n\n.nc-compass-05::before {\n  content: \"\\ea25\"; }\n\n.nc-controller-modern::before {\n  content: \"\\ea26\"; }\n\n.nc-credit-card::before {\n  content: \"\\ea27\"; }\n\n.nc-delivery-fast::before {\n  content: \"\\ea28\"; }\n\n.nc-diamond::before {\n  content: \"\\ea29\"; }\n\n.nc-email-85::before {\n  content: \"\\ea2a\"; }\n\n.nc-favourite-28::before {\n  content: \"\\ea2b\"; }\n\n.nc-glasses-2::before {\n  content: \"\\ea2c\"; }\n\n.nc-globe-2::before {\n  content: \"\\ea2d\"; }\n\n.nc-globe::before {\n  content: \"\\ea2e\"; }\n\n.nc-hat-3::before {\n  content: \"\\ea2f\"; }\n\n.nc-headphones::before {\n  content: \"\\ea30\"; }\n\n.nc-html5::before {\n  content: \"\\ea31\"; }\n\n.nc-image::before {\n  content: \"\\ea32\"; }\n\n.nc-istanbul::before {\n  content: \"\\ea33\"; }\n\n.nc-key-25::before {\n  content: \"\\ea34\"; }\n\n.nc-laptop::before {\n  content: \"\\ea35\"; }\n\n.nc-layout-11::before {\n  content: \"\\ea36\"; }\n\n.nc-lock-circle-open::before {\n  content: \"\\ea37\"; }\n\n.nc-map-big::before {\n  content: \"\\ea38\"; }\n\n.nc-minimal-down::before {\n  content: \"\\ea39\"; }\n\n.nc-minimal-left::before {\n  content: \"\\ea3a\"; }\n\n.nc-minimal-right::before {\n  content: \"\\ea3b\"; }\n\n.nc-minimal-up::before {\n  content: \"\\ea3c\"; }\n\n.nc-mobile::before {\n  content: \"\\ea3d\"; }\n\n.nc-money-coins::before {\n  content: \"\\ea3e\"; }\n\n.nc-note-03::before {\n  content: \"\\ea3f\"; }\n\n.nc-palette::before {\n  content: \"\\ea40\"; }\n\n.nc-paper::before {\n  content: \"\\ea41\"; }\n\n.nc-pin-3::before {\n  content: \"\\ea42\"; }\n\n.nc-planet::before {\n  content: \"\\ea43\"; }\n\n.nc-refresh-69::before {\n  content: \"\\ea44\"; }\n\n.nc-ruler-pencil::before {\n  content: \"\\ea45\"; }\n\n.nc-satisfied::before {\n  content: \"\\ea46\"; }\n\n.nc-scissors::before {\n  content: \"\\ea47\"; }\n\n.nc-send::before {\n  content: \"\\ea48\"; }\n\n.nc-settings-gear-65::before {\n  content: \"\\ea49\"; }\n\n.nc-settings::before {\n  content: \"\\ea4a\"; }\n\n.nc-share-66::before {\n  content: \"\\ea4b\"; }\n\n.nc-shop::before {\n  content: \"\\ea4c\"; }\n\n.nc-simple-add::before {\n  content: \"\\ea4d\"; }\n\n.nc-simple-delete::before {\n  content: \"\\ea4e\"; }\n\n.nc-simple-remove::before {\n  content: \"\\ea4f\"; }\n\n.nc-single-02::before {\n  content: \"\\ea50\"; }\n\n.nc-single-copy-04::before {\n  content: \"\\ea51\"; }\n\n.nc-sound-wave::before {\n  content: \"\\ea52\"; }\n\n.nc-spaceship::before {\n  content: \"\\ea53\"; }\n\n.nc-sun-fog-29::before {\n  content: \"\\ea54\"; }\n\n.nc-support-17::before {\n  content: \"\\ea55\"; }\n\n.nc-tablet-2::before {\n  content: \"\\ea56\"; }\n\n.nc-tag-content::before {\n  content: \"\\ea57\"; }\n\n.nc-tap-01::before {\n  content: \"\\ea58\"; }\n\n.nc-tie-bow::before {\n  content: \"\\ea59\"; }\n\n.nc-tile-56::before {\n  content: \"\\ea5a\"; }\n\n.nc-time-alarm::before {\n  content: \"\\ea5b\"; }\n\n.nc-touch-id::before {\n  content: \"\\ea5c\"; }\n\n.nc-trophy::before {\n  content: \"\\ea5d\"; }\n\n.nc-tv-2::before {\n  content: \"\\ea5e\"; }\n\n.nc-umbrella-13::before {\n  content: \"\\ea5f\"; }\n\n.nc-user-run::before {\n  content: \"\\ea60\"; }\n\n.nc-vector::before {\n  content: \"\\ea61\"; }\n\n.nc-watch-time::before {\n  content: \"\\ea62\"; }\n\n.nc-world-2::before {\n  content: \"\\ea63\"; }\n\n.nc-zoom-split::before {\n  content: \"\\ea64\"; }\n\n/* all icon font classes list here */\n.table .img-wrapper {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  overflow: hidden;\n  margin: 0 auto; }\n\n.table .img-row {\n  max-width: 60px;\n  width: 60px; }\n\n.table .form-check {\n  margin: 0; }\n  .table .form-check label .form-check-sign::before,\n  .table .form-check label .form-check-sign::after {\n    top: -17px;\n    left: 4px; }\n\n.table .btn {\n  margin: 0; }\n\n.table small, .table .small {\n  font-weight: 300; }\n\n.card-tasks .card-body .table {\n  margin-bottom: 0; }\n  .card-tasks .card-body .table > thead > tr > th,\n  .card-tasks .card-body .table > tbody > tr > th,\n  .card-tasks .card-body .table > tfoot > tr > th,\n  .card-tasks .card-body .table > thead > tr > td,\n  .card-tasks .card-body .table > tbody > tr > td,\n  .card-tasks .card-body .table > tfoot > tr > td {\n    padding-top: 0;\n    padding-bottom: 0; }\n\n.table > thead > tr > th {\n  font-size: 14px;\n  font-weight: 700;\n  padding-bottom: 0;\n  text-transform: uppercase;\n  border: 0; }\n\n.table .radio,\n.table .checkbox {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding: 0;\n  width: 15px; }\n  .table .radio .icons,\n  .table .checkbox .icons {\n    position: relative; }\n  .table .radio label:after, .table .radio label:before,\n  .table .checkbox label:after,\n  .table .checkbox label:before {\n    top: -17px;\n    left: -3px; }\n\n.table > thead > tr > th,\n.table > tbody > tr > th,\n.table > tfoot > tr > th,\n.table > thead > tr > td,\n.table > tbody > tr > td,\n.table > tfoot > tr > td {\n  padding: 12px 7px;\n  vertical-align: middle; }\n\n.table .th-description {\n  max-width: 150px; }\n\n.table .td-price {\n  font-size: 26px;\n  font-weight: 300;\n  margin-top: 5px;\n  position: relative;\n  top: 4px;\n  text-align: right; }\n\n.table .td-total {\n  font-weight: 700;\n  font-size: 1.57em;\n  padding-top: 20px;\n  text-align: right; }\n\n.table .td-actions .btn {\n  margin: 0px; }\n\n.table > tbody > tr {\n  position: relative; }\n\n.table-shopping > thead > tr > th {\n  font-size: 1em;\n  text-transform: uppercase; }\n\n.table-shopping > tbody > tr > td {\n  font-size: 1em; }\n  .table-shopping > tbody > tr > td b {\n    display: block;\n    margin-bottom: 5px; }\n\n.table-shopping .td-name {\n  font-weight: 400;\n  font-size: 1.5em; }\n  .table-shopping .td-name small {\n    color: #9A9A9A;\n    font-size: 0.75em;\n    font-weight: 300; }\n\n.table-shopping .td-number {\n  font-weight: 300;\n  font-size: 1.714em; }\n\n.table-shopping .td-name {\n  min-width: 200px; }\n\n.table-shopping .td-number {\n  text-align: right;\n  min-width: 170px; }\n  .table-shopping .td-number small {\n    margin-right: 3px; }\n\n.table-shopping .img-container {\n  width: 120px;\n  max-height: 160px;\n  overflow: hidden;\n  display: block; }\n  .table-shopping .img-container img {\n    width: 100%; }\n\n.table-responsive {\n  overflow: scroll;\n  padding-bottom: 10px; }\n\n#tables .table-responsive {\n  margin-bottom: 30px; }\n\n.table-hover > tbody > tr:hover {\n  background-color: #f5f5f5; }\n\n.wrapper {\n  position: relative;\n  top: 0;\n  height: 100vh; }\n  .wrapper.wrapper-full-page {\n    min-height: 100vh;\n    height: auto; }\n\n.sidebar,\n.off-canvas-sidebar {\n  position: fixed;\n  top: 0;\n  height: 100%;\n  bottom: 0;\n  width: 260px;\n  left: 0;\n  z-index: 1030;\n  border-right: 1px solid #ddd; }\n  .sidebar .sidebar-wrapper,\n  .off-canvas-sidebar .sidebar-wrapper {\n    position: relative;\n    height: calc(100vh - 75px);\n    overflow: auto;\n    width: 260px;\n    z-index: 4;\n    padding-bottom: 100px; }\n    .sidebar .sidebar-wrapper .dropdown .dropdown-backdrop,\n    .off-canvas-sidebar .sidebar-wrapper .dropdown .dropdown-backdrop {\n      display: none !important; }\n    .sidebar .sidebar-wrapper .navbar-form,\n    .off-canvas-sidebar .sidebar-wrapper .navbar-form {\n      border: none; }\n  .sidebar .navbar-minimize,\n  .off-canvas-sidebar .navbar-minimize {\n    position: absolute;\n    right: 20px;\n    top: 2px;\n    opacity: 1; }\n  .sidebar .logo-tim,\n  .off-canvas-sidebar .logo-tim {\n    border-radius: 50%;\n    border: 1px solid #333;\n    display: block;\n    height: 61px;\n    width: 61px;\n    float: left;\n    overflow: hidden; }\n    .sidebar .logo-tim img,\n    .off-canvas-sidebar .logo-tim img {\n      width: 60px;\n      height: 60px; }\n  .sidebar .nav,\n  .off-canvas-sidebar .nav {\n    margin-top: 20px;\n    display: block; }\n    .sidebar .nav .caret,\n    .off-canvas-sidebar .nav .caret {\n      top: 14px;\n      position: absolute;\n      right: 10px; }\n    .sidebar .nav li > a + div .nav li > a,\n    .off-canvas-sidebar .nav li > a + div .nav li > a {\n      margin-top: 7px; }\n    .sidebar .nav li > a,\n    .off-canvas-sidebar .nav li > a {\n      margin: 10px 15px 0;\n      color: #FFFFFF;\n      display: block;\n      text-decoration: none;\n      position: relative;\n      text-transform: uppercase;\n      cursor: pointer;\n      font-size: 12px;\n      padding: 10px 8px;\n      line-height: 30px;\n      opacity: .7; }\n    .sidebar .nav li .nav > li > a,\n    .off-canvas-sidebar .nav li .nav > li > a {\n      padding: 5px 8px; }\n    .sidebar .nav li.active > a,\n    .sidebar .nav li.active > a > i,\n    .off-canvas-sidebar .nav li.active > a,\n    .off-canvas-sidebar .nav li.active > a > i {\n      opacity: 1; }\n    .sidebar .nav li:hover:not(.active) > a,\n    .sidebar .nav li:focus:not(.active) > a,\n    .off-canvas-sidebar .nav li:hover:not(.active) > a,\n    .off-canvas-sidebar .nav li:focus:not(.active) > a {\n      opacity: 1; }\n    .sidebar .nav i,\n    .off-canvas-sidebar .nav i {\n      font-size: 24px;\n      float: left;\n      margin-right: 12px;\n      line-height: 30px;\n      width: 34px;\n      text-align: center;\n      color: rgba(255, 255, 255, 0.5);\n      position: relative; }\n    .sidebar .nav p,\n    .off-canvas-sidebar .nav p {\n      margin-bottom: 0; }\n    .sidebar .nav .collapse .nav,\n    .sidebar .nav .collapsing .nav,\n    .off-canvas-sidebar .nav .collapse .nav,\n    .off-canvas-sidebar .nav .collapsing .nav {\n      margin-top: 0; }\n  .sidebar .sidebar-background,\n  .off-canvas-sidebar .sidebar-background {\n    position: absolute;\n    z-index: 1;\n    height: 100%;\n    width: 100%;\n    display: block;\n    top: 0;\n    left: 0;\n    background-size: cover;\n    background-position: center center; }\n    .sidebar .sidebar-background:after,\n    .off-canvas-sidebar .sidebar-background:after {\n      position: absolute;\n      z-index: 3;\n      width: 100%;\n      height: 100%;\n      content: \"\";\n      display: block;\n      background: #FFFFFF;\n      opacity: 1; }\n  .sidebar .logo,\n  .off-canvas-sidebar .logo {\n    position: relative;\n    padding: 7px 0.7rem;\n    z-index: 4; }\n    .sidebar .logo a.logo-mini,\n    .off-canvas-sidebar .logo a.logo-mini {\n      opacity: 1;\n      float: left;\n      width: 34px;\n      text-align: center;\n      margin-left: 10px;\n      margin-right: 12px; }\n    .sidebar .logo a.logo-normal,\n    .off-canvas-sidebar .logo a.logo-normal {\n      display: block;\n      opacity: 1;\n      padding: 11px 0 8px;\n      -webkit-transform: translate3d(0px, 0, 0);\n      -moz-transform: translate3d(0px, 0, 0);\n      -o-transform: translate3d(0px, 0, 0);\n      -ms-transform: translate3d(0px, 0, 0);\n      transform: translate3d(0px, 0, 0); }\n    .sidebar .logo:after,\n    .off-canvas-sidebar .logo:after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      right: 15px;\n      height: 1px;\n      width: calc(100% - 30px);\n      background-color: rgba(255, 255, 255, 0.5); }\n    .sidebar .logo p,\n    .off-canvas-sidebar .logo p {\n      float: left;\n      font-size: 20px;\n      margin: 10px 10px;\n      color: #FFFFFF;\n      line-height: 20px;\n      font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; }\n    .sidebar .logo .simple-text,\n    .off-canvas-sidebar .logo .simple-text {\n      text-transform: uppercase;\n      padding: 0.5rem 0;\n      display: block;\n      white-space: nowrap;\n      font-size: 1rem;\n      color: #FFFFFF;\n      text-decoration: none;\n      font-weight: 400;\n      line-height: 30px;\n      overflow: hidden; }\n  .sidebar .logo-tim,\n  .off-canvas-sidebar .logo-tim {\n    border-radius: 50%;\n    border: 1px solid #333;\n    display: block;\n    height: 61px;\n    width: 61px;\n    float: left;\n    overflow: hidden; }\n    .sidebar .logo-tim img,\n    .off-canvas-sidebar .logo-tim img {\n      width: 60px;\n      height: 60px; }\n  .sidebar:before, .sidebar:after,\n  .off-canvas-sidebar:before,\n  .off-canvas-sidebar:after {\n    display: block;\n    content: \"\";\n    opacity: 1;\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0; }\n  .sidebar:after,\n  .off-canvas-sidebar:after {\n    background: #66615B;\n    background: -webkit-linear-gradient(#66615B 0%, #000 80%);\n    background: -o-linear-gradient(#66615B 0%, #000 80%);\n    background: -moz-linear-gradient(#66615B 0%, #000 80%);\n    background: linear-gradient(#66615B 0%, #000 80%);\n    z-index: 3; }\n  .sidebar[data-color=\"white\"]:after,\n  .off-canvas-sidebar[data-color=\"white\"]:after {\n    background: #FFFFFF; }\n  .sidebar[data-color=\"white\"] .nav li a,\n  .sidebar[data-color=\"white\"] .nav li a i,\n  .sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"],\n  .sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] i,\n  .sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] ~ div > ul > li .sidebar-mini-icon,\n  .sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] ~ div > ul > li > a,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a i,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] ~ div > ul > li .sidebar-mini-icon,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li a[data-toggle=\"collapse\"] ~ div > ul > li > a {\n    color: #66615B;\n    opacity: .7; }\n  .sidebar[data-color=\"white\"] .nav li:hover:not(.active) > a,\n  .sidebar[data-color=\"white\"] .nav li:focus:not(.active) > a,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li:hover:not(.active) > a,\n  .off-canvas-sidebar[data-color=\"white\"] .nav li:focus:not(.active) > a {\n    opacity: 1; }\n  .sidebar[data-color=\"white\"] .logo .simple-text,\n  .off-canvas-sidebar[data-color=\"white\"] .logo .simple-text {\n    color: #66615B; }\n  .sidebar[data-color=\"white\"] .logo:after,\n  .off-canvas-sidebar[data-color=\"white\"] .logo:after {\n    background-color: #66615B;\n    opacity: .4; }\n  .sidebar[data-color=\"white\"] .user .info a span,\n  .sidebar[data-color=\"white\"] .user .nav .sidebar-mini-icon,\n  .sidebar[data-color=\"white\"] .user .nav .sidebar-normal,\n  .off-canvas-sidebar[data-color=\"white\"] .user .info a span,\n  .off-canvas-sidebar[data-color=\"white\"] .user .nav .sidebar-mini-icon,\n  .off-canvas-sidebar[data-color=\"white\"] .user .nav .sidebar-normal {\n    color: #66615B !important; }\n  .sidebar[data-color=\"white\"] .user:after,\n  .off-canvas-sidebar[data-color=\"white\"] .user:after {\n    background-color: #66615B;\n    opacity: .4; }\n  .sidebar[data-color=\"black\"]:after,\n  .off-canvas-sidebar[data-color=\"black\"]:after {\n    background: #212120; }\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a,\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"primary\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #51cbce;\n    opacity: 1; }\n  .sidebar[data-active-color=\"info\"] .nav li.active > a,\n  .sidebar[data-active-color=\"info\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"info\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #51bcda;\n    opacity: 1; }\n  .sidebar[data-active-color=\"success\"] .nav li.active > a,\n  .sidebar[data-active-color=\"success\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"success\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #6bd098;\n    opacity: 1; }\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a,\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"warning\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #fbc658;\n    opacity: 1; }\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a,\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"danger\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #ef8157;\n    opacity: 1; }\n  .sidebar[data-active-color=\"default\"] .nav li.active > a,\n  .sidebar[data-active-color=\"default\"] .nav li.active > a i,\n  .sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a,\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a,\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a i,\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"],\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] i,\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n  .off-canvas-sidebar[data-active-color=\"default\"] .nav li.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n    color: #66615B;\n    opacity: 1; }\n\n.visible-on-sidebar-regular {\n  display: inline-block !important; }\n\n.visible-on-sidebar-mini {\n  display: none !important; }\n\n.off-canvas-sidebar .nav > li > a,\n.off-canvas-sidebar .nav > li > a:hover {\n  color: #FFFFFF; }\n\n.off-canvas-sidebar .nav > li > a:focus {\n  background: rgba(200, 200, 200, 0.2); }\n\n.main-panel {\n  position: relative;\n  float: right;\n  width: calc(100% - 260px);\n  background-color: #f4f3ef;\n  -webkit-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n  -moz-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n  -o-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n  -ms-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n  transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1); }\n  .main-panel > .content {\n    padding: 0 30px 30px;\n    min-height: calc(100vh - px);\n    margin-top: 93px; }\n  .main-panel > .navbar {\n    margin-bottom: 0; }\n  .main-panel .header {\n    margin-bottom: 50px; }\n\n.perfect-scrollbar-on .sidebar,\n.perfect-scrollbar-on .main-panel {\n  height: 100%;\n  max-height: 100%; }\n\n.panel-header {\n  height: 260px;\n  padding-top: 80px;\n  padding-bottom: 45px;\n  background: #141E30;\n  /* fallback for old browsers */\n  background: -webkit-gradient(linear, left top, right top, from(#0c2646), color-stop(60%, #204065), to(#2a5788));\n  background: linear-gradient(to right, #0c2646 0%, #204065 60%, #2a5788 100%);\n  position: relative;\n  overflow: hidden; }\n  .panel-header .header .title {\n    color: #FFFFFF; }\n  .panel-header .header .category {\n    max-width: 600px;\n    color: rgba(255, 255, 255, 0.5);\n    margin: 0 auto;\n    font-size: 13px; }\n    .panel-header .header .category a {\n      color: #FFFFFF; }\n\n.panel-header-sm {\n  height: 135px; }\n\n.panel-header-lg {\n  height: 380px; }\n\n.footer {\n  padding: 24px 0; }\n  .footer.footer-default {\n    background-color: #f2f2f2; }\n  .footer nav {\n    display: inline-block;\n    float: left;\n    padding-left: 0; }\n  .footer ul {\n    margin-bottom: 0;\n    padding: 0;\n    list-style: none; }\n    .footer ul li {\n      display: inline-block; }\n      .footer ul li a {\n        color: inherit;\n        padding: 0.5rem;\n        font-size: 0.8571em;\n        text-transform: uppercase;\n        text-decoration: none; }\n        .footer ul li a:hover {\n          text-decoration: none; }\n  .footer .copyright {\n    font-size: 0.8571em;\n    line-height: 1.8; }\n  .footer:after {\n    display: table;\n    clear: both;\n    content: \" \"; }\n\n.fixed-plugin {\n  position: fixed;\n  right: 0;\n  width: 64px;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 1031;\n  border-radius: 8px 0 0 8px;\n  text-align: center;\n  top: 120px; }\n  .fixed-plugin li > a,\n  .fixed-plugin .badge {\n    transition: all .34s;\n    -webkit-transition: all .34s;\n    -moz-transition: all .34s; }\n  .fixed-plugin .fa-cog {\n    color: #FFFFFF;\n    padding: 10px;\n    border-radius: 0 0 6px 6px;\n    width: auto; }\n  .fixed-plugin .dropdown-menu {\n    right: 80px;\n    left: auto !important;\n    top: -52px !important;\n    width: 290px;\n    border-radius: 10px;\n    padding: 0 10px; }\n  .fixed-plugin .dropdown .dropdown-menu .nc-icon {\n    top: 2px;\n    right: 10px;\n    font-size: 14px; }\n  .fixed-plugin .dropdown-menu:after,\n  .fixed-plugin .dropdown-menu:before {\n    right: 10px;\n    margin-left: auto;\n    left: auto; }\n  .fixed-plugin .fa-circle-thin {\n    color: #FFFFFF; }\n  .fixed-plugin .active .fa-circle-thin {\n    color: #00bbff; }\n  .fixed-plugin .dropdown-menu > .active > a,\n  .fixed-plugin .dropdown-menu > .active > a:hover,\n  .fixed-plugin .dropdown-menu > .active > a:focus {\n    color: #777777;\n    text-align: center; }\n  .fixed-plugin img {\n    border-radius: 0;\n    width: 100%;\n    height: 100px;\n    margin: 0 auto; }\n  .fixed-plugin .dropdown-menu li > a:hover,\n  .fixed-plugin .dropdown-menu li > a:focus {\n    box-shadow: none; }\n  .fixed-plugin .badge {\n    border: 3px solid #FFFFFF;\n    border-radius: 50%;\n    cursor: pointer;\n    display: inline-block;\n    height: 23px;\n    margin-right: 5px;\n    position: relative;\n    width: 23px; }\n    .fixed-plugin .badge.badge-light {\n      border: 1px solid #E3E3E3; }\n      .fixed-plugin .badge.badge-light.active, .fixed-plugin .badge.badge-light:hover {\n        border: 3px solid #0bf; }\n  .fixed-plugin .badge.active,\n  .fixed-plugin .badge:hover {\n    border-color: #00bbff; }\n  .fixed-plugin .badge-blue {\n    background-color: #51bcda; }\n  .fixed-plugin .badge-green {\n    background-color: #6bd098; }\n  .fixed-plugin .badge-orange {\n    background-color: #51cbce; }\n  .fixed-plugin .badge-yellow {\n    background-color: #fbc658; }\n  .fixed-plugin .badge-red {\n    background-color: #ef8157; }\n  .fixed-plugin h5 {\n    font-size: 14px;\n    margin: 10px; }\n  .fixed-plugin .dropdown-menu li {\n    display: block;\n    padding: 15px 2px;\n    width: 25%;\n    float: left; }\n  .fixed-plugin li.adjustments-line,\n  .fixed-plugin li.header-title,\n  .fixed-plugin li.button-container {\n    width: 100%;\n    height: 35px;\n    min-height: inherit; }\n  .fixed-plugin li.button-container {\n    height: auto; }\n    .fixed-plugin li.button-container div {\n      margin-bottom: 5px; }\n  .fixed-plugin #sharrreTitle {\n    text-align: center;\n    padding: 10px 0;\n    height: 50px; }\n  .fixed-plugin li.header-title {\n    height: 30px;\n    line-height: 25px;\n    font-size: 12px;\n    font-weight: 600;\n    text-align: center;\n    text-transform: uppercase; }\n  .fixed-plugin .adjustments-line p {\n    float: left;\n    display: inline-block;\n    margin-bottom: 0;\n    font-size: 1em;\n    color: #3C4858; }\n  .fixed-plugin .adjustments-line a {\n    color: transparent; }\n    .fixed-plugin .adjustments-line a .badge-colors {\n      position: relative;\n      top: -2px; }\n    .fixed-plugin .adjustments-line a a:hover,\n    .fixed-plugin .adjustments-line a a:focus {\n      color: transparent; }\n  .fixed-plugin .adjustments-line .togglebutton {\n    text-align: center; }\n    .fixed-plugin .adjustments-line .togglebutton .label-switch {\n      position: relative;\n      left: -10px;\n      font-size: 0.7142em;\n      color: #66615B; }\n      .fixed-plugin .adjustments-line .togglebutton .label-switch.label-right {\n        left: 10px; }\n    .fixed-plugin .adjustments-line .togglebutton .toggle {\n      margin-right: 0; }\n  .fixed-plugin .adjustments-line .dropdown-menu > li.adjustments-line > a {\n    padding-right: 0;\n    padding-left: 0;\n    border-bottom: 1px solid #ddd;\n    border-radius: 0;\n    margin: 0; }\n  .fixed-plugin .dropdown-menu > li > a.img-holder {\n    font-size: 16px;\n    text-align: center;\n    border-radius: 10px;\n    background-color: #FFF;\n    border: 3px solid #FFF;\n    padding-left: 0;\n    padding-right: 0;\n    opacity: 1;\n    cursor: pointer;\n    display: block;\n    max-height: 100px;\n    overflow: hidden;\n    padding: 0; }\n    .fixed-plugin .dropdown-menu > li > a.img-holder img {\n      margin-top: auto; }\n  .fixed-plugin .dropdown-menu > li a.switch-trigger:hover,\n  .fixed-plugin .dropdown-menu > li > a.switch-trigger:focus {\n    background-color: transparent; }\n  .fixed-plugin .dropdown-menu > li:hover > a.img-holder, .fixed-plugin .dropdown-menu > li:focus > a.img-holder {\n    border-color: rgba(0, 187, 255, 0.53); }\n  .fixed-plugin .dropdown-menu > .active > a.img-holder,\n  .fixed-plugin .dropdown-menu > .active > a.img-holder {\n    border-color: #00bbff;\n    background-color: #FFFFFF; }\n  .fixed-plugin .btn-social {\n    width: 50%;\n    display: block;\n    width: 48%;\n    float: left;\n    font-weight: 600; }\n  .fixed-plugin .btn-social i {\n    margin-right: 5px; }\n  .fixed-plugin .btn-social:first-child {\n    margin-right: 2%; }\n  .fixed-plugin .dropdown .dropdown-menu {\n    transform-origin: 0 0; }\n    .fixed-plugin .dropdown .dropdown-menu:before {\n      border-bottom: 16px solid rgba(0, 0, 0, 0);\n      border-left: 16px solid rgba(0, 0, 0, 0.2);\n      border-top: 16px solid rgba(0, 0, 0, 0);\n      right: -27px;\n      bottom: 425px; }\n    .fixed-plugin .dropdown .dropdown-menu:after {\n      border-bottom: 16px solid rgba(0, 0, 0, 0);\n      border-left: 16px solid #FFFFFF;\n      border-top: 16px solid rgba(0, 0, 0, 0);\n      right: -26px;\n      bottom: 425px; }\n    .fixed-plugin .dropdown .dropdown-menu:before, .fixed-plugin .dropdown .dropdown-menu:after {\n      content: \"\";\n      display: inline-block;\n      position: absolute;\n      width: 16px;\n      transform: translateY(-50px);\n      -webkit-transform: translateY(-50px);\n      -moz-transform: translateY(-50px); }\n  .fixed-plugin .dropdown.show-dropdown .show .dropdown-menu .show {\n    transform: translate3d(0, -60px, 0) !important;\n    bottom: auto !important;\n    top: 0 !important; }\n  .fixed-plugin .bootstrap-switch {\n    margin: 0; }\n\n.fixed-plugin .show-dropdown .dropdown-menu[x-placement=bottom-start] {\n  -webkit-transform: translate3d(0, -100px, 0) !important;\n  -moz-transform: translate3d(0, -100px, 0) !important;\n  -o-transform: translate3d(0, -100px, 0) !important;\n  -ms-transform: translate3d(0, -100px, 0) !important;\n  transform: translate3d(0, -100px, 0) !important; }\n  .fixed-plugin .show-dropdown .dropdown-menu[x-placement=bottom-start]:before, .fixed-plugin .show-dropdown .dropdown-menu[x-placement=bottom-start]:after {\n    top: 100px; }\n\n.fixed-plugin .show-dropdown .dropdown-menu[x-placement=top-start] {\n  -webkit-transform: translate3d(0, 100px, 0) !important;\n  -moz-transform: translate3d(0, 100px, 0) !important;\n  -o-transform: translate3d(0, 100px, 0) !important;\n  -ms-transform: translate3d(0, 100px, 0) !important;\n  transform: translate3d(0, 100px, 0) !important; }\n\n.fixed-plugin .show-dropdown.show .dropdown-menu.show[x-placement=bottom-start] {\n  -webkit-transform: translate3d(0, -60px, 0) !important;\n  -moz-transform: translate3d(0, -60px, 0) !important;\n  -o-transform: translate3d(0, -60px, 0) !important;\n  -ms-transform: translate3d(0, -60px, 0) !important;\n  transform: translate3d(0, -60px, 0) !important; }\n\n.fixed-plugin .show-dropdown.show .dropdown-menu.show[x-placement=top-start] {\n  -webkit-transform: translate3d(0, 470px, 0) !important;\n  -moz-transform: translate3d(0, 470px, 0) !important;\n  -o-transform: translate3d(0, 470px, 0) !important;\n  -ms-transform: translate3d(0, 470px, 0) !important;\n  transform: translate3d(0, 470px, 0) !important; }\n\n.card {\n  border-radius: 12px;\n  box-shadow: 0 6px 10px -4px rgba(0, 0, 0, 0.15);\n  background-color: #FFFFFF;\n  color: #252422;\n  margin-bottom: 20px;\n  position: relative;\n  border: 0 none;\n  -webkit-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -moz-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -o-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -ms-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease; }\n  .card .card-body {\n    padding: 15px 15px 10px 15px; }\n    .card .card-body.table-full-width {\n      padding-left: 0;\n      padding-right: 0; }\n  .card .card-header {\n    padding: 15px 15px 0;\n    border: 0; }\n    .card .card-header:not([data-background-color]) {\n      background-color: transparent; }\n    .card .card-header .card-title {\n      margin-top: 10px; }\n  .card .map {\n    border-radius: 3px; }\n    .card .map.map-big {\n      height: 500px; }\n  .card[data-background-color=\"orange\"] {\n    background-color: #51cbce; }\n    .card[data-background-color=\"orange\"] .card-header {\n      background-color: #51cbce; }\n    .card[data-background-color=\"orange\"] .card-footer .stats {\n      color: #FFFFFF; }\n  .card[data-background-color=\"red\"] {\n    background-color: #ef8157; }\n  .card[data-background-color=\"yellow\"] {\n    background-color: #fbc658; }\n  .card[data-background-color=\"blue\"] {\n    background-color: #51bcda; }\n  .card[data-background-color=\"green\"] {\n    background-color: #6bd098; }\n  .card .image {\n    overflow: hidden;\n    height: 200px;\n    position: relative; }\n  .card .avatar {\n    width: 30px;\n    height: 30px;\n    overflow: hidden;\n    border-radius: 50%;\n    margin-bottom: 15px; }\n  .card .numbers {\n    font-size: 2em; }\n  .card .big-title {\n    font-size: 12px;\n    text-align: center;\n    font-weight: 500;\n    padding-bottom: 15px; }\n  .card label {\n    font-size: 0.8571em;\n    margin-bottom: 5px;\n    color: #9A9A9A; }\n  .card .card-footer {\n    background-color: transparent;\n    border: 0; }\n    .card .card-footer .stats i {\n      margin-right: 5px;\n      position: relative;\n      top: 0px;\n      color: #66615B; }\n    .card .card-footer .btn {\n      margin: 0; }\n  .card.card-plain {\n    background-color: transparent;\n    box-shadow: none;\n    border-radius: 0; }\n    .card.card-plain .card-body {\n      padding-left: 5px;\n      padding-right: 5px; }\n    .card.card-plain img {\n      border-radius: 12px; }\n\n.card-plain {\n  background: transparent;\n  box-shadow: none; }\n  .card-plain .card-header,\n  .card-plain .card-footer {\n    margin-left: 0;\n    margin-right: 0;\n    background-color: transparent; }\n  .card-plain:not(.card-subcategories).card-body {\n    padding-left: 0;\n    padding-right: 0; }\n\n.card-chart .card-header .card-title {\n  margin-top: 10px;\n  margin-bottom: 0; }\n\n.card-chart .card-header .card-category {\n  margin-bottom: 5px; }\n\n.card-chart .table {\n  margin-bottom: 0; }\n  .card-chart .table td {\n    border-top: none;\n    border-bottom: 1px solid #e9ecef; }\n\n.card-chart .card-progress {\n  margin-top: 30px; }\n\n.card-chart .chart-area {\n  height: 190px;\n  width: calc(100% + 30px);\n  margin-left: -15px;\n  margin-right: -15px; }\n\n.card-chart .card-footer {\n  margin-top: 15px; }\n  .card-chart .card-footer .stats {\n    color: #9A9A9A; }\n\n.card-chart .dropdown {\n  position: absolute;\n  right: 20px;\n  top: 20px; }\n  .card-chart .dropdown .btn {\n    margin: 0; }\n\n.card-user .image {\n  height: 130px; }\n  .card-user .image img {\n    border-radius: 12px; }\n\n.card-user .author {\n  text-align: center;\n  text-transform: none;\n  margin-top: -77px; }\n  .card-user .author a + p.description {\n    margin-top: -7px; }\n\n.card-user .avatar {\n  width: 124px;\n  height: 124px;\n  border: 1px solid #FFFFFF;\n  position: relative; }\n\n.card-user .card-body {\n  min-height: 240px; }\n\n.card-user hr {\n  margin: 5px 15px 15px; }\n\n.card-user .card-body + .card-footer {\n  padding-top: 0; }\n\n.card-user .card-footer h5 {\n  font-size: 1.25em;\n  margin-bottom: 0; }\n\n.card-user .button-container {\n  margin-bottom: 6px;\n  text-align: center; }\n\n.map {\n  height: 500px; }\n\n.card-stats .card-body {\n  padding: 15px 15px 0px; }\n  .card-stats .card-body .numbers {\n    text-align: right;\n    font-size: 2em; }\n    .card-stats .card-body .numbers p {\n      margin-bottom: 0; }\n    .card-stats .card-body .numbers .card-category {\n      color: #9A9A9A;\n      font-size: 16px;\n      line-height: 1.4em; }\n\n.card-stats .card-footer {\n  padding: 0px 15px 15px; }\n  .card-stats .card-footer .stats {\n    color: #9A9A9A; }\n  .card-stats .card-footer hr {\n    margin-top: 10px;\n    margin-bottom: 15px; }\n\n.card-stats .icon-big {\n  font-size: 3em;\n  min-height: 64px; }\n  .card-stats .icon-big i {\n    line-height: 59px; }\n\n@media screen and (max-width: 991px) {\n  .navbar {\n    padding: 0; }\n    .navbar.navbar-absolute {\n      padding-top: 0; }\n    .navbar .navbar-brand {\n      font-size: 16px;\n      margin-right: 0; }\n  .profile-photo .profile-photo-small {\n    margin-left: -2px; }\n  .button-dropdown {\n    display: none; }\n  #minimizeSidebar {\n    display: none; }\n  .navbar .container-fluid {\n    padding-right: 15px;\n    padding-left: 15px; }\n  .navbar .navbar-collapse .input-group {\n    margin: 0;\n    margin-top: 5px; }\n  .navbar .navbar-nav .nav-item:first-child {\n    margin-top: 10px; }\n  .navbar .navbar-nav .nav-item:not(:last-child) {\n    margin-bottom: 10px; }\n  .navbar .dropdown.show .dropdown-menu {\n    display: block; }\n  .navbar .dropdown .dropdown-menu {\n    display: none; }\n  .navbar .dropdown.show .dropdown-menu,\n  .navbar .dropdown .dropdown-menu {\n    border: 0;\n    transition: none;\n    -webkit-box-shadow: none;\n    width: auto;\n    margin: 0px 1rem;\n    margin-top: 0px;\n    box-shadow: none;\n    position: static;\n    padding-left: 10px; }\n    .navbar .dropdown.show .dropdown-menu:before,\n    .navbar .dropdown .dropdown-menu:before {\n      display: none; }\n  .navbar .dropdown-menu .dropdown-item:focus,\n  .navbar .dropdown-menu .dropdown-item:hover {\n    color: #FFFFFF; }\n  .navbar.bg-white .dropdown-menu .dropdown-item:focus,\n  .navbar.bg-white .dropdown-menu .dropdown-item:hover {\n    color: #66615B; }\n  .navbar .navbar-toggler-bar {\n    display: block;\n    position: relative;\n    width: 22px;\n    height: 1px;\n    border-radius: 1px;\n    background: #66615B; }\n    .navbar .navbar-toggler-bar + .navbar-toggler-bar {\n      margin-top: 7px; }\n    .navbar .navbar-toggler-bar + .navbar-toggler-bar.navbar-kebab {\n      margin-top: 3px; }\n    .navbar .navbar-toggler-bar.bar2 {\n      width: 17px;\n      transition: width .2s linear; }\n  .navbar.bg-white:not(.navbar-transparent) .navbar-toggler-bar {\n    background-color: #66615B; }\n  .navbar .toggled .navbar-toggler-bar {\n    width: 24px; }\n    .navbar .toggled .navbar-toggler-bar + .navbar-toggler-bar {\n      margin-top: 5px; }\n  .wrapper {\n    -webkit-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -moz-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -o-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -ms-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1); }\n  .nav-open .main-panel {\n    right: 0;\n    -webkit-transform: translate3d(260px, 0, 0);\n    -moz-transform: translate3d(260px, 0, 0);\n    -o-transform: translate3d(260px, 0, 0);\n    -ms-transform: translate3d(260px, 0, 0);\n    transform: translate3d(260px, 0, 0); }\n  .nav-open .sidebar {\n    -webkit-transform: translate3d(0px, 0, 0);\n    -moz-transform: translate3d(0px, 0, 0);\n    -o-transform: translate3d(0px, 0, 0);\n    -ms-transform: translate3d(0px, 0, 0);\n    transform: translate3d(0px, 0, 0); }\n  .nav-open body {\n    position: relative;\n    overflow-x: hidden; }\n  .nav-open .menu-on-right .main-panel {\n    -webkit-transform: translate3d(-260px, 0, 0);\n    -moz-transform: translate3d(-260px, 0, 0);\n    -o-transform: translate3d(-260px, 0, 0);\n    -ms-transform: translate3d(-260px, 0, 0);\n    transform: translate3d(-260px, 0, 0); }\n  .nav-open .menu-on-right .navbar-collapse,\n  .nav-open .menu-on-right .sidebar {\n    -webkit-transform: translate3d(0px, 0, 0);\n    -moz-transform: translate3d(0px, 0, 0);\n    -o-transform: translate3d(0px, 0, 0);\n    -ms-transform: translate3d(0px, 0, 0);\n    transform: translate3d(0px, 0, 0); }\n  .nav-open .menu-on-right .navbar-translate {\n    -webkit-transform: translate3d(-300px, 0, 0);\n    -moz-transform: translate3d(-300px, 0, 0);\n    -o-transform: translate3d(-300px, 0, 0);\n    -ms-transform: translate3d(-300px, 0, 0);\n    transform: translate3d(-300px, 0, 0); }\n  .nav-open .menu-on-right #bodyClick {\n    right: 260px;\n    left: auto; }\n  .menu-on-right .sidebar {\n    left: auto;\n    right: 0;\n    -webkit-transform: translate3d(260px, 0, 0);\n    -moz-transform: translate3d(260px, 0, 0);\n    -o-transform: translate3d(260px, 0, 0);\n    -ms-transform: translate3d(260px, 0, 0);\n    transform: translate3d(260px, 0, 0); }\n  .bar1,\n  .bar2,\n  .bar3 {\n    outline: 1px solid transparent; }\n  .bar1 {\n    top: 0px;\n    -webkit-animation: topbar-back 500ms linear 0s;\n    -moz-animation: topbar-back 500ms linear 0s;\n    animation: topbar-back 500ms 0s;\n    -webkit-animation-fill-mode: forwards;\n    -moz-animation-fill-mode: forwards;\n    animation-fill-mode: forwards; }\n  .bar2 {\n    opacity: 1; }\n  .bar3 {\n    bottom: 0px;\n    -webkit-animation: bottombar-back 500ms linear 0s;\n    -moz-animation: bottombar-back 500ms linear 0s;\n    animation: bottombar-back 500ms 0s;\n    -webkit-animation-fill-mode: forwards;\n    -moz-animation-fill-mode: forwards;\n    animation-fill-mode: forwards; }\n  .toggled .bar1 {\n    top: 6px;\n    -webkit-animation: topbar-x 500ms linear 0s;\n    -moz-animation: topbar-x 500ms linear 0s;\n    animation: topbar-x 500ms 0s;\n    -webkit-animation-fill-mode: forwards;\n    -moz-animation-fill-mode: forwards;\n    animation-fill-mode: forwards; }\n  .toggled .bar2 {\n    opacity: 0; }\n  .toggled .bar3 {\n    bottom: 6px;\n    -webkit-animation: bottombar-x 500ms linear 0s;\n    -moz-animation: bottombar-x 500ms linear 0s;\n    animation: bottombar-x 500ms 0s;\n    -webkit-animation-fill-mode: forwards;\n    -moz-animation-fill-mode: forwards;\n    animation-fill-mode: forwards; }\n  @keyframes topbar-x {\n    0% {\n      top: 0px;\n      transform: rotate(0deg); }\n    45% {\n      top: 6px;\n      transform: rotate(145deg); }\n    75% {\n      transform: rotate(130deg); }\n    100% {\n      transform: rotate(135deg); } }\n  @-webkit-keyframes topbar-x {\n    0% {\n      top: 0px;\n      -webkit-transform: rotate(0deg); }\n    45% {\n      top: 6px;\n      -webkit-transform: rotate(145deg); }\n    75% {\n      -webkit-transform: rotate(130deg); }\n    100% {\n      -webkit-transform: rotate(135deg); } }\n  @-moz-keyframes topbar-x {\n    0% {\n      top: 0px;\n      -moz-transform: rotate(0deg); }\n    45% {\n      top: 6px;\n      -moz-transform: rotate(145deg); }\n    75% {\n      -moz-transform: rotate(130deg); }\n    100% {\n      -moz-transform: rotate(135deg); } }\n  @keyframes topbar-back {\n    0% {\n      top: 6px;\n      transform: rotate(135deg); }\n    45% {\n      transform: rotate(-10deg); }\n    75% {\n      transform: rotate(5deg); }\n    100% {\n      top: 0px;\n      transform: rotate(0); } }\n  @-webkit-keyframes topbar-back {\n    0% {\n      top: 6px;\n      -webkit-transform: rotate(135deg); }\n    45% {\n      -webkit-transform: rotate(-10deg); }\n    75% {\n      -webkit-transform: rotate(5deg); }\n    100% {\n      top: 0px;\n      -webkit-transform: rotate(0); } }\n  @-moz-keyframes topbar-back {\n    0% {\n      top: 6px;\n      -moz-transform: rotate(135deg); }\n    45% {\n      -moz-transform: rotate(-10deg); }\n    75% {\n      -moz-transform: rotate(5deg); }\n    100% {\n      top: 0px;\n      -moz-transform: rotate(0); } }\n  @keyframes bottombar-x {\n    0% {\n      bottom: 0px;\n      transform: rotate(0deg); }\n    45% {\n      bottom: 6px;\n      transform: rotate(-145deg); }\n    75% {\n      transform: rotate(-130deg); }\n    100% {\n      transform: rotate(-135deg); } }\n  @-webkit-keyframes bottombar-x {\n    0% {\n      bottom: 0px;\n      -webkit-transform: rotate(0deg); }\n    45% {\n      bottom: 6px;\n      -webkit-transform: rotate(-145deg); }\n    75% {\n      -webkit-transform: rotate(-130deg); }\n    100% {\n      -webkit-transform: rotate(-135deg); } }\n  @-moz-keyframes bottombar-x {\n    0% {\n      bottom: 0px;\n      -moz-transform: rotate(0deg); }\n    45% {\n      bottom: 6px;\n      -moz-transform: rotate(-145deg); }\n    75% {\n      -moz-transform: rotate(-130deg); }\n    100% {\n      -moz-transform: rotate(-135deg); } }\n  @keyframes bottombar-back {\n    0% {\n      bottom: 6px;\n      transform: rotate(-135deg); }\n    45% {\n      transform: rotate(10deg); }\n    75% {\n      transform: rotate(-5deg); }\n    100% {\n      bottom: 0px;\n      transform: rotate(0); } }\n  @-webkit-keyframes bottombar-back {\n    0% {\n      bottom: 6px;\n      -webkit-transform: rotate(-135deg); }\n    45% {\n      -webkit-transform: rotate(10deg); }\n    75% {\n      -webkit-transform: rotate(-5deg); }\n    100% {\n      bottom: 0px;\n      -webkit-transform: rotate(0); } }\n  @-moz-keyframes bottombar-back {\n    0% {\n      bottom: 6px;\n      -moz-transform: rotate(-135deg); }\n    45% {\n      -moz-transform: rotate(10deg); }\n    75% {\n      -moz-transform: rotate(-5deg); }\n    100% {\n      bottom: 0px;\n      -moz-transform: rotate(0); } }\n  @-webkit-keyframes fadeIn {\n    0% {\n      opacity: 0; }\n    100% {\n      opacity: 1; } }\n  @-moz-keyframes fadeIn {\n    0% {\n      opacity: 0; }\n    100% {\n      opacity: 1; } }\n  @keyframes fadeIn {\n    0% {\n      opacity: 0; }\n    100% {\n      opacity: 1; } }\n  #bodyClick {\n    height: 100%;\n    width: 100%;\n    position: fixed;\n    opacity: 1;\n    top: 0;\n    right: 0;\n    left: 260px;\n    content: \"\";\n    z-index: 9999;\n    overflow-x: hidden;\n    background-color: transparent;\n    -webkit-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -moz-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -o-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -ms-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1); }\n  .footer .copyright {\n    text-align: right; }\n  .section-nucleo-icons .icons-container {\n    margin-top: 65px; }\n  .navbar-nav .nav-link i.fa,\n  .navbar-nav .nav-link i.nc-icon {\n    opacity: .5; }\n  .sidebar,\n  .bootstrap-navbar {\n    position: fixed;\n    display: block;\n    top: 0;\n    height: 100%;\n    width: 260px;\n    right: auto;\n    left: 0;\n    z-index: 1032;\n    visibility: visible;\n    overflow-y: visible;\n    padding: 0;\n    -webkit-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -moz-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -o-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -ms-transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    transition: all 0.5s cubic-bezier(0.685, 0.0473, 0.346, 1);\n    -webkit-transform: translate3d(-260px, 0, 0);\n    -moz-transform: translate3d(-260px, 0, 0);\n    -o-transform: translate3d(-260px, 0, 0);\n    -ms-transform: translate3d(-260px, 0, 0);\n    transform: translate3d(-260px, 0, 0); }\n  .main-panel {\n    width: 100%; }\n  .timeline:before {\n    left: 5% !important; }\n  .timeline > li > .timeline-badge {\n    left: 5% !important; }\n  .timeline > li > .timeline-panel {\n    float: right !important;\n    width: 82% !important; }\n    .timeline > li > .timeline-panel:before {\n      border-left-width: 0 !important;\n      border-right-width: 15px !important;\n      left: -15px !important;\n      right: auto !important; }\n    .timeline > li > .timeline-panel:after {\n      border-left-width: 0 !important;\n      border-right-width: 14px !important;\n      left: -14px !important;\n      right: auto !important; } }\n\n@media (max-width: 991px) and (min-width: 768px) {\n  .nav-tabs-navigation.verical-navs {\n    padding: 0px 2px; } }\n\n@media screen and (min-width: 768px) {\n  .footer .footer-nav {\n    padding-left: 21px; }\n  .footer .credits {\n    padding-right: 15px; } }\n\n@media screen and (min-width: 992px) {\n  .navbar-collapse {\n    background: none !important; }\n  .navbar .navbar-toggle {\n    display: none; }\n  .navbar-nav .nav-link.profile-photo {\n    padding: 0;\n    margin: 7px 0.7rem; }\n  .section-nucleo-icons .icons-container {\n    margin: 0 0 0 auto; }\n  .dropdown-menu .dropdown-item {\n    color: inherit; }\n  .footer .copyright {\n    float: right;\n    padding-right: 15px; }\n  .sidebar .sidebar-wrapper li.active > a:not([data-toggle=\"collapse\"]):before,\n  .sidebar .sidebar-wrapper li.active > [data-toggle=\"collapse\"] + div .nav li:before {\n    border-right: 17px solid #DDDDDD;\n    border-top: 17px solid transparent;\n    border-bottom: 17px solid transparent;\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: -16px;\n    opacity: 1;\n    top: 7px;\n    transition: opacity 150ms ease-in; }\n  .sidebar .sidebar-wrapper li.active > a:not([data-toggle=\"collapse\"]):after,\n  .sidebar .sidebar-wrapper li.active > [data-toggle=\"collapse\"] + div .nav li:after {\n    border-right: 17px solid #f4f3ef;\n    border-top: 17px solid transparent;\n    border-bottom: 17px solid transparent;\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: -17px;\n    opacity: 1;\n    top: 7px;\n    transition: opacity 150ms ease-in; }\n  .sidebar .sidebar-wrapper li.active > [data-toggle=\"collapse\"] + div .nav li a:before, .sidebar .sidebar-wrapper li.active > [data-toggle=\"collapse\"] + div .nav li a:after {\n    top: 0; } }\n\n@media screen and (max-width: 768px) {\n  .card-stats [class*=\"col-\"] .statistics::after {\n    display: none; }\n  .main-panel .content {\n    padding-left: 15px;\n    padding-right: 15px; }\n  .footer nav {\n    display: block;\n    margin-bottom: 5px;\n    float: none; }\n  .landing-page .section-story-overview .image-container:nth-child(2) {\n    margin-left: 0;\n    margin-bottom: 30px; }\n  .card .form-horizontal .col-md-3.col-form-label {\n    text-align: left; } }\n\n@media screen and (max-width: 767px) {\n  .nav-tabs-navigation.verical-navs {\n    padding: 0 28px; }\n  .typography-line {\n    padding-left: 23% !important; }\n    .typography-line span {\n      width: 60px !important; }\n  .login-page .navbar,\n  .lock-page .navbar,\n  .register-page .navbar {\n    padding: .5rem 1rem; }\n  .footer .footer-nav,\n  .footer .credits {\n    margin: 0 auto !important; }\n  .footer .footer-nav {\n    margin-bottom: 10px !important; }\n  .register-page .content {\n    padding-top: 5vh; }\n  .register-page .footer {\n    position: relative; }\n  .register-page .info-area.info-horizontal {\n    margin-top: 0; } }\n\n@media screen and (max-width: 413px) {\n  .fixed-plugin .dropdown.show-dropdown.show .dropdown-menu.show {\n    width: 225px !important; }\n    .fixed-plugin .dropdown.show-dropdown.show .dropdown-menu.show[x-placement=top-start] {\n      transform: translate3d(0, 400px, 0) !important; }\n    .fixed-plugin .dropdown.show-dropdown.show .dropdown-menu.show:before, .fixed-plugin .dropdown.show-dropdown.show .dropdown-menu.show:after {\n      bottom: 360px !important; }\n  .login-page .container {\n    padding-top: 100px !important; } }\n\n@media screen and (max-width: 576px) {\n  .navbar[class*='navbar-toggleable-'] .container {\n    margin-left: 0;\n    margin-right: 0; }\n  .card-contributions .card-stats {\n    flex-direction: column; }\n    .card-contributions .card-stats .bootstrap-switch {\n      margin-bottom: 15px; }\n  .footer .copyright {\n    text-align: center; }\n  .section-nucleo-icons .icons-container i {\n    font-size: 30px; }\n    .section-nucleo-icons .icons-container i:nth-child(6) {\n      font-size: 48px; }\n  .page-header .container h6.category-absolute {\n    width: 90%; }\n  .card-timeline .timeline .timeline-panel {\n    width: 38%;\n    padding: 15px; } }\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// This file was modified by <PERSON> Tim to keep only the animation that we need for <PERSON>trap Notify\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@charset \"UTF-8\";\n\n/*!\nAnimate.css - http://daneden.me/animate\nLicensed under the MIT license - http://opensource.org/licenses/MIT\n\nCopyright (c) 2015 <PERSON>\n*/\n\n.animated {\n  -webkit-animation-duration: 1s;\n  animation-duration: 1s;\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n\n.animated.infinite {\n  -webkit-animation-iteration-count: infinite;\n  animation-iteration-count: infinite;\n}\n\n.animated.hinge {\n  -webkit-animation-duration: 2s;\n  animation-duration: 2s;\n}\n\n.animated.bounceIn,\n.animated.bounceOut {\n  -webkit-animation-duration: .75s;\n  animation-duration: .75s;\n}\n\n.animated.flipOutX,\n.animated.flipOutY {\n  -webkit-animation-duration: .75s;\n  animation-duration: .75s;\n}\n\n@-webkit-keyframes shake {\n  from, to {\n    -webkit-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0);\n  }\n\n  10%, 30%, 50%, 70%, 90% {\n    -webkit-transform: translate3d(-10px, 0, 0);\n    transform: translate3d(-10px, 0, 0);\n  }\n\n  20%, 40%, 60%, 80% {\n    -webkit-transform: translate3d(10px, 0, 0);\n    transform: translate3d(10px, 0, 0);\n  }\n}\n\n@keyframes shake {\n  from, to {\n    -webkit-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0);\n  }\n\n  10%, 30%, 50%, 70%, 90% {\n    -webkit-transform: translate3d(-10px, 0, 0);\n    transform: translate3d(-10px, 0, 0);\n  }\n\n  20%, 40%, 60%, 80% {\n    -webkit-transform: translate3d(10px, 0, 0);\n    transform: translate3d(10px, 0, 0);\n  }\n}\n\n.shake {\n  -webkit-animation-name: shake;\n  animation-name: shake;\n}\n\n\n\n@-webkit-keyframes fadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0);\n  }\n\n  to {\n    opacity: 1;\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n@keyframes fadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0);\n  }\n\n  to {\n    opacity: 1;\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n.fadeInDown {\n  -webkit-animation-name: fadeInDown;\n  animation-name: fadeInDown;\n}\n\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n.fadeOut {\n  -webkit-animation-name: fadeOut;\n  animation-name: fadeOut;\n}\n\n@-webkit-keyframes fadeOutDown {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, 100%, 0);\n    transform: translate3d(0, 100%, 0);\n  }\n}\n\n@keyframes fadeOutDown {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, 100%, 0);\n    transform: translate3d(0, 100%, 0);\n  }\n}\n\n.fadeOutDown {\n  -webkit-animation-name: fadeOutDown;\n  animation-name: fadeOutDown;\n}\n\n@-webkit-keyframes fadeOutUp {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0);\n  }\n}\n\n@keyframes fadeOutUp {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -100%, 0);\n    transform: translate3d(0, -100%, 0);\n  }\n}\n\n.fadeOutUp {\n  -webkit-animation-name: fadeOutUp;\n  animation-name: fadeOutUp;\n}\n", "/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n", ".btn,\n.navbar .navbar-nav > a.btn{\n    border-width: $border-thick;\n    font-weight: $font-weight-semi;\n    font-size: $font-size-small;\n    line-height: $line-height;\n    text-transform: uppercase;\n    border: none;\n    margin: 10px 1px;\n    border-radius: $border-radius-small;\n    padding: $padding-btn-vertical $padding-btn-horizontal;\n    cursor: pointer;\n\n    @include btn-styles($default-color, $default-states-color);\n    @include transition($fast-transition-time, linear);\n\n    &:hover,\n    &:focus{\n        @include opacity(1);\n        outline: 0 !important;\n    }\n    &:active,\n    &.active,\n    .open > &.dropdown-toggle {\n         @include box-shadow(none);\n         outline: 0 !important;\n    }\n\n    .badge{\n      margin: 0;\n    }\n\n    &.btn-icon {\n        // see above for color variations\n        height: $btn-icon-size-regular;\n        min-width: $btn-icon-size-regular;\n        width: $btn-icon-size-regular;\n        padding: 0;\n        font-size: $btn-icon-font-size-regular;\n        overflow: hidden;\n        position: relative;\n        line-height: normal;\n\n        &.btn-simple{\n            padding: 0;\n        }\n\n        &.btn-sm{\n            height: $btn-icon-size-small;\n            min-width: $btn-icon-size-small;\n            width: $btn-icon-size-small;\n\n            .fa,\n            .far,\n            .fas,\n            .nc-icon{\n                font-size: $btn-icon-font-size-small;\n            }\n        }\n\n        &.btn-lg{\n            height: $btn-icon-size-lg;\n            min-width: $btn-icon-size-lg;\n            width: $btn-icon-size-lg;\n\n            .fa,\n            .far,\n            .fas,\n            .nc-icon{\n                font-size: $btn-icon-font-size-lg;\n            }\n        }\n\n        &:not(.btn-footer) .nc-icon,\n        &:not(.btn-footer) .fa,\n        &:not(.btn-footer) .far,\n        &:not(.btn-footer) .fas{\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-12px, -12px);\n            line-height: 1.5626rem;\n            width: 24px;\n        }\n\n        &.btn-neutral {\n          font-size: 20px;\n        }\n    }\n\n    &:not(.btn-icon) .nc-icon{\n        position: relative;\n        top: 1px;\n    }\n}\n\n// Apply the mixin to the buttons\n// .btn-default { @include btn-styles($default-color, $default-states-color); }\n.btn-primary { @include btn-styles($primary-color, $primary-states-color); }\n.btn-success { @include btn-styles($success-color, $success-states-color); }\n.btn-info    { @include btn-styles($info-color, $info-states-color); }\n.btn-warning { @include btn-styles($warning-color, $warning-states-color); }\n.btn-danger  { @include btn-styles($danger-color, $danger-states-color); }\n// .btn-neutral { @include btn-styles($white-color, $white-color); }\n\n.btn-outline-default { @include btn-outline-styles($default-color, $default-states-color); }\n.btn-outline-primary { @include btn-outline-styles($primary-color, $primary-states-color); }\n.btn-outline-success { @include btn-outline-styles($success-color, $success-states-color); }\n.btn-outline-info    { @include btn-outline-styles($info-color, $info-states-color); }\n.btn-outline-warning { @include btn-outline-styles($warning-color, $warning-states-color); }\n.btn-outline-danger  { @include btn-outline-styles($danger-color, $danger-states-color); }\n.btn-outline-neutral { @include btn-outline-styles($white-color, $default-states-color);\n    &:hover,\n    &:focus{\n        color: $default-states-color;\n        background-color: $white-color;\n    }\n}\n.btn-neutral {\n    @include btn-styles($white-color, $white-color);\n    color: $default-color;\n    &:hover,\n    &:focus{\n        color: $default-states-color;\n    }\n\n    &.btn-border{\n        &:hover,\n        &:focus{\n            color: $default-color;\n        }\n\n        &:active,\n        &.active,\n        .open > &.dropdown-toggle{\n             background-color: $white-color;\n             color: $default-color;\n        }\n    }\n\n    &.btn-link:active,\n    &.btn-link.active{\n        background-color: transparent;\n    }\n}\n\n.btn{\n     &:disabled,\n     &[disabled],\n     &.disabled{\n        @include opacity(.5);\n        pointer-events: none;\n    }\n}\n.btn-simple{\n    border: $border;\n    border-color: $default-color;\n    padding: $padding-btn-vertical - 1 $padding-round-horizontal - 1;\n    background-color: $transparent-bg;\n}\n\n.btn-simple,\n.btn-link{\n    &.disabled,\n    &:disabled,\n    &[disabled],\n    fieldset[disabled] & {\n        &,\n        &:hover,\n        &:focus,\n        &.focus,\n        &:active,\n        &.active {\n            background-color: $transparent-bg;\n        }\n    }\n}\n\n.btn-link{\n  border: $none;\n  padding: $padding-base-vertical $padding-base-horizontal;\n  background-color: $transparent-bg;\n}\n\n.btn-lg{\n   @include btn-size($padding-large-vertical, $padding-large-horizontal, $font-size-large, $border-radius-large);\n}\n.btn-sm{\n    @include btn-size($padding-small-vertical, $padding-small-horizontal, $font-size-small, $border-radius-small);\n}\n\n.btn-wd {\n    min-width: 140px;\n}\n.btn-group.select{\n    width: 100%;\n}\n.btn-group.select .btn{\n    text-align: left;\n}\n.btn-group.select .caret{\n    position: absolute;\n    top: 50%;\n    margin-top: -1px;\n    right: 8px;\n}\n.btn-group {\n  .btn + .btn {\n    margin-left: -3px;\n  }\n  .btn {\n    &:focus {\n      background-color: $info-color !important;\n    }\n  }\n}\n\n\n.btn-round{\n    border-width: $border-thin;\n    border-radius: $btn-round-radius;\n    padding-right: $padding-round-horizontal;\n    padding-left: $padding-round-horizontal;\n\n    &.btn-simple{\n        padding: $padding-btn-vertical - 1  $padding-round-horizontal - 1;\n    }\n}\n\n.no-caret {\n  &.dropdown-toggle::after {\n    display: none;\n  }\n}\n", "//== Buttons\n//\n//## For each of Bootstrap's buttons, define text, background and border color.\n\n$font-color:                 #66615b !default;\n$fill-font-color:            rgba(255, 255, 255, 0.8);\n$font-family-sans-serif:     'Montserrat', \"Helvetica\", Arial, sans-serif;\n$sans-serif-family:          'Montserrat', 'Helvetica Neue',  Arial, sans-serif;\n\n\n$none:                       0   !default;\n$border-thin:                1px !default;\n$border-thick:               2px !default;\n\n$white-color:                #FFFFFF !default;\n$white-bg:                   #FFFFFF !default;\n$orange-bg:                  #e95e38 !default;\n\n$smoke-bg:                   #F5F5F5 !default;\n$light-black:                #444    !default;\n\n$black-bg:                   rgba(30,30,30,.97) !default;\n\n$black-color:                #2c2c2c !default;\n$black-hr:                   #444444 !default;\n\n$hr-line:                    rgba(0,0,0, .1) !default;\n\n$light-gray:                 #E3E3E3 !default;\n$medium-gray:                #DDDDDD !default;\n$dark-gray:                  #9A9A9A !default;\n\n$table-line-color:           #ccc !default;\n$muted-color:                #a49e93 !default;\n\n$opacity-gray-3:             rgba(222,222,222, .3) !default;\n$opacity-gray-5:             rgba(222,222,222, .5) !default;\n$opacity-gray-8:             rgba(222,222,222, .8) !default;\n\n\n$opacity-5:                  rgba(255,255,255, .5) !default;\n$opacity-8:                  rgba(255,255,255, .8) !default;\n\n$datepicker-color-days:             rgba(255,255,255, .8)  !default;\n$datepicker-color-old-new-days:     rgba(255,255,255, .4)  !default;\n\n\n$opacity-1:                  rgba(255,255,255, .1) !default;\n$opacity-2:                  rgba(255,255,255, .2) !default;\n\n$transparent-bg:             transparent !default;\n$dark-background:            #555555 !default;\n\n$gray-input-bg:              #fffcf5 !default;\n$danger-input-bg:            #FFC0A4 !default;\n$success-input-bg:           #ABF3CB !default;\n$other-medium-gray:          #A49E93 !default;\n$transparent-bg:             transparent !default;\n\n$placeholder-gray:           rgba(210, 210, 210, 1)  !default;\n\n$default-color:              #66615B !default;\n$default-bg:                 #66615B !default;\n$default-states-color:       #403D39 !default;\n\n$smoke-bg:                   #F5F5F5 !default;\n$pale-bg:                    #FFFCF5 !default;\n$medium-pale-bg:             #F1EAE0 !default;\n$default-body-bg:            #f4f3ef !default;\n\n$primary-color:              #51cbce !default;\n$primary-states-color:       darken($primary-color, 10%) !default;\n\n$success-color:              #6bd098 !default;\n$success-states-color:       darken($success-color, 10%) !default;\n\n$info-color:                 #51bcda !default;\n$info-states-color:          darken($info-color, 10%) !default;\n\n$warning-color:              #fbc658 !default;\n$warning-states-color:       darken($warning-color, 10%) !default;\n\n$danger-color:               #ef8157 !default;\n$danger-states-color:        darken($danger-color, 8%) !default;\n\n$link-disabled-color:        #666666 !default;\n\n$purple-color:               #c178c1 !default;\n$purple-states-color:        darken($purple-color, 8%) !default;\n\n$medium-pale-bg:             #F1EAE0 !default;\n\n$brown-color:                #dcb285 !default;\n$default-color-opacity:      rgba(182, 182, 182, .6) !default;\n$primary-color-opacity:      rgba(249, 99, 50, .3) !default;\n$success-color-opacity:      rgba(24, 206, 15, .3) !default;\n$info-color-opacity:         rgba(44, 168, 255, .3) !default;\n$warning-color-opacity:      rgba(255, 178, 54, .3) !default;\n$danger-color-opacity:       rgba(255, 54, 54, .3) !default;\n\n$new-blue:            #1DC7EA;\n$new-purple:          #9368E9;\n$new-red:             #FB404B;\n$new-green:           #87CB16;\n$new-orange:          #FFA534;\n$new-dark-blue:       #1F77D0;\n$new-black:           #5e5e5e;\n\n$orange-color:               #f96332 !default;\n$color-red-error: rgb(185, 74, 72) !default;\n$color-grey-arrow: rgba(204, 204, 204, 0.2) !default;\n\n$bg-nude:               #FFFCF5 !default;\n$bg-primary:            lighten($primary-color, 7%) !default;\n$bg-info:               lighten($info-color, 7%) !default;\n$bg-success:            lighten($success-color, 7%) !default;\n$bg-warning:            lighten($warning-color, 7%) !default;\n$bg-danger:             lighten($danger-color, 7%) !default;\n$bg-brown:              lighten($brown-color, 7%) !default;\n$bg-purple:             lighten($purple-color, 7%) !default;\n\n//     brand Colors\n$brand-primary:              $primary-color !default;\n$brand-info:                 $info-color !default;\n$brand-success:              $success-color !default;\n$brand-warning:              $warning-color !default;\n$brand-danger:               $danger-color !default;\n$brand-inverse:              $black-color !default;\n\n$link-disabled-color:        #666666 !default;\n$dark-color:                 #212120 !default;\n//     light colors\n$light-blue:                 rgba($primary-color, .2);\n$light-azure:                rgba($info-color, .2);\n$light-green:                rgba($success-color, .2);\n$light-orange:               rgba($warning-color, .2);\n$light-red:                  rgba($danger-color, .2);\n\n// padding for links inside dropdown menu\n$padding-dropdown-vertical:     10px !default;\n$padding-dropdown-horizontal:   15px !default;\n\n$margin-large-vertical:        30px !default;\n$margin-base-vertical:         15px !default;\n\n// border radius for buttons\n$border-radius-none:             0px !default;\n$border-radius-btn-small:      26px !default;\n$border-radius-btn-base:       20px !default;\n$border-radius-btn-large:      50px !default;\n\n\n$margin-bottom:                0 0 10px 0 !default;\n$border-radius-small:           3px !default;\n$border-radius-base:            4px !default;\n$border-radius-large:           6px !default;\n$border-radius-x-large:         8px !default;\n$border-radius-extreme:         12px !default;\n\n//variables used in cards\n$card-black-color:          #252422 !default;\n$card-muted-color:          #ccc5b9 !default;\n\n$card-background-blue:      #b8d8d8 !default;\n$card-font-blue:            #506568 !default;\n$card-subtitle-blue:        #7a9e9f !default;\n\n$card-background-green:      #d5e5a3 !default;\n$card-font-green:            #60773d !default;\n$card-subtitle-green:        #92ac56 !default;\n\n$card-background-yellow:      #ffe28c !default;\n$card-font-yellow:            #b25825 !default;\n$card-subtitle-yellow:        #d88715 !default;\n\n$card-background-brown:      #d6c1ab !default;\n$card-font-brown:            #75442e !default;\n$card-subtitle-brown:        #a47e65 !default;\n\n$card-background-purple:      #baa9ba !default;\n$card-font-purple:            #3a283d !default;\n$card-subtitle-purple:        #5a283d !default;\n\n$card-background-orange:      #ff8f5e !default;\n$card-font-orange:            #772510 !default;\n$card-subtitle-orange:        #e95e37 !default;\n\n\n\n//== Components\n//\n\n$padding-input-vertical:        11px !default;\n$padding-input-horizontal:      11px !default;\n\n$padding-btn-vertical:         11px !default;\n$padding-btn-horizontal:       22px !default;\n\n$padding-base-vertical:        .5rem !default;\n$padding-base-horizontal:      .7rem !default;\n\n$padding-round-horizontal:     23px !default;\n\n$padding-simple-vertical:      10px !default;\n$padding-simple-horizontal:    17px !default;\n\n$padding-large-vertical:       15px !default;\n$padding-large-horizontal:     48px !default;\n\n$padding-small-vertical:        5px !default;\n$padding-small-horizontal:     15px !default;\n\n// $padding-xs-vertical:           1px !default;\n// $padding-xs-horizontal:         5px !default;\n\n$padding-label-vertical:        2px !default;\n$padding-label-horizontal:     12px !default;\n\n$margin-large-vertical:        30px !default;\n$margin-base-vertical:         15px !default;\n\n$margin-base-horizontal:       15px !default;\n\n$margin-bottom:                 10px        !default;\n$border:                        1px solid   !default;\n$border-radius-extra-small:     0.125rem    !default;\n$border-radius-small:           0.1875rem   !default;\n$border-radius-large:           0.25rem     !default;\n$border-radius-extreme:         0.875rem    !default;\n\n$border-radius-large-top:      $border-radius-large $border-radius-large 0 0 !default;\n$border-radius-large-bottom:   0 0 $border-radius-large $border-radius-large !default;\n\n$btn-round-radius:             30px         !default;\n\n$height-base:                  40px         !default;\n\n$btn-icon-size:                 3.5rem       !default;\n$btn-icon-size-regular:         2.375rem      !default;\n$btn-icon-font-size-regular:    0.9375rem     !default;\n$btn-icon-font-size-small:      0.6875rem      !default;\n$btn-icon-size-small:           1.875rem     !default;\n$btn-icon-font-size-lg:         1.325rem     !default;\n$btn-icon-size-lg:              3.6rem         !default;\n\n$font-size-h1:                 3.5em        !default; // ~ 49px\n$font-size-h2:                 2.5em        !default; // ~ 35px\n$font-size-h3:                 2em          !default; // ~ 28px\n$font-size-h4:                 1.714em      !default; // ~ 24px\n$font-size-h5:                 1.57em       !default; // ~ 22px\n$font-size-h6:                 1em          !default; // ~ 14px\n\n$font-paragraph:               1em          !default;\n$font-size-navbar:             1em          !default;\n$font-size-mini:               0.7142em     !default;\n$font-size-small:              0.8571em     !default;\n$font-size-base:               14px         !default;\n$font-size-large:              1rem         !default;\n$font-size-large-navbar:       20px         !default;\n$font-size-blockquote:         1.1em        !default; // ~ 15px\n$font-size-medium:             16px         !default;\n$font-size-xs:                 12px         !default;\n\n$font-weight-light:             300         !default;\n$font-weight-normal:            400         !default;\n$font-weight-semi:              600         !default;\n$font-weight-bold:              700         !default;\n\n$line-height-general:        1.5            !default;\n$line-height-nav-link:       1.625rem       !default;\n$btn-icon-line-height:       2.4em          !default;\n$line-height:                1.35em         !default;\n$line-height-lg:             54px           !default;\n\n\n$border-radius-top:        10px 10px 0 0     !default;\n$border-radius-bottom:     0 0 10px 10px     !default;\n\n$dropdown-shadow:          1px 2px 7px 1px rgba(0,0,0,.125);\n$box-shadow-raised:        0px 10px 25px 0px rgba(0, 0, 0, 0.3);\n$box-shadow:               0 2px 2px rgba(204, 197, 185, 0.5);\n$sidebar-box-shadow:       0px 2px 22px 0 rgba(0, 0, 0,.20), 0px 2px 30px 0 rgba(0, 0, 0,.35);\n\n$general-transition-time:  300ms             !default;\n\n$slow-transition-time:           370ms       !default;\n$dropdown-coordinates:      29px -50px       !default;\n\n$fast-transition-time:           150ms       !default;\n$select-coordinates:         50% -40px       !default;\n\n$transition-linear:         linear           !default;\n$transition-bezier:         cubic-bezier(0.34, 1.61, 0.7, 1) !default;\n$transition-ease:           ease 0s;\n\n\n//$navbar-padding-a:               9px 13px;\n$navbar-margin-a:                15px 0px;\n\n$padding-social-a:               10px 5px;\n\n$navbar-margin-a-btn:            15px 0px;\n$navbar-margin-a-btn-round:      16px 0px;\n\n$navbar-padding-a-icons:         6px 15px;\n$navbar-margin-a-icons:          6px  3px;\n\n$navbar-padding-base:            0.625rem;\n//$navbar-margin-brand:             5px  0px;\n\n$navbar-margin-brand-icons:      12px auto;\n$navbar-margin-btn:              15px  3px;\n\n$height-icon-sm:\t\t\t\t 32px;\n$width-icon-sm:\t\t\t\t\t 32px;\n$padding-icon-sm:\t\t\t     4px;\n$border-radius-icon-sm:\t\t\t 7px;\n\n$height-icon-message:\t\t\t 40px;\n$width-icon-message:\t\t\t 40px;\n\n$height-icon-message-sm: \t\t 20px;\n$width-icon-message-sm:\t\t\t 20px;\n\n$white-navbar:                  rgba(#FFFFFF, .96);\n$primary-navbar:                rgba(#34ACDC, .98);\n$info-navbar:                   rgba(#5BCAFF, .98);\n$success-navbar:                rgba(#4CD964, .98);\n$warning-navbar:                rgba(#FF9500, .98);\n$danger-navbar:                 rgba(#FF4C40, .98);\n\n$topbar-x:             topbar-x !default;\n$topbar-back:          topbar-back !default;\n$bottombar-x:          bottombar-x !default;\n$bottombar-back:       bottombar-back !default;\n\n//Nucleo Icons\n\n$nc-font-path:        '../fonts' !default;\n$nc-font-size-base:   14px !default;\n$nc-css-prefix:       nc !default;\n$nc-background-color:     #eee !default;\n$nc-li-width:         (30em / 14) !default;\n$nc-padding-width:    (1em/3) !default;\n\n// Social icons color\n$social-twitter:                   #55acee !default;\n$social-twitter-state-color:       darken(#55acee, 5%) !default;\n\n$social-facebook: \t\t\t       #3b5998 !default;\n$social-facebook-state-color:      darken(#3b5998, 5%) !default;\n\n$social-google: \t\t\t       #dd4b39 !default;\n$social-google-state-color:        darken(#dd4b39, 5%) !default;\n\n$social-linkedin: \t\t\t         #0077B5 !default;\n$social-linkedin-state-color:        darken(#0077B5, 5%) !default;\n\n// Sidebar variables\n$sidebar-width:              calc(100% - 260px) !default;\n$sidebar-mini-width:         calc(100% - 80px) !default;\n\n\n// Social icons color\n$social-twitter:                   #55acee !default;\n$social-twitter-state-color:       lighten(#55acee, 6%) !default;\n\n$social-facebook: \t\t\t       #3b5998 !default;\n$social-facebook-state-color:      lighten(#3b5998, 6%) !default;\n\n$social-google: \t\t\t       #dd4b39 !default;\n$social-google-state-color:        lighten(#dd4b39, 6%) !default;\n\n$social-linkedin: \t\t\t       #0077B5 !default;\n$social-linkedin-state-color:      lighten(#0077B5, 6%) !default;\n\n$social-pinterest: \t\t\t       #cc2127 !default;\n$social-pinterest-state-color:     lighten(#cc2127, 6%) !default;\n\n$social-dribbble: \t\t           #ea4c89 !default;\n$social-dribbble-state-color:      lighten(#ea4c89, 6%) !default;\n\n$social-github: \t\t      \t   #333333 !default;\n$social-github-state-color:        lighten(#333333, 6%) !default;\n\n$social-youtube: \t\t    \t   #e52d27 !default;\n$social-youtube-state-color:       lighten(#e52d27, 6%) !default;\n\n$social-instagram: \t\t           #125688 !default;\n$social-instagram-state-color:     lighten(#125688, 6%) !default;\n\n$social-reddit: \t\t  \t       #ff4500 !default;\n$social-reddit-state-color:        lighten(#ff4500, 6%) !default;\n\n$social-tumblr: \t\t\t       #35465c !default;\n$social-tumblr-state-color:        lighten(#35465c, 6%) !default;\n\n$social-behance: \t\t\t       #1769ff !default;\n$social-behance-state-color:       lighten(#1769ff, 6%) !default;\n\n// Placeholder text color\n$input-color-placeholder: #999 !default;\n\n$zindex-select-dropdown: 1060 !default; // must be higher than a modal background (1050)\n", "// Mixin for generating new styles\n@mixin btn-styles($btn-color, $btn-states-color) {\n    background-color: $btn-color;\n\n    &:hover,\n    &:focus,\n    &:active,\n    &.active,\n    &:active:focus,\n    &:active:hover,\n    &.active:focus,\n    &.active:hover,\n    .show > &.dropdown-toggle,\n    .show > &.dropdown-toggle:focus,\n    .show > &.dropdown-toggle:hover {\n        background-color: $btn-states-color !important;\n        color: $white-color !important;\n        box-shadow: none !important;\n    }\n\n    &:not([data-action]):hover{\n        box-shadow:  none;\n    }\n\n    &.disabled,\n    &:disabled,\n    &[disabled],\n    fieldset[disabled] & {\n        &,\n        &:hover,\n        &:focus,\n        &.focus,\n        &:active,\n        &.active {\n            background-color: $btn-color;\n            border-color: $btn-color;\n        }\n    }\n\n    // btn-neutral style\n    @if $btn-color == $white-color{\n        color: $primary-color;\n\n        &.btn-danger{\n            color: $danger-color;\n\n            &:hover,\n            &:focus,\n            &:active,\n            &:active:focus{\n                color: $danger-states-color !important;\n            }\n        }\n\n        &.btn-info{\n            color: $info-color !important;\n\n            &:hover,\n            &:focus,\n            &:active,\n            &:active:focus{\n                color: $info-states-color !important;\n            }\n        }\n\n        &.btn-warning{\n            color: $warning-color !important;\n\n            &:hover,\n            &:focus,\n            &:active,\n            &:active:focus{\n                color: $warning-states-color !important;\n            }\n        }\n\n        &.btn-success{\n            color: $success-color !important;\n\n            &:hover,\n            &:focus,\n            &:active,\n            &:active:focus{\n                color: $success-states-color !important;\n            }\n        }\n\n        &.btn-default{\n            color: $default-color !important;\n\n            &:hover,\n            &:focus,\n            &:active,\n            &:active:focus{\n                color: $default-states-color !important;\n            }\n        }\n\n        &.active,\n        &:active,\n        &:active:focus,\n        &:active:hover,\n        &.active:focus,\n        &.active:hover,\n        .show > &.dropdown-toggle,\n        .show > &.dropdown-toggle:focus,\n        .show > &.dropdown-toggle:hover {\n            background-color: $white-color !important;\n            color: $primary-states-color !important;\n            box-shadow: none !important;\n        }\n\n        &:hover,\n        &:focus{\n            color: $primary-states-color !important;\n\n            &:not(.nav-link){\n                box-shadow: none;\n            }\n\n        }\n\n    } @else {\n        color: $white-color;\n    }\n\n    &.btn-simple{\n        color: $btn-color;\n        border-color: $btn-color;\n\n        &:hover,\n        &:focus,\n        &:active{\n            background-color: $transparent-bg;\n            color: $btn-states-color;\n            border-color: $btn-states-color;\n            box-shadow: none;\n        }\n    }\n\n    &.btn-link{\n        color: $btn-color;\n\n        &:hover,\n        &:focus,\n        &:active,\n        &:active:focus {\n            background-color: $transparent-bg;\n            color: $btn-states-color;\n            text-decoration: none;\n            box-shadow: none;\n        }\n    }\n}\n\n@mixin btn-outline-styles($btn-color, $btn-states-color){\n    background: $transparent-bg;\n    border: 2px solid $btn-color !important;\n    color: $btn-color;\n    @include opacity(1);\n\n    &:hover,\n    &:focus,\n    &:active,\n    &:focus:active,\n    &.active,\n    .open > &.dropdown-toggle {\n      background-color: $btn-color !important;\n      color: $fill-font-color !important;\n      border-color: $btn-color !important;\n      .caret{\n          border-top-color: $fill-font-color !important;\n      }\n    }\n\n    .caret{\n        border-top-color: $white-color !important;\n    }\n\n    &.disabled,\n    &:disabled,\n    &[disabled],\n    fieldset[disabled] & {\n      &,\n      &:hover,\n      &:focus,\n      &.focus,\n      &:active,\n      &.active {\n        background-color: $transparent-bg !important;\n        border-color: $btn-color !important;\n      }\n    }\n}\n\n@mixin btn-size($padding-vertical, $padding-horizontal, $font-size, $border){\n   font-size: $font-size;\n   border-radius: $border;\n   padding: $padding-vertical $padding-horizontal;\n\n   &.btn-simple{\n       padding: $padding-vertical - 1 $padding-horizontal - 1;\n   }\n\n}\n\n@mixin rotate-180(){\n    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n    -webkit-transform: rotate(180deg);\n    -ms-transform: rotate(180deg);\n    transform: rotate(180deg);\n}\n", "@mixin box-shadow($shadow...) {\n  -webkit-box-shadow: $shadow; // iOS <4.3 & Android <4.1\n          box-shadow: $shadow;\n}\n\n@mixin transition($time, $type){\n    -webkit-transition: all $time $type;\n    -moz-transition: all $time $type;\n    -o-transition: all $time $type;\n    -ms-transition: all $time $type;\n    transition: all $time $type;\n}\n\n\n@mixin sidebar-color($color){\n  &:after{\n    background: $color;\n  }\n}\n\n@mixin bar-animation($type){\n     -webkit-animation: $type 500ms linear 0s;\n     -moz-animation: $type 500ms linear 0s;\n     animation: $type 500ms 0s;\n     -webkit-animation-fill-mode: forwards;\n     -moz-animation-fill-mode: forwards;\n     animation-fill-mode: forwards;\n}\n\n@mixin sidebar-active-color($font-color){\n    .nav {\n        li {\n          &.active > a,\n          &.active > a i,\n          &.active > a[data-toggle=\"collapse\"],\n          &.active > a[data-toggle=\"collapse\"] i,\n          &.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active .sidebar-mini-icon,\n          &.active > a[data-toggle=\"collapse\"] ~ div > ul > li.active > a {\n            color: $font-color;\n            opacity: 1;\n          }\n        }\n    }\n}\n\n@mixin transition-opacity($time, $type){\n    -webkit-transition: opacity $time $type;\n    -moz-transition: opacity $time $type;\n    -o-transition: opacity $time $type;\n    -ms-transition: opacity $time $type;\n    transition: opacity $time $type;\n}\n\n@mixin transform-translate-y-dropdown($value) {\n    -webkit-transform:  translate3d(-20px,$value,0) !important;\n       -moz-transform: translate3d(-20px,$value,0) !important;\n       -o-transform: translate3d(-20px,$value,0) !important;\n       -ms-transform: translate3d(-20px,$value,0) !important;\n       transform: translate3d(-20px,$value,0) !important;\n}\n\n@mixin transform-translate-x($value){\n     -webkit-transform:  translate3d($value, 0, 0);\n        -moz-transform: translate3d($value, 0, 0);\n        -o-transform: translate3d($value, 0, 0);\n        -ms-transform: translate3d($value, 0, 0);\n        transform: translate3d($value, 0, 0);\n}\n\n@mixin transform-translate-y($value){\n     -webkit-transform:  translate3d(0,$value,0) !important;\n        -moz-transform: translate3d(0,$value,0) !important;\n        -o-transform: translate3d(0,$value,0) !important;\n        -ms-transform: translate3d(0,$value,0) !important;\n        transform: translate3d(0,$value,0) !important;\n}\n\n@mixin transform-translate-y-fixed-plugin($value){\n     -webkit-transform:  translate3d(0,$value,0) !important;\n        -moz-transform: translate3d(0,$value,0) !important;\n        -o-transform: translate3d(0,$value,0) !important;\n        -ms-transform: translate3d(0,$value,0) !important;\n        transform: translate3d(0,$value,0) !important;\n}\n\n@mixin icon-gradient($color, $bottomColor: #000){\n    background: $color;\n    background: -webkit-linear-gradient($color 0%, $bottomColor 80%);\n    background: -o-linear-gradient($color 0%, $bottomColor 80%);\n    background: -moz-linear-gradient($color 0%, $bottomColor 80%);\n    background: linear-gradient($color 0%, $bottomColor 80%);\n}\n\n@mixin topbar-x-rotation(){\n    @keyframes topbar-x {\n      0% {top: 0px; transform: rotate(0deg); }\n      45% {top: 6px; transform: rotate(145deg); }\n      75% {transform: rotate(130deg); }\n      100% {transform: rotate(135deg); }\n    }\n    @-webkit-keyframes topbar-x {\n      0% {top: 0px; -webkit-transform: rotate(0deg); }\n      45% {top: 6px; -webkit-transform: rotate(145deg); }\n      75% {-webkit-transform: rotate(130deg); }\n      100% { -webkit-transform: rotate(135deg); }\n    }\n    @-moz-keyframes topbar-x {\n      0% {top: 0px; -moz-transform: rotate(0deg); }\n      45% {top: 6px; -moz-transform: rotate(145deg); }\n      75% {-moz-transform: rotate(130deg); }\n      100% { -moz-transform: rotate(135deg); }\n    }\n}\n\n\n@mixin topbar-back-rotation(){\n    @keyframes topbar-back {\n      0% { top: 6px; transform: rotate(135deg); }\n      45% { transform: rotate(-10deg); }\n      75% { transform: rotate(5deg); }\n      100% { top: 0px; transform: rotate(0); }\n    }\n\n    @-webkit-keyframes topbar-back {\n      0% { top: 6px; -webkit-transform: rotate(135deg); }\n      45% { -webkit-transform: rotate(-10deg); }\n      75% { -webkit-transform: rotate(5deg); }\n      100% { top: 0px; -webkit-transform: rotate(0); }\n    }\n\n    @-moz-keyframes topbar-back {\n      0% { top: 6px; -moz-transform: rotate(135deg); }\n      45% { -moz-transform: rotate(-10deg); }\n      75% { -moz-transform: rotate(5deg); }\n      100% { top: 0px; -moz-transform: rotate(0); }\n    }\n}\n\n@mixin bottombar-x-rotation(){\n    @keyframes bottombar-x {\n      0% {bottom: 0px; transform: rotate(0deg);}\n      45% {bottom: 6px; transform: rotate(-145deg);}\n      75% {transform: rotate(-130deg);}\n      100% {transform: rotate(-135deg);}\n    }\n    @-webkit-keyframes bottombar-x {\n      0% {bottom: 0px; -webkit-transform: rotate(0deg);}\n      45% {bottom: 6px; -webkit-transform: rotate(-145deg);}\n      75% {-webkit-transform: rotate(-130deg);}\n      100% {-webkit-transform: rotate(-135deg);}\n    }\n    @-moz-keyframes bottombar-x {\n      0% {bottom: 0px; -moz-transform: rotate(0deg);}\n      45% {bottom: 6px; -moz-transform: rotate(-145deg);}\n      75% {-moz-transform: rotate(-130deg);}\n      100% {-moz-transform: rotate(-135deg);}\n    }\n}\n\n@mixin bottombar-back-rotation{\n    @keyframes bottombar-back {\n      0% { bottom: 6px;transform: rotate(-135deg);}\n      45% { transform: rotate(10deg);}\n      75% { transform: rotate(-5deg);}\n      100% { bottom: 0px;transform: rotate(0);}\n    }\n    @-webkit-keyframes bottombar-back {\n      0% {bottom: 6px;-webkit-transform: rotate(-135deg);}\n      45% {-webkit-transform: rotate(10deg);}\n      75% {-webkit-transform: rotate(-5deg);}\n      100% {bottom: 0px;-webkit-transform: rotate(0);}\n    }\n    @-moz-keyframes bottombar-back {\n      0% {bottom: 6px;-moz-transform: rotate(-135deg);}\n      45% {-moz-transform: rotate(10deg);}\n      75% {-moz-transform: rotate(-5deg);}\n      100% {bottom: 0px;-moz-transform: rotate(0);}\n    }\n\n}\n\n@mixin sidebar-text-color($text-color){\n    .nav {\n      li {\n        a,\n        a i,\n        a[data-toggle=\"collapse\"],\n        a[data-toggle=\"collapse\"] i,\n        a[data-toggle=\"collapse\"] ~ div > ul > li .sidebar-mini-icon,\n        a[data-toggle=\"collapse\"] ~ div > ul > li > a {\n          color: $text-color;\n          opacity: .7;\n        }\n\n        &:hover:not(.active) > a,\n        &:focus:not(.active) > a {\n            opacity: 1;\n        }\n      }\n    }\n\n    .logo {\n      .simple-text {\n        color: $text-color;\n      }\n      &:after {\n        background-color: $text-color;\n        opacity: .4;\n      }\n    }\n\n    .user {\n      .info a span,\n      .nav .sidebar-mini-icon,\n      .nav .sidebar-normal {\n        color: $text-color !important;\n      }\n      &:after {\n        background-color: $text-color;\n        opacity: .4;\n      }\n    }\n}\n\n@mixin badge-color($color) {\n    border-color: $color;\n    background-color: $color;\n}\n", "// Opacity\n\n@mixin opacity($opacity) {\n  opacity: $opacity;\n  // IE8 filter\n  $opacity-ie: ($opacity * 100);\n  filter: #{alpha(opacity=$opacity-ie)};\n}\n", "@mixin input-size($padding-vertical, $padding-horizontal){\n    padding: $padding-vertical $padding-horizontal;\n}\n\n@mixin form-control-placeholder($color, $opacity){\n   .form-control::-moz-placeholder{\n       color: $color;\n       @include opacity(1);\n   }\n   .form-control:-moz-placeholder{\n       color: $color;\n       @include opacity(1);\n   }\n   .form-control::-webkit-input-placeholder{\n       color: $color;\n       @include opacity(1);\n   }\n   .form-control:-ms-input-placeholder{\n       color: $color;\n       @include opacity(1);\n   }\n}\n\n@mixin placeholder() {\n  &::-moz-placeholder {@content; } // Firefox\n  &:-ms-input-placeholder {@content; } // Internet Explorer 10+\n  &::-webkit-input-placeholder  {@content; } // Safari and Chrome\n}\n\n@mixin light-form(){\n    border-radius: 0;\n    border:0;\n    padding: 0;\n    background-color: transparent;\n\n}\n\n\n@mixin form-control-lg-padding($padding-vertical, $padding-horizontal) {\n    .form-group.no-border.form-control-lg,\n    .input-group.no-border.form-control-lg{\n        .input-group-append .input-group-text{\n            padding: $padding-vertical 0 $padding-vertical $padding-horizontal;\n        }\n\n        .form-control{\n            padding: $padding-vertical $padding-horizontal;\n\n            & + .input-group-prepend .input-group-text,\n            & + .input-group-append .input-group-text{\n                padding: $padding-vertical $padding-horizontal $padding-vertical 0;\n            }\n        }\n    }\n\n    .form-group.form-control-lg,\n    .input-group.form-control-lg{\n        .form-control{\n            padding: $padding-vertical - 1 $padding-horizontal - 1;\n\n            & + .input-group-prepend .input-group-text,\n            & + .input-group-append .input-group-text{\n                padding: $padding-vertical - 1 $padding-horizontal - 1 $padding-vertical - 1 0;\n            }\n        }\n\n        .input-group-prepend .input-group-text,\n        .input-group-append .input-group-text{\n            padding: $padding-vertical - 1 0 $padding-vertical $padding-horizontal - 1;\n\n            & + .form-control{\n                padding: $padding-vertical  $padding-horizontal - 1 $padding-vertical $padding-horizontal - 3;\n            }\n        }\n    }\n}\n\n\n\n@mixin input-base-padding($padding-vertical, $padding-horizontal) {\n    .form-group.no-border,\n    .input-group.no-border{\n        .form-control{\n            padding: $padding-vertical $padding-horizontal;\n\n            & + .input-group-prepend .input-group-text,\n            & + .input-group-append .input-group-text{\n                padding: $padding-vertical $padding-horizontal $padding-vertical 0;\n            }\n        }\n\n        .input-group-prepend .input-group-text,\n        .input-group-append .input-group-text{\n            padding: $padding-vertical 0 $padding-vertical $padding-horizontal;\n        }\n    }\n\n    .form-group,\n    .input-group{\n        .form-control{\n            padding: $padding-vertical - 1 $padding-horizontal - 1 $padding-vertical - 1 $padding-horizontal - 1;\n\n            & + .input-group-prepend .input-group-text,\n            & + .input-group-append .input-group-text{\n                padding: $padding-vertical - 1 $padding-horizontal - 1 $padding-vertical - 1 0;\n            }\n        }\n\n        .input-group-prepend .input-group-text,\n        .input-group-append .input-group-text{\n            padding: $padding-vertical - 1 0 $padding-vertical - 1 $padding-horizontal - 1;\n\n            & + .form-control,\n            & ~ .form-control{\n                padding:$padding-vertical - 1 $padding-horizontal $padding-vertical $padding-horizontal - 3;\n            }\n        }\n    }\n}\n\n\n//color1 = $opacity-5\n//color2 = $opacity-8\n//color3 = $white-color\n//color4 = $transparent-bg\n//color5 = $opacity-1\n//color6 = $opacity-2\n\n\n@mixin input-coloured-bg($color1, $color2, $color3, $color4, $color5, $color6) {\n    @include form-control-placeholder(darken($color2, 8%), 1);\n\n    .form-control{\n        border-color: $color1;\n        color: $color2;\n\n        &:focus{\n            border-color: $color3;\n            background-color: $color4;\n            color: $color3;\n        }\n    }\n\n    .has-success,\n    .has-danger{\n        &:after{\n            color: $color3;\n        }\n    }\n\n    .has-danger{\n        .form-control{\n            background-color: $color4;\n        }\n    }\n\n    .input-group-prepend .input-group-text,\n    .input-group-append .input-group-text{\n        background-color: $color4;\n        border-color: $color1;\n        color: $color2;\n    }\n\n    .input-group-focus{\n        .input-group-prepend .input-group-text,\n        .input-group-append .input-group-text{\n            background-color: $color4;\n            border-color: $color3;\n            color: $color3;\n        }\n    }\n\n    .form-group.no-border,\n    .input-group.no-border{\n        .form-control{\n            background-color: $color5;\n            color: $color2;\n\n            &:focus,\n            &:active,\n            &:active{\n                background-color: $color6;\n                color: $color3;\n            }\n        }\n\n        .form-control + .input-group-prepend .input-group-text,\n        .form-control + .input-group-append .input-group-text{\n            background-color: $color5;\n\n            &:focus,\n            &:active,\n            &:active{\n                background-color: $color6;\n                color: $color3;\n            }\n        }\n\n        .form-control{\n            &:focus{\n                & + .input-group-prepend .input-group-text,\n                & + .input-group-append .input-group-text{\n                    background-color: $color6;\n                    color: $color3;\n                }\n            }\n        }\n\n        .input-group-prepend .input-group-text,\n        .input-group-append .input-group-text{\n            background-color: $color5;\n            border: none;\n            color: $color2;\n        }\n\n        &.input-group-focus{\n            .input-group-prepend .input-group-text,\n            .input-group-append .input-group-text{\n                background-color: $color6;\n                color: $color3;\n            }\n        }\n    }\n}\n\n@mixin transition-input-focus-color() {\n    -webkit-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n    -moz-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n    -o-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n    -ms-transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n    transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;\n}\n", "@include placeholder() {\n  color: $dark-gray;\n};\n\n\n.form-control {\n    background-color: $white-color;\n    border: 1px solid $medium-gray;\n    border-radius: $border-radius-base;\n    color: $font-color;\n    line-height: normal;\n    height: auto;\n    font-size: $font-size-base;\n    @include transition-input-focus-color();\n    @include box-shadow(none);\n\n    &:focus {\n        border: 1px solid $dark-gray;\n        @include box-shadow(none);\n        outline: 0 !important;\n        color: $default-color;\n\n        & + .input-group-append .input-group-text,\n        & ~ .input-group-append .input-group-text,\n        & + .input-group-prepend .input-group-text,\n        & ~ .input-group-prepend .input-group-text{\n            border: 1px solid #ccc;\n            border-left: none;\n            background-color: $transparent-bg;\n        }\n    }\n\n    .has-success &,\n    .has-error &,\n    .has-success &:focus,\n    .has-error &:focus{\n        @include box-shadow(none);\n    }\n\n    .has-success &{\n        border: 1px solid $table-line-color;\n        color: $font-color;\n\n        &.form-control-success{\n            padding-right: 2.5em !important;\n        }\n    }\n    .has-success &:focus{\n        border: 1px solid $success-color;\n        color: $success-color;\n    }\n    .has-danger &{\n        background-color: $danger-input-bg;\n        border: 1px solid $danger-color;\n        color: $danger-color;\n\n        &.form-control-danger{\n            padding-right: 2.5em !important;\n        }\n    }\n    .has-danger &:focus{\n        background-color: $white-color;\n        border: 1px solid $danger-color;\n    }\n\n    & + .form-control-feedback{\n        border-radius: $border-radius-large;\n        font-size: $font-size-base;\n        margin-top: -7px;\n        position: absolute;\n        right: 10px;\n        top: 50%;\n        vertical-align: middle;\n    }\n\n    .open &{\n        border-radius: $border-radius-large $border-radius-large 0 0;\n        border-bottom-color: transparent;\n    }\n\n    & + .input-group-append .input-group-text,\n    & + .input-group-prepend .input-group-text{\n        background-color: $white-bg;\n    }\n}\n\n\n@include form-control-lg-padding($padding-large-vertical, $padding-input-horizontal);\n@include input-base-padding($padding-input-vertical, $padding-input-horizontal);\n\n.input-group {\n  &.has-success {\n    .input-group-prepend,\n    .input-group-append {\n      .input-group-text {\n        border: 1px solid $table-line-color;\n        color: $font-color;\n        background-color: $white-color;\n        border-right: none;\n      }\n    }\n  }\n}\n\n.form-group.no-border,\n.input-group.no-border{\n    .form-control,\n    .form-control + .input-group-prepend .input-group-text,\n    .form-control + .input-group-append .input-group-text{\n        background-color: $opacity-gray-3;\n        border: medium none;\n        &:focus,\n        &:active,\n        &:active{\n            border: medium none;\n            background-color: $opacity-gray-5;\n        }\n    }\n\n    .form-control{\n        &:focus{\n            & + .input-group-prepend .input-group-text,\n            & + .input-group-append .input-group-text{\n                background-color: $opacity-gray-5;\n            }\n        }\n    }\n\n    .input-group-prepend .input-group-text,\n    .input-group-append .input-group-text{\n        background-color: $opacity-gray-3;\n        border: none;\n    }\n}\n\n.has-error{\n    .form-control-feedback, .control-label{\n        color: $danger-color;\n    }\n}\n.has-success{\n    .form-control-feedback, .control-label{\n        color: $success-color;\n    }\n}\n\n.input-group.has-danger {\n  .input-group-prepend {\n    border-radius: $border-radius-base;\n    .input-group-text {\n      border: 1px solid $danger-color;\n      border-right: none;\n    }\n  }\n  .error {\n    display: block;\n    width: 100%;\n    color: $danger-color;\n    margin-top: 3px;\n  }\n}\n\n.input-group.has-success {\n  .input-group-prepend {\n    border-radius: $border-radius-base;\n    .input-group-text {\n      // border: 1px solid $success-color;\n      border-right: none;\n    }\n  }\n}\n\n\n.input-group-focus{\n  .input-group-prepend .input-group-text,\n  .input-group-append .input-group-text{\n    background-color: $white-bg;\n    border-color: $dark-gray;\n  }\n\n  &.no-border{\n    .input-group-prepend .input-group-text,\n    .input-group-append .input-group-text{\n      background-color: $opacity-gray-5;\n    }\n  }\n\n  &.has-danger {\n    .input-group-append,\n    .input-group-prepend {\n      .input-group-text {\n        background-color: $danger-input-bg;\n      }\n    }\n  }\n\n  &.has-success {\n    .input-group-append,\n    .input-group-prepend {\n      .input-group-text {\n        background-color: $success-input-bg;\n        border: 1px solid $success-color;\n        border-right: none;\n      }\n    }\n  }\n}\n\n.input-group-append .input-group-text,\n.input-group-prepend .input-group-text {\n    background-color: transparent;\n    border: 1px solid $light-gray;\n    color: $default-color;\n    border-top-right-radius: $border-radius-base;\n    border-bottom-right-radius: $border-radius-base;\n\n    & i{\n      opacity: .5;\n    }\n\n    @include transition-input-focus-color();\n\n    .has-danger &{\n      background-color: $danger-input-bg;\n    }\n    .has-success &{\n      background-color: $success-input-bg;\n    }\n    .has-danger.input-group-focus &{\n      background-color: $white-color;\n      color: $danger-color;\n    }\n    .has-success.input-group-focus &{\n      background-color: $white-color;\n      color: $success-color;\n    }\n    .has-danger .form-control:focus + &{\n      color: $danger-color;\n    }\n    .has-success .form-control:focus + &{\n      color: $success-color;\n    }\n\n    & + .form-control,\n    & ~ .form-control{\n        @include input-size($padding-base-vertical - 1, $padding-base-horizontal);\n        padding-left: 18px;\n    }\n\n    i{\n        width: 17px;\n    }\n}\n\n.input-group-append,\n.input-group-prepend{\n  margin: 0;\n}\n\n\n.input-group-append .input-group-text{\n  border-left: none;\n}\n.input-group-prepend .input-group-text{\n  border-right: none;\n}\n\n.input-group,\n.form-group{\n    margin-bottom: 10px;\n    position: relative;\n\n    .form-control-static{\n        margin-top: 9px;\n    }\n    &.has-danger {\n      .error {\n        color: $danger-color;\n      }\n    }\n}\n.input-group[disabled]{\n    .input-group-prepend .input-group-text,\n    .input-group-append .input-group-text{\n        background-color: $light-gray;\n    }\n}\n\n.input-group .form-control:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child){\n    border-radius: $border-radius-base;\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n    border-left: 0 none;\n}\n\n.input-group .form-control:first-child,\n.input-group-btn:first-child > .dropdown-toggle,\n.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {\n    border-right: 0 none;\n}\n.input-group .form-control:last-child,\n.input-group-btn:last-child > .dropdown-toggle,\n.input-group-btn:first-child > .btn:not(:first-child) {\n    border-left: 0 none;\n}\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\n    background-color: $light-gray;\n    color: $default-color;\n    cursor: not-allowed;\n}\n\n.input-group-btn .btn{\n    border-width: $border-thin;\n    padding: $padding-btn-vertical  $padding-base-horizontal;\n}\n.input-group-btn .btn-default:not(.btn-fill){\n    border-color: $medium-gray;\n}\n\n.input-group-btn:last-child > .btn{\n    margin-left: 0;\n}\ntextarea.form-control{\n    max-width: 100%;\n    max-height: 80px;\n    padding: 10px 10px 0 0;\n    resize: none;\n    border: none;\n    border: 1px solid $light-gray;\n    border-radius: $border-radius-base;\n    line-height: 2;\n}\n\n.has-success,\n.has-danger{\n\n    &.form-group .form-control,\n    &.form-group.no-border .form-control{\n        padding-right: $padding-input-horizontal + 21;\n    }\n}\n\n.form.form-newsletter .form-group{\n    float: left;\n    width: 78%;\n    margin-right: 2%;\n    margin-top: 9px;\n}\n\n.input-group .input-group-btn{\n    padding: 0 12px;\n}\n\n// Input files - hide actual input - requires specific markup in the sample.\n.form-group input[type=file] {\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.form-text{\n    font-size: $font-size-small;\n}\n\n.form-control-lg{\n    padding: 0;\n    font-size: inherit;\n    line-height: 0;\n    border-radius: 0;\n}\n\n.form-horizontal{\n    .col-form-label,\n    .label-on-right{\n        padding: 10px 5px 0 15px;\n        text-align: right;\n        max-width: 180px;\n    }\n\n    .checkbox-radios{\n        margin-bottom: 15px;\n\n        .form-check:first-child{\n           margin-top: 8px;\n       }\n    }\n\n    .label-on-right{\n        text-align: left;\n        padding: 10px 15px 0 5px;\n    }\n\n    .form-check-inline{\n        margin-top: 6px;\n    }\n}\n\n// .input-group .input-group-prepend .input-group-text {\n//   padding: 7px 0 10px 10px;\n// }\n// .input-group.no-border .form-control+.input-group-append .input-group-text {\n//   padding: 8px 11px 11px 0;\n// }\n", "button,\ninput,\noptgroup,\nselect,\ntextarea{\n    font-family: $sans-serif-family;\n}\nh1,h2,h3,h4,h5,h6{\n    font-weight: $font-weight-normal;\n}\n\na{\n    color: $primary-color;\n    &:hover,\n    &:focus{\n        color: $primary-color;\n    }\n}\nh1, .h1 {\n    font-size: $font-size-h1;\n    line-height: 1.15;\n    margin-bottom: $margin-base-vertical * 2;\n\n    small{\n        font-weight: $font-weight-bold;\n        text-transform: uppercase;\n        opacity: .8;\n    }\n}\nh2, .h2{\n    font-size: $font-size-h2;\n    margin-bottom: $margin-base-vertical * 2;\n}\nh3, .h3{\n    font-size: $font-size-h3;\n    margin-bottom: $margin-base-vertical * 2;\n    line-height: 1.4em;\n}\nh4, .h4{\n    font-size: $font-size-h4;\n    line-height: 1.45em;\n    margin-top: $margin-base-vertical * 2;\n    margin-bottom: $margin-base-vertical;\n\n    & + .category,\n    &.title + .category{\n        margin-top: -10px;\n    }\n}\nh5, .h5 {\n    font-size: $font-size-h5;\n    line-height: 1.4em;\n    margin-bottom: 15px;\n}\nh6, .h6{\n    font-size: $font-size-h6;\n    font-weight: $font-weight-bold;\n    text-transform: uppercase;\n}\np{\n    &.description{\n        font-size: 1.14em;\n    }\n}\n\n// i.fa{\n//     font-size: 18px;\n//     position: relative;\n//     top: 1px;\n// }\n\n.title{\n    font-weight: $font-weight-bold;\n\n    &.title-up{\n        text-transform: uppercase;\n\n        a{\n            color: $black-color;\n            text-decoration: none;\n        }\n    }\n    & + .category{\n        margin-top: -10px;\n    }\n}\n\n.description,\n.card-description,\n.footer-big p,\n.card .footer .stats{\n    color: $dark-gray;\n    font-weight: $font-weight-light;\n}\n.category,\n.card-category{\n    text-transform: capitalize;\n    font-weight: $font-weight-normal;\n    color: $dark-gray;\n    font-size: $font-size-mini;\n}\n\n.card-category{\n    font-size: $font-size-h6;\n}\n\n.text-primary,\na.text-primary:focus, a.text-primary:hover {\n  color: $brand-primary !important;\n}\n.text-info,\na.text-info:focus, a.text-info:hover {\n  color: $brand-info !important;\n}\n.text-success,\na.text-success:focus, a.text-success:hover {\n  color: $brand-success !important;\n}\n.text-warning,\na.text-warning:focus, a.text-warning:hover {\n  color: $brand-warning !important;\n}\n.text-danger,\na.text-danger:focus, a.text-danger:hover {\n  color: $brand-danger !important;\n}\n\n.text-gray,\na.text-gray:focus, a.text-gray:hover{\n    color: $light-gray !important;\n}\n\n\n.blockquote{\n    border-left: none;\n    border: 1px solid $default-color;\n    padding: 20px;\n    font-size: $font-size-blockquote;\n    line-height: 1.8;\n\n    small{\n        color: $default-color;\n        font-size: $font-size-small;\n        text-transform: uppercase;\n    }\n\n    &.blockquote-primary{\n        border-color: $primary-color;\n        color: $primary-color;\n\n        small{\n            color: $primary-color;\n        }\n    }\n\n    &.blockquote-danger{\n        border-color: $danger-color;\n        color: $danger-color;\n\n        small{\n            color: $danger-color;\n        }\n    }\n\n    &.blockquote-white{\n        border-color: $opacity-8;\n        color: $white-color;\n\n        small{\n            color: $opacity-8;\n        }\n    }\n}\n", "body{\n    color: $black-color;\n    font-size: $font-size-base;\n    font-family: $sans-serif-family;\n    -moz-osx-font-smoothing: grayscale;\n    -webkit-font-smoothing: antialiased;\n}\n\n.main{\n    position: relative;\n    background: $white-color;\n}\n/* Animations */\n.nav-pills .nav-link,\n.navbar,\n.nav-tabs .nav-link,\n.sidebar .nav a,\n.sidebar .nav a i,\n.animation-transition-general,\n.tag,\n.tag [data-role=\"remove\"],\n.animation-transition-general{\n    @include transition($general-transition-time, $transition-ease);\n}\n\n//transition for dropdown caret\n.dropdown-toggle:after,\n.bootstrap-switch-label:before,\n.caret{\n    @include transition($fast-transition-time, $transition-ease);\n}\n\n.dropdown-toggle[aria-expanded=\"true\"]:after,\na[data-toggle=\"collapse\"][aria-expanded=\"true\"] .caret,\n.card-collapse .card a[data-toggle=\"collapse\"][aria-expanded=\"true\"] i,\n.card-collapse .card a[data-toggle=\"collapse\"].expanded i{\n    @include rotate-180();\n}\n\n.button-bar{\n    display: block;\n    position: relative;\n    width: 22px;\n    height: 1px;\n    border-radius: 1px;\n    background: $white-bg;\n\n    & + .button-bar{\n        margin-top: 7px;\n    }\n\n    &:nth-child(2){\n        width: 17px;\n    }\n}\n\n.caret{\n    display: inline-block;\n    width: 0;\n    height: 0;\n    margin-left: 2px;\n    vertical-align: middle;\n    border-top: 4px dashed;\n    border-top: 4px solid\\9;\n    border-right: 4px solid transparent;\n    border-left: 4px solid transparent;\n}\n\n.pull-left{\n  float: left;\n}\n.pull-right{\n  float: right;\n}\n\n\n.offline-doc {\n  .navbar.navbar-transparent{\n    padding-top: 25px;\n    border-bottom: none;\n\n    .navbar-minimize {\n      display: none;\n    }\n    .navbar-brand,\n    .collapse .navbar-nav .nav-link {\n      color: $white-color !important;\n    }\n  }\n  .footer {\n    z-index: 3 !important;\n  }\n  .page-header{\n    .page-header-image {\n      position: absolute;\n      background-size: cover;\n      background-position: center center;\n      width: 100%;\n      height: 100%;\n      z-index: -1;\n    }\n    .container {\n      z-index: 3;\n    }\n    &:after {\n      background-color: rgba(0, 0, 0, 0.5);\n      content: \"\";\n      display: block;\n      height: 100%;\n      left: 0;\n      position: absolute;\n      top: 0;\n      width: 100%;\n      z-index: 2;\n    }\n  }\n}\n\n.fixed-plugin {\n  .dropdown-menu li {\n    padding: 2px !important;\n  }\n}\n\n// badge color\n\n.badge{\n  &.badge-default{\n      @include badge-color($default-color);\n  }\n  &.badge-primary{\n      @include badge-color($primary-color);\n  }\n  &.badge-info{\n      @include badge-color($info-color);\n  }\n  &.badge-success{\n      @include badge-color($success-color);\n  }\n  &.badge-warning{\n      @include badge-color($warning-color);\n  }\n  &.badge-danger{\n      @include badge-color($danger-color);\n  }\n  &.badge-neutral{\n      @include badge-color($white-color);\n      color: inherit;\n  }\n}\n\n.card-user {\n  form {\n    .form-group {\n      margin-bottom: 20px;\n    }\n  }\n}\n", ".from-check,\n.form-check-radio {\n    margin-bottom: 12px;\n    position: relative;\n}\n\n.form-check {\n  padding-left: 0;\n  margin-bottom: .5rem;\n\n    .form-check-label{\n        display: inline-block;\n        position: relative;\n        cursor: pointer;\n        padding-left: 35px;\n        line-height: 26px;\n        margin-bottom: 0;\n    }\n\n    .form-check-sign::before,\n    .form-check-sign::after {\n        content: \" \";\n        display: inline-block;\n        position: absolute;\n        width: 24px;\n        height: 24px;\n        left: 0;\n        cursor: pointer;\n        border-radius: 6px;\n        top: 0;\n        background-color: #AAA7A4;\n        -webkit-transition: opacity 0.3s linear;\n        -moz-transition: opacity 0.3s linear;\n        -o-transition: opacity 0.3s linear;\n        -ms-transition: opacity 0.3s linear;\n        transition: opacity 0.3s linear;\n    }\n    .form-check-sign::after {\n        font-family: 'FontAwesome';\n        content: \"\\f00c\";\n        top: -1px;\n        text-align: center;\n        font-size: 15px;\n        opacity: 0;\n        color: #FFF;\n        border: 0;\n        background-color: inherit;\n    }\n    &.disabled{\n        .form-check-label{\n            color: $dark-gray;\n            opacity: .5;\n            cursor: not-allowed;\n        }\n    }\n\n}\n\n.form-check.disabled .form-check-label,\n.form-check.disabled .form-check-label {\n\n}\n\n.form-check input[type=\"checkbox\"],\n.form-check-radio input[type=\"radio\"]{\n    opacity: 0;\n    position: absolute;\n    visibility: hidden;\n}\n.form-check input[type=\"checkbox\"]:checked + .form-check-sign::after{\n    opacity: 1;\n}\n\n.form-control input[type=\"checkbox\"]:disabled + .form-check-sign::before,\n.checkbox input[type=\"checkbox\"]:disabled + .form-check-sign::after{\n    cursor: not-allowed;\n}\n\n.form-check .form-check-label input[type=\"checkbox\"]:disabled + .form-check-sign,\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign{\n    pointer-events: none !important;\n}\n\n.form-check-radio{\n  margin-left: -3px;\n\n    .form-check-label{\n        padding-left: 2rem;\n    }\n    &.disabled{\n        .form-check-label{\n            color: $dark-gray;\n            opacity: .5;\n            cursor: not-allowed;\n        }\n    }\n}\n\n.form-check-radio .form-check-sign::before{\n    font-family: 'FontAwesome';\n    content: \"\\f10c\";\n    font-size: 22px;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    display: inline-block;\n    position: absolute;\n    opacity: .50;\n    left: 5px;\n    top: -5px;\n}\n\n.form-check-label input[type=\"checkbox\"]:checked + .form-check-sign:before{\n    background-color: #66615B;\n}\n\n.form-check-radio input[type=\"radio\"] + .form-check-sign:after,\n.form-check-radio input[type=\"radio\"] {\n    opacity: 0;\n    @include transition-opacity(0.3s, linear);\n    content:\" \";\n    display: block;\n}\n\n.form-check-radio input[type=\"radio\"]:checked + .form-check-sign::after {\n    font-family: 'FontAwesome';\n    content: \"\\f192\";\n    top: -5px;\n    position: absolute;\n    left: 5px;\n    opacity: 1;\n    font-size: 22px;\n}\n\n.form-check-radio input[type=\"radio\"]:checked + .form-check-sign::after{\n    opacity: 1;\n}\n\n\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign::before,\n.form-check-radio input[type=\"radio\"]:disabled + .form-check-sign::after {\n    color: $dark-gray;\n}\n", ".navbar{\n    padding-top: $navbar-padding-base;\n    padding-bottom: $navbar-padding-base;\n    min-height: 53px;\n    margin-bottom: 20px;\n\n    a{\n        vertical-align: middle;\n\n        &:not(.btn):not(.dropdown-item){\n            color: $white-color;\n        }\n\n        &.dropdown-item{\n            color: $default-color;\n        }\n    }\n\n\n\n    &.bg-white{\n      .input-group .form-control,\n      .input-group.no-border .form-control{\n        color: $default-color;\n\n        @include placeholder(){\n          color: $default-color;\n        };\n      }\n      .input-group-prepend .input-group-text i,\n      .input-group-append .input-group-text i{\n        color: $default-color;\n        opacity: .5;\n      }\n    }\n\n    .form-group,\n    .input-group{\n      margin: 0;\n      margin-left: -3px;\n      margin-right: 5px;\n\n      .form-group-addon,\n      .input-group-prepend .input-group-text,\n      .input-group-append .input-group-text{\n        color: $default-color;\n\n        i {\n          opacity: 1;\n        }\n      }\n\n      &.no-border{\n        .form-control{\n          color: $default-color;\n\n          @include placeholder(){\n            color: $default-color;\n          };\n        }\n      }\n    }\n\n    p{\n        display: inline-block;\n        margin: 0;\n        line-height: 1.8em;\n        font-size: 1em;\n        font-weight: 400;\n    }\n\n    &.navbar-absolute{\n        position: absolute;\n        width: 100%;\n        padding-top: 10px;\n        z-index: 1029;\n    }\n\n    .documentation &{\n        &.fixed-top{\n            left: 0;\n            width: initial;\n        }\n    }\n\n    .navbar-wrapper{\n        display: inline-flex;\n        align-items: center;\n\n        .navbar-minimize{\n            padding-right: 10px;\n\n            .btn{\n                margin: 0;\n            }\n        }\n\n        .navbar-toggle{\n            .navbar-toggler{\n                padding-left: 0;\n            }\n\n            &:hover{\n                & .navbar-toggler-bar.bar2{\n                    width: 22px;\n                }\n            }\n        }\n    }\n\n\n\n    .navbar-nav{\n        &.navbar-logo{\n            position: absolute;\n            left: 0;\n            right: 0;\n            margin: 0 auto;\n            width: 49px;\n            top: -4px;\n        }\n\n        .nav-link.btn{\n            padding: $padding-btn-vertical $padding-btn-horizontal;\n            &.btn-lg{\n                padding: $padding-large-vertical $padding-large-horizontal;\n            }\n            &.btn-sm{\n                padding: $padding-small-vertical $padding-small-horizontal;\n            }\n        }\n\n        .nav-link{\n            text-transform: uppercase;\n            font-size: $font-size-mini;\n            padding: $padding-base-vertical $padding-base-horizontal;\n            line-height: $line-height-nav-link;\n            margin-right: 3px;\n\n            i.fa + p,\n            i.nc-icon + p{\n                margin-left: 3px;\n            }\n\n            i.fa,\n            i.nc-icon{\n                font-size: 18px;\n                position: relative;\n                top: 3px;\n                text-align: center;\n                width: 21px;\n            }\n\n            i.nc-icon{\n                top: 4px;\n                font-size: 16px;\n            }\n\n            &.profile-photo{\n                .profile-photo-small{\n                    width: 27px;\n                    height: 27px;\n                }\n            }\n\n            &.disabled{\n                opacity: .5;\n                color: $white-color;\n            }\n        }\n\n        .nav-item.active .nav-link:not(.btn),\n        .nav-item .nav-link:not(.btn):focus,\n        .nav-item .nav-link:not(.btn):hover,\n        .nav-item .nav-link:not(.btn):active{\n            border-radius: $border-radius-small;\n            color: $default-color;\n        }\n    }\n\n    .logo-container{\n        width: 27px;\n        height: 27px;\n        overflow: hidden;\n        margin: 0 auto;\n        border-radius: 50%;\n        border: 1px solid transparent;\n    }\n\n    .navbar-brand{\n        text-transform: capitalize;\n        font-size: $font-size-large-navbar;\n        padding-top: $padding-base-vertical;\n        padding-bottom: $padding-base-vertical;\n        line-height: $line-height-nav-link;\n    }\n\n    .navbar-toggler{\n        width: 37px;\n        height: 27px;\n        vertical-align: middle;\n        outline: 0;\n        cursor: pointer;\n\n        & .navbar-toggler-bar.navbar-kebab{\n            width: 3px;\n            height: 3px;\n            border-radius: 50%;\n            margin: 0 auto;\n        }\n    }\n\n    .button-dropdown{\n        .navbar-toggler-bar:nth-child(2){\n            width: 17px;\n        }\n    }\n\n    &.navbar-transparent{\n      background-color: $transparent-bg !important;\n      box-shadow: none;\n      border-bottom: 1px solid #ddd;\n\n      a:not(.dropdown-item):not(.btn){\n        color: $default-color;\n\n        &.disabled{\n          opacity: .5;\n          color: $default-color;\n         }\n       }\n\n       .button-bar{\n           background: $default-color;\n       }\n\n      .nav-item .nav-link:not(.btn){\n        color: $default-color;\n      }\n      .nav-item.active .nav-link:not(.btn),\n      .nav-item .nav-link:not(.btn):focus,\n      .nav-item .nav-link:not(.btn):hover,\n      .nav-item .nav-link:not(.btn):focus:hover,\n      .nav-item .nav-link:not(.btn):active {\n        color: $primary-color;\n      }\n    }\n\n    &.bg-white {\n        a:not(.dropdown-item):not(.btn){\n            color: $default-color;\n\n            &.disabled{\n                opacity: .5;\n                color: $default-color;\n            }\n        }\n\n        .button-bar{\n            background: $default-color;\n        }\n\n        .nav-item.active .nav-link:not(.btn),\n        .nav-item .nav-link:not(.btn):focus,\n        .nav-item .nav-link:not(.btn):hover,\n        .nav-item .nav-link:not(.btn):active{\n            color: $info-color;\n        }\n\n        .logo-container{\n            border: 1px solid $default-color;\n        }\n    }\n\n    .navbar-collapse {\n      .nav-item {\n        a {\n          font-size: $font-size-base;\n        }\n      }\n    }\n}\n\n.bg-default{\n    background-color: $default-color !important;\n}\n\n.bg-primary{\n    background-color: $primary-color !important;\n}\n\n.bg-info{\n    background-color: $info-color !important;\n}\n\n.bg-success{\n    background-color: $success-color !important;\n}\n\n.bg-danger{\n    background-color: $danger-color !important;\n}\n\n.bg-warning{\n    background-color: $warning-color !important;\n}\n\n.bg-white{\n    background-color: $white-color !important;\n}\n", ".page-header{\n    min-height: 100vh;\n    max-height: 1000px;\n    padding: 0;\n    color: $white-color;\n    position: relative;\n\n    .page-header-image{\n        position: absolute;\n        background-size: cover;\n        background-position: center center;\n        width: 100%;\n        height: 100%;\n        z-index: -1;\n    }\n\n    .content-center{\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        z-index: 2;\n        -ms-transform: translate(-50%, -50%);\n        -webkit-transform: translate(-50%, -50%);\n        transform: translate(-50%, -50%);\n        text-align: center;\n        color: #FFFFFF;\n        padding: 0 15px;\n        width: 100%;\n        max-width: 880px;\n\n    }\n\n    footer{\n        position: absolute;\n        bottom: 0;\n        width: 100%;\n    }\n\n    .container{\n        height: 100%;\n        z-index: 1;\n    }\n\n    .category,\n    .description{\n        color: $opacity-8;\n    }\n\n    &.page-header-small{\n        min-height: 60vh;\n        max-height: 440px;\n    }\n\n    &.page-header-mini{\n        min-height: 40vh;\n        max-height: 340px;\n    }\n\n    .title{\n        margin-bottom: 15px;\n    }\n    .title + h4{\n        margin-top: 10px;\n    }\n\n    &:after,\n    &:before{\n        position: absolute;\n        z-index: 0;\n        width: 100%;\n        height: 100%;\n        display: block;\n        left: 0;\n        top: 0;\n        content: \"\";\n    }\n\n    &:before{\n        background-color: rgba(0,0,0,.3);\n    }\n\n    &[filter-color=\"orange\"]{\n        @include linear-gradient(rgba($black-color,.20), rgba(224, 23, 3, 0.6));\n    }\n}\n", "@mixin linear-gradient($color1, $color2){\n    background: $color1; /* For browsers that do not support gradients */\n    background: -webkit-linear-gradient(90deg, $color1 , $color2); /* For Safari 5.1 to 6.0 */\n    background: -o-linear-gradient(90deg, $color1, $color2); /* For Opera 11.1 to 12.0 */\n    background: -moz-linear-gradient(90deg, $color1, $color2); /* For Firefox 3.6 to 15 */\n    background: linear-gradient(0deg, $color1 , $color2); /* Standard syntax */\n}\n", ".dropdown,\n.dropup {\n  > .dropdown-menu:first-of-type {\n    display: block;\n    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;\n  }\n  .dropdown-menu {\n      @include opacity(0);\n      @include box-shadow($dropdown-shadow);\n      visibility: hidden;\n      position: absolute;\n\n      &[x-placement=\"top-start\"] {\n        @include transform-translate-y-dropdown(0px);\n      }\n      &[x-placement=\"bottom-start\"] {\n        @include transform-translate-y-dropdown(0px);\n      }\n  }\n\n  &.show .dropdown-menu {\n    @include opacity(1);\n    visibility: visible;\n\n    &[x-placement=\"top-start\"] {\n      @include transform-translate-y-dropdown(-50px);\n      top: auto !important;\n      bottom: 0 !important;\n    }\n    &[x-placement=\"bottom-start\"] {\n      @include transform-translate-y-dropdown(50px);\n      bottom: auto !important;\n      top: 0 !important;\n    }\n  }\n}\n\n.bootstrap-select {\n  .dropdown-menu {\n    li.hidden {\n      display: none;\n    }\n  }\n\n  .bs-searchbox {\n    .form-control {\n      border-radius: 4px;\n      border-bottom-color: #ddd;\n    }\n  }\n}\n\n.dropup .dropdown-menu,\n.dropdown-btn .dropdown-menu {\n    @include transform-translate-y-dropdown(0px);\n    top: auto !important;\n    bottom: 0 !important;\n\n}\n\n.dropup.show .dropdown-menu,\n.dropdown-btn.show .dropdown-menu {\n  opacity: 1;\n  visibility: visible;\n\n  &.show {\n    @include transform-translate-y(-50px);\n  }\n}\n\n.bootstrap-select.show {\n  .dropdown-menu.show {\n    &[x-placement=\"top-start\"] {\n      @include transform-translate-y-dropdown(-57px);\n      top: auto !important;\n      bottom: 0 !important;\n    }\n\n    li:last-child {\n      a:hover {\n        border-radius: 0 0 12px 12px;\n      }\n    }\n  }\n}\n\n.bootstrap-select.dropup.show {\n  &:before {\n    top: -1px !important;\n  }\n\n  &:after {\n    top: -2px !important;\n  }\n}\n.dropdown-menu {\n    background-color: $white-color;\n    border: 0 none;\n    border-radius: $border-radius-extreme;\n    margin-top: 10px;\n    padding: 0px;\n\n    .divider{\n        background-color: $medium-pale-bg;\n        margin: 0px;\n    }\n\n    .dropdown-header{\n        color: $dark-gray;\n        font-size: $font-size-small;\n        padding: $padding-dropdown-vertical $padding-dropdown-horizontal;\n    }\n\n    .no-notification{\n        color: #9A9A9A;\n        font-size: 1.2em;\n        padding: 30px 30px;\n        text-align: center;\n    }\n\n    .dropdown-item{\n        color: $font-color;\n        font-size: $font-size-base;\n        padding: 10px 45px 10px 15px;\n        clear: both;\n        white-space: nowrap;\n        width: 100%;\n        display: block;\n\n       img{\n           margin-top: -3px;\n       }\n\n    }\n    .dropdown-item:focus{\n        outline: 0 !important;\n    }\n\n    .btn-group.select &{\n        min-width: 100%;\n    }\n\n    .dropdown-item:first-child{\n       border-top-left-radius: $border-radius-extreme;\n       border-top-right-radius: $border-radius-extreme;\n    }\n\n    .dropdown-item:last-child{\n        border-bottom-left-radius: $border-radius-extreme;\n        border-bottom-right-radius: $border-radius-extreme;\n    }\n\n    .select & .dropdown-item:first-child{\n        border-radius: 0;\n        border-bottom: 0 none;\n    }\n\n    .dropdown-item:hover,\n    .dropdown-item:focus{\n        color: $white-color !important;\n        opacity: 1;\n        text-decoration: none;\n\n    }\n    .dropdown-item:hover,\n    .dropdown-item:focus{\n        background-color: $default-color;\n    }\n\n    &.dropdown-primary .dropdown-item:hover,\n    &.dropdown-primary .dropdown-item:focus{\n        background-color: $bg-primary;\n    }\n    &.dropdown-info .dropdown-item:hover,\n    &.dropdown-info .dropdown-item:focus{\n        background-color: $bg-info;\n    }\n    &.dropdown-success .dropdown-item:hover,\n    &.dropdown-success .dropdown-item:focus{\n        background-color: $bg-success;\n    }\n    &.dropdown-warning .dropdown-item:hover,\n    &.dropdown-warning .dropdown-item:focus{\n        background-color: $bg-warning;\n    }\n    &.dropdown-danger .dropdown-item:hover,\n    &.dropdown-danger .dropdown-item:focus{\n        background-color: $bg-danger;\n    }\n\n}\n.dropdown-divider{\n  margin: 0 !important;\n}\n\n.btn-group.select.open{\n    overflow: visible;\n}\n.dropdown-menu-right{\n    right: -2px;\n    left: auto;\n}\n\n.navbar-nav .dropdown-menu:before,\n.dropdown .dropdown-menu[x-placement=\"bottom-start\"]:before,\n.dropdown .dropdown-menu[x-placement=\"bottom-end\"]:before,\n.card.card-just-text .dropdown .dropdown-menu:before,\n.card-just-text .dropdown .dropdown-menu:before,\n.dropdown-btn .dropdown-menu:before{\n    border-bottom: 11px solid $medium-pale-bg;\n    border-left: 11px solid rgba(0, 0, 0, 0);\n    border-right: 11px solid rgba(0, 0, 0, 0);\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: 12px;\n    top: -12px;\n}\n\n.navbar-nav .dropdown-menu:after,\n.dropdown .dropdown-menu[x-placement=\"bottom-start\"]:after,\n.dropdown .dropdown-menu[x-placement=\"bottom-end\"]:after,\n.card.card-just-text .dropdown .dropdown-menu:after,\n.card-just-text .dropdown .dropdown-menu:after,\n.dropdown-btn .dropdown-menu:after{\n    border-bottom: 11px solid $white-color;\n    border-left: 11px solid rgba(0, 0, 0, 0);\n    border-right: 11px solid rgba(0, 0, 0, 0);\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: 12px;\n    top: -11px;\n}\n\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"top-start\"]:before,\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"bottom-start\"]:before{\n    left: 30px !important;\n    right: auto;\n}\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"top-start\"]:after,\n.dropdown .dropdown-menu.dropdown-notification[x-placement=\"bottom-start\"]:after{\n    left: 30px !important;\n    right: auto;\n}\n//  the style for opening dropdowns on mobile devices; for the desktop version check the _responsive.scss file\n//  code from _responsive.scss\n\n@media screen and (min-width: 768px){\n    .navbar-form {\n      margin-top: 21px;\n      margin-bottom: 21px;\n      padding-left: 5px;\n      padding-right: 5px;\n    }\n    .navbar-search-form{\n      display: none;\n    }\n    .navbar-nav .dropdown-item .dropdown-menu,\n    .dropdown .dropdown-menu,\n    .dropdown-btn .dropdown-menu{\n      transform: translate3d(0px, -40px, 0px);\n      transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;\n    }\n    .navbar-nav .dropdown-item.show .dropdown-menu,\n    .dropdown.show .dropdown-menu,\n    .dropdown-btn.show .dropdown-menu{\n      transform: translate3d(0px, 0px, 0px);\n      visibility: visible !important;\n    }\n    .bootstrap-select .dropdown-menu{\n      -webkit-transition: all 150ms linear;\n      -moz-transition: all 150ms linear;\n      -o-transition: all 150ms linear;\n      -ms-transition: all 150ms linear;\n      transition: all 150ms linear;\n    }\n    .bootstrap-datetimepicker-widget{\n      visibility: visible !important;\n    }\n\n    .bootstrap-select .show .dropdown-menu{\n      transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;\n      transform: translate3d(0px, 0px, 0px);\n    }\n\n    .navbar-nav.navbar-right li .dropdown-menu:before,\n    .navbar-nav.navbar-right li .dropdown-menu:after{\n        left: auto;\n        right: 12px;\n    }\n\n\n    .footer:not(.footer-big){\n        nav ul{\n           li:first-child{\n             margin-left: 0;\n           }\n        }\n    }\n\n// no dragging the others navs in page\n    body > .navbar-collapse.collapse{\n        display: none !important;\n    }\n}\n\n.dropdown-sharing{\n    .dropup-item{\n        color: $font-color;\n        font-size: $font-size-base;\n\n        .social-line{\n            line-height: 28px;\n            padding: 10px 20px 5px 20px !important;\n\n            [class*=\"icon-\"]{\n                font-size: 20px;\n            }\n        }\n        &:last-child{\n            margin: 0 13px;\n            display: block;\n        }\n        .btn{\n            margin: 10px;\n        }\n    }\n\n    .dropup-item:hover,\n    .dropup-item:focus{\n        .social-line,\n        .action-line{\n            background-color: $white-color;\n            color: $font-color;\n            opacity: 1;\n            text-decoration: none;\n        }\n    }\n}\n.show .dropdown-sharing,\n.show .dropdown-actions{\n    margin-bottom: 1px;\n}\n\n.dropdown-actions{\n    .dropdown-item{\n        margin: -15px 35px;\n        .action-line{\n            padding: 5px 10px;\n            line-height: 24px;\n            font-weight: bold;\n            [class*=\"icon-\"]{\n                font-size: 24px;\n            }\n            .col-sm-9{\n                line-height: 34px;\n            }\n        }\n        .link-danger{\n            color: $danger-color;\n            &:hover, &:active, &:focus{\n                color: $danger-color;\n            }\n        }\n    }\n    li:hover,\n    li:focus{\n        a{\n            color: $font-color;\n            opacity: 1;\n            text-decoration: none;\n        }\n    }\n    .action-line{\n        .icon-simple{\n            margin-left: -15px;\n        }\n    }\n}\n.dropdown .dropdown-menu[x-placement=\"top-start\"]:before,\n.dropdown .dropdown-menu[x-placement=\"top-end\"]:before,\n.dropup .dropdown-menu:before{\n    border-top: 11px solid #DCD9D1;\n    border-left: 11px solid transparent;\n    border-right: 11px solid transparent;\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: 12px;\n    bottom: -12px;\n}\n\n.dropdown .dropdown-menu[x-placement=\"top-start\"]:after,\n.dropdown .dropdown-menu[x-placement=\"top-end\"]:after,\n.dropup .dropdown-menu:after{\n    border-top: 11px solid #FFF;\n    border-left: 11px solid transparent;\n    border-right: 11px solid transparent;\n    content: \"\";\n    display: inline-block;\n    position: absolute;\n    right: 12px;\n    bottom: -11px;\n}\n\n.dropup,\n.dropdown{\n    .dropdown-toggle:after{\n        margin-left: 0;\n    }\n}\n\n.dropdown-notification{\n    .dropdown-notification-list{\n            .notification-item{\n                border-bottom: 1px solid #F1EAE0;\n                font-size: 16px;\n                color: #66615b;\n\n                .notification-text{\n                    padding-left: 40px;\n                    position: relative;\n                    min-width: 330px;\n                    min-height: 70px;\n                    white-space: normal;\n\n\n                    .label{\n                        display: block;\n                        position: absolute;\n                        top: 50%;\n                        margin-top: -12px;\n                        left: 7px;\n                    }\n                    .message{\n                        font-size: 0.9em;\n                        line-height: 0.7;\n                        margin-left: 10px;\n                    }\n                    .time{\n                        color: #9A9A9A;\n                        font-size: 0.7em;\n                        margin-left: 10px;\n                    }\n                }\n                .read-notification{\n                    font-size: 12px;\n                    opacity: 0;\n                    position: absolute;\n                    right: 5px;\n                    top: 50%;\n                    margin-top: -12px;\n                }\n                &:hover{\n                    text-decoration: none;\n\n                    .notification-text{\n                        color: #66615b;\n                        background-color: #F0EFEB !important;\n                    }\n                    .read-notification{\n                        opacity: 1 !important;\n                    }\n                }\n            }\n\n    }\n    .dropdown-footer{\n        background-color: #E8E7E3;\n        border-radius: 0 0 8px 8px;\n\n        .dropdown-footer-menu{\n            list-style: outside none none;\n            padding: 0px 5px;\n            li{\n                display: inline-block;\n                text-align: left;\n                padding: 0 10px;\n\n                a{\n                    color: #9C9B99;\n                    font-size: 0.9em;\n                    line-height: 35px;\n                }\n            }\n        }\n    }\n}\n", ".alert {\n    border: 0;\n    border-radius: $border-radius-small;\n    color: $white-color;\n    padding-top: .9rem;\n    padding-bottom: .9rem;\n    position: relative;\n\n    &.alert-success {\n      background-color: lighten($success-color, 5%);\n    }\n\n    &.alert-danger {\n      background-color: lighten($danger-color, 5%);\n    }\n\n    &.alert-warning {\n      background-color: lighten($warning-color, 5%);\n    }\n\n    &.alert-info {\n      background-color: lighten($info-color, 5%);\n    }\n\n    &.alert-primary {\n      background-color: lighten($primary-color, 5%);\n    }\n\n    &.alert-default {\n      background-color: lighten($default-color, 5%);\n    }\n\n    .close {\n      color: $white-color;\n      opacity: .9;\n      text-shadow: none;\n      line-height: 0;\n      outline: 0;\n\n      i.fa,\n      i.nc-icon {\n          font-size: 14px !important;\n      }\n\n      &:hover,\n      &:focus {\n        opacity: 1;\n      }\n    }\n\n    span[data-notify=\"icon\"]{\n        font-size: 27px;\n        display: block;\n        left: 19px;\n        position: absolute;\n        top: 50%;\n        margin-top: -11px;\n    }\n\n    button.close{\n        position: absolute;\n        right: 10px;\n        top: 50%;\n        margin-top: -13px;\n        width: 25px;\n        height: 25px;\n        padding: 3px;\n    }\n\n    .close ~ span{\n        display: block;\n        max-width: 89%;\n    }\n\n    &.alert-with-icon{\n        padding-left: 65px;\n    }\n}\n", "img{\n    max-width: 100%;\n    border-radius: $border-radius-small;\n}\n.img-raised{\n    box-shadow: $box-shadow-raised;\n}\n", "/*--------------------------------\n\nnucleo-icons Web Font - built using nucleoapp.com\nLicense - nucleoapp.com/license/\n\n-------------------------------- */\n@font-face {\n  font-family: 'nucleo-icons';\n  src: url('../fonts/nucleo-icons.eot');\n  src: url('../fonts/nucleo-icons.eot') format('embedded-opentype'), url('../fonts/nucleo-icons.woff2') format('woff2'), url('../fonts/nucleo-icons.woff') format('woff'), url('../fonts/nucleo-icons.ttf') format('truetype'), url('../fonts/nucleo-icons.svg') format('svg');\n  font-weight: normal;\n  font-style: normal;\n}\n/*------------------------\n\tbase class definition\n-------------------------*/\n.nc-icon {\n  display: inline-block;\n  font: normal normal normal 14px/1 'nucleo-icons';\n  font-size: inherit;\n  speak: none;\n  text-transform: none;\n  /* Better Font Rendering */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n/*------------------------\n  change icon size\n-------------------------*/\n.nc-icon.lg {\n  font-size: 1.33333333em;\n  vertical-align: -16%;\n}\n.nc-icon.x2 {\n  font-size: 2em;\n}\n.nc-icon.x3 {\n  font-size: 3em;\n}\n/*----------------------------------\n  add a square/circle background\n-----------------------------------*/\n.nc-icon.square,\n.nc-icon.circle {\n  padding: 0.33333333em;\n  vertical-align: -16%;\n  background-color: #eee;\n}\n.nc-icon.circle {\n  border-radius: 50%;\n}\n/*------------------------\n  list icons\n-------------------------*/\n.nc-icon-ul {\n  padding-left: 0;\n  margin-left: 2.14285714em;\n  list-style-type: none;\n}\n.nc-icon-ul > li {\n  position: relative;\n}\n.nc-icon-ul > li > .nc-icon {\n  position: absolute;\n  left: -1.57142857em;\n  top: 0.14285714em;\n  text-align: center;\n}\n.nc-icon-ul > li > .nc-icon.lg {\n  top: 0;\n  left: -1.35714286em;\n}\n.nc-icon-ul > li > .nc-icon.circle,\n.nc-icon-ul > li > .nc-icon.square {\n  top: -0.19047619em;\n  left: -1.9047619em;\n}\n/*------------------------\n  spinning icons\n-------------------------*/\n.nc-icon.spin {\n  -webkit-animation: nc-icon-spin 2s infinite linear;\n  -moz-animation: nc-icon-spin 2s infinite linear;\n  animation: nc-icon-spin 2s infinite linear;\n}\n@-webkit-keyframes nc-icon-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n  }\n}\n@-moz-keyframes nc-icon-spin {\n  0% {\n    -moz-transform: rotate(0deg);\n  }\n  100% {\n    -moz-transform: rotate(360deg);\n  }\n}\n@keyframes nc-icon-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n    -moz-transform: rotate(0deg);\n    -ms-transform: rotate(0deg);\n    -o-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    -moz-transform: rotate(360deg);\n    -ms-transform: rotate(360deg);\n    -o-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n/*------------------------\n  rotated/flipped icons\n-------------------------*/\n.nc-icon.rotate-90 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);\n  -webkit-transform: rotate(90deg);\n  -moz-transform: rotate(90deg);\n  -ms-transform: rotate(90deg);\n  -o-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n.nc-icon.rotate-180 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n  -webkit-transform: rotate(180deg);\n  -moz-transform: rotate(180deg);\n  -ms-transform: rotate(180deg);\n  -o-transform: rotate(180deg);\n  transform: rotate(180deg);\n}\n.nc-icon.rotate-270 {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);\n  -webkit-transform: rotate(270deg);\n  -moz-transform: rotate(270deg);\n  -ms-transform: rotate(270deg);\n  -o-transform: rotate(270deg);\n  transform: rotate(270deg);\n}\n.nc-icon.flip-y {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);\n  -webkit-transform: scale(-1, 1);\n  -moz-transform: scale(-1, 1);\n  -ms-transform: scale(-1, 1);\n  -o-transform: scale(-1, 1);\n  transform: scale(-1, 1);\n}\n.nc-icon.flip-x {\n  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);\n  -webkit-transform: scale(1, -1);\n  -moz-transform: scale(1, -1);\n  -ms-transform: scale(1, -1);\n  -o-transform: scale(1, -1);\n  transform: scale(1, -1);\n}\n/*------------------------\n\tfont icons\n-------------------------*/\n\n.nc-air-baloon::before {\n    content: \"\\ea01\";\n}\n\n.nc-album-2::before {\n    content: \"\\ea02\";\n}\n\n.nc-alert-circle-i::before {\n    content: \"\\ea04\";\n}\n\n.nc-align-center::before {\n    content: \"\\ea03\";\n}\n\n.nc-align-left-2::before {\n    content: \"\\ea05\";\n}\n\n.nc-ambulance::before {\n    content: \"\\ea06\";\n}\n\n.nc-app::before {\n    content: \"\\ea07\";\n}\n\n.nc-atom::before {\n    content: \"\\ea08\";\n}\n\n.nc-badge::before {\n    content: \"\\ea09\";\n}\n\n.nc-bag-16::before {\n    content: \"\\ea0a\";\n}\n\n.nc-bank::before {\n    content: \"\\ea0b\";\n}\n\n.nc-basket::before {\n    content: \"\\ea0c\";\n}\n\n.nc-bell-55::before {\n    content: \"\\ea0d\";\n}\n\n.nc-bold::before {\n    content: \"\\ea0e\";\n}\n\n.nc-book-bookmark::before {\n    content: \"\\ea0f\";\n}\n\n.nc-bookmark-2::before {\n    content: \"\\ea10\";\n}\n\n.nc-box-2::before {\n    content: \"\\ea11\";\n}\n\n.nc-box::before {\n    content: \"\\ea12\";\n}\n\n.nc-briefcase-24::before {\n    content: \"\\ea13\";\n}\n\n.nc-bulb-63::before {\n    content: \"\\ea14\";\n}\n\n.nc-bullet-list-67::before {\n    content: \"\\ea15\";\n}\n\n.nc-bus-front-12::before {\n    content: \"\\ea16\";\n}\n\n.nc-button-pause::before {\n    content: \"\\ea17\";\n}\n\n.nc-button-play::before {\n    content: \"\\ea18\";\n}\n\n.nc-button-power::before {\n    content: \"\\ea19\";\n}\n\n.nc-calendar-60::before {\n    content: \"\\ea1a\";\n}\n\n.nc-camera-compact::before {\n    content: \"\\ea1b\";\n}\n\n.nc-caps-small::before {\n    content: \"\\ea1c\";\n}\n\n.nc-cart-simple::before {\n    content: \"\\ea1d\";\n}\n\n.nc-chart-bar-32::before {\n    content: \"\\ea1e\";\n}\n\n.nc-chart-pie-36::before {\n    content: \"\\ea1f\";\n}\n\n.nc-chat-33::before {\n    content: \"\\ea20\";\n}\n\n.nc-check-2::before {\n    content: \"\\ea21\";\n}\n\n.nc-circle-10::before {\n    content: \"\\ea22\";\n}\n\n.nc-cloud-download-93::before {\n    content: \"\\ea23\";\n}\n\n.nc-cloud-upload-94::before {\n    content: \"\\ea24\";\n}\n\n.nc-compass-05::before {\n    content: \"\\ea25\";\n}\n\n.nc-controller-modern::before {\n    content: \"\\ea26\";\n}\n\n.nc-credit-card::before {\n    content: \"\\ea27\";\n}\n\n.nc-delivery-fast::before {\n    content: \"\\ea28\";\n}\n\n.nc-diamond::before {\n    content: \"\\ea29\";\n}\n\n.nc-email-85::before {\n    content: \"\\ea2a\";\n}\n\n.nc-favourite-28::before {\n    content: \"\\ea2b\";\n}\n\n.nc-glasses-2::before {\n    content: \"\\ea2c\";\n}\n\n.nc-globe-2::before {\n    content: \"\\ea2d\";\n}\n\n.nc-globe::before {\n    content: \"\\ea2e\";\n}\n\n.nc-hat-3::before {\n    content: \"\\ea2f\";\n}\n\n.nc-headphones::before {\n    content: \"\\ea30\";\n}\n\n.nc-html5::before {\n    content: \"\\ea31\";\n}\n\n.nc-image::before {\n    content: \"\\ea32\";\n}\n\n.nc-istanbul::before {\n    content: \"\\ea33\";\n}\n\n.nc-key-25::before {\n    content: \"\\ea34\";\n}\n\n.nc-laptop::before {\n    content: \"\\ea35\";\n}\n\n.nc-layout-11::before {\n    content: \"\\ea36\";\n}\n\n.nc-lock-circle-open::before {\n    content: \"\\ea37\";\n}\n\n.nc-map-big::before {\n    content: \"\\ea38\";\n}\n\n.nc-minimal-down::before {\n    content: \"\\ea39\";\n}\n\n.nc-minimal-left::before {\n    content: \"\\ea3a\";\n}\n\n.nc-minimal-right::before {\n    content: \"\\ea3b\";\n}\n\n.nc-minimal-up::before {\n    content: \"\\ea3c\";\n}\n\n.nc-mobile::before {\n    content: \"\\ea3d\";\n}\n\n.nc-money-coins::before {\n    content: \"\\ea3e\";\n}\n\n.nc-note-03::before {\n    content: \"\\ea3f\";\n}\n\n.nc-palette::before {\n    content: \"\\ea40\";\n}\n\n.nc-paper::before {\n    content: \"\\ea41\";\n}\n\n.nc-pin-3::before {\n    content: \"\\ea42\";\n}\n\n.nc-planet::before {\n    content: \"\\ea43\";\n}\n\n.nc-refresh-69::before {\n    content: \"\\ea44\";\n}\n\n.nc-ruler-pencil::before {\n    content: \"\\ea45\";\n}\n\n.nc-satisfied::before {\n    content: \"\\ea46\";\n}\n\n.nc-scissors::before {\n    content: \"\\ea47\";\n}\n\n.nc-send::before {\n    content: \"\\ea48\";\n}\n\n.nc-settings-gear-65::before {\n    content: \"\\ea49\";\n}\n\n.nc-settings::before {\n    content: \"\\ea4a\";\n}\n\n.nc-share-66::before {\n    content: \"\\ea4b\";\n}\n\n.nc-shop::before {\n    content: \"\\ea4c\";\n}\n\n.nc-simple-add::before {\n    content: \"\\ea4d\";\n}\n\n.nc-simple-delete::before {\n    content: \"\\ea4e\";\n}\n\n.nc-simple-remove::before {\n    content: \"\\ea4f\";\n}\n\n.nc-single-02::before {\n    content: \"\\ea50\";\n}\n\n.nc-single-copy-04::before {\n    content: \"\\ea51\";\n}\n\n.nc-sound-wave::before {\n    content: \"\\ea52\";\n}\n\n.nc-spaceship::before {\n    content: \"\\ea53\";\n}\n\n.nc-sun-fog-29::before {\n    content: \"\\ea54\";\n}\n\n.nc-support-17::before {\n    content: \"\\ea55\";\n}\n\n.nc-tablet-2::before {\n    content: \"\\ea56\";\n}\n\n.nc-tag-content::before {\n    content: \"\\ea57\";\n}\n\n.nc-tap-01::before {\n    content: \"\\ea58\";\n}\n\n.nc-tie-bow::before {\n    content: \"\\ea59\";\n}\n\n.nc-tile-56::before {\n    content: \"\\ea5a\";\n}\n\n.nc-time-alarm::before {\n    content: \"\\ea5b\";\n}\n\n.nc-touch-id::before {\n    content: \"\\ea5c\";\n}\n\n.nc-trophy::before {\n    content: \"\\ea5d\";\n}\n\n.nc-tv-2::before {\n    content: \"\\ea5e\";\n}\n\n.nc-umbrella-13::before {\n    content: \"\\ea5f\";\n}\n\n.nc-user-run::before {\n    content: \"\\ea60\";\n}\n\n.nc-vector::before {\n    content: \"\\ea61\";\n}\n\n.nc-watch-time::before {\n    content: \"\\ea62\";\n}\n\n.nc-world-2::before {\n    content: \"\\ea63\";\n}\n\n.nc-zoom-split::before {\n    content: \"\\ea64\";\n}\n\n\n/* all icon font classes list here */\n", ".table{\n\n    .img-wrapper{\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      overflow: hidden;\n      margin: 0 auto;\n    }\n\n    .img-row{\n      max-width: 60px;\n      width: 60px;\n    }\n\n    .form-check{\n        margin: 0;\n\n        & label .form-check-sign::before,\n        & label .form-check-sign::after{\n            top: -17px;\n            left: 4px;\n        }\n    }\n\n    .btn{\n        margin: 0;\n    }\n\n    small,.small{\n      font-weight: 300;\n    }\n\n    .card-tasks .card-body &{\n        margin-bottom: 0;\n\n        > thead > tr > th,\n        > tbody > tr > th,\n        > tfoot > tr > th,\n        > thead > tr > td,\n        > tbody > tr > td,\n        > tfoot > tr > td{\n            padding-top: 0;\n            padding-bottom: 0;\n        }\n    }\n\n   > thead > tr > th{\n       font-size: 14px;\n       font-weight: $font-weight-bold;\n       padding-bottom: 0;\n       text-transform: uppercase;\n       border: 0;\n   }\n\n   .radio,\n   .checkbox{\n       margin-top: 0;\n       margin-bottom: 0;\n       padding: 0;\n       width: 15px;\n\n       .icons{\n           position: relative;\n       }\n\n        label{\n            &:after,\n            &:before{\n                top: -17px;\n                left: -3px;\n            }\n        }\n   }\n   > thead > tr > th,\n   > tbody > tr > th,\n   > tfoot > tr > th,\n   > thead > tr > td,\n   > tbody > tr > td,\n   > tfoot > tr > td{\n       padding: 12px 7px;\n       vertical-align: middle;\n   }\n\n   .th-description{\n       max-width: 150px;\n   }\n   .td-price{\n       font-size: 26px;\n       font-weight: $font-weight-light;\n       margin-top: 5px;\n       position: relative;\n       top: 4px;\n       text-align: right;\n   }\n   .td-total{\n        font-weight: $font-weight-bold;\n        font-size: $font-size-h5;\n        padding-top: 20px;\n        text-align: right;\n    }\n\n   .td-actions .btn{\n    margin: 0px;\n    }\n\n    > tbody > tr{\n        position: relative;\n    }\n}\n\n.table-shopping{\n    > thead > tr > th{\n        font-size: $font-size-h6;\n        text-transform: uppercase;\n    }\n    > tbody > tr > td{\n        font-size: $font-paragraph;\n\n        b{\n            display: block;\n            margin-bottom: 5px;\n        }\n    }\n    .td-name{\n        font-weight: $font-weight-normal;\n        font-size: 1.5em;\n        small{\n            color: $dark-gray;\n            font-size: 0.75em;\n            font-weight: $font-weight-light;\n        }\n    }\n    .td-number{\n       font-weight: $font-weight-light;\n       font-size: $font-size-h4;\n   }\n    .td-name{\n        min-width: 200px;\n    }\n    .td-number{\n        text-align: right;\n        min-width: 170px;\n\n        small{\n            margin-right: 3px;\n        }\n    }\n\n    .img-container{\n        width: 120px;\n        max-height: 160px;\n        overflow: hidden;\n        display: block;\n\n        img{\n            width: 100%;\n        }\n    }\n}\n\n.table-responsive{\n  overflow: scroll;\n  padding-bottom: 10px;\n}\n\n#tables .table-responsive{\n    margin-bottom: 30px;\n}\n\n.table-hover>tbody>tr:hover{\n  background-color: #f5f5f5;\n}\n", ".wrapper{\n    position: relative;\n    top: 0;\n    height: 100vh;\n\n    &.wrapper-full-page{\n        min-height: 100vh;\n        height: auto;\n    }\n}\n\n.sidebar,\n.off-canvas-sidebar{\n    position: fixed;\n    top: 0;\n    height: 100%;\n    bottom: 0;\n    width: 260px;\n    left: 0;\n    z-index: 1030;\n    border-right: 1px solid #ddd;\n\n    .sidebar-wrapper{\n        position: relative;\n        height: calc(100vh - 75px);\n        overflow: auto;\n        width: 260px;\n        z-index: 4;\n        padding-bottom: 100px;\n\n        .dropdown .dropdown-backdrop{\n          display: none !important;\n        }\n\n        .navbar-form{\n            border: none;\n        }\n    }\n\n    .navbar-minimize{\n      position: absolute;\n      right: 20px;\n      top: 2px;\n      opacity: 1;\n\n      @extend .animation-transition-general;\n    }\n    .logo-tim{\n      border-radius: 50%;\n      border: 1px solid #333;\n      display: block;\n      height: 61px;\n      width: 61px;\n      float: left;\n      overflow: hidden;\n\n      img{\n          width: 60px;\n          height: 60px;\n      }\n    }\n\n    .nav{\n        margin-top: 20px;\n        display: block;\n\n        .caret{\n            top: 14px;\n            position: absolute;\n            right: 10px;\n        }\n\n        li{\n            > a + div .nav li > a{\n                margin-top: 7px;\n            }\n\n            > a{\n                margin: 10px 15px 0;\n                color: $white-color;\n                display: block;\n                text-decoration: none;\n                position: relative;\n                text-transform: uppercase;\n                cursor: pointer;\n                font-size: 12px;\n                padding: 10px 8px;\n                line-height: 30px;\n                opacity: .7;\n            }\n\n            .nav > li > a{\n              padding: 5px 8px;\n            }\n\n            &.active > a,\n            &.active > a > i {\n              opacity: 1;\n            }\n\n            &:hover:not(.active) > a,\n            &:focus:not(.active) > a {\n                opacity: 1;\n            }\n        }\n\n        i{\n            font-size: 24px;\n            float: left;\n            margin-right: 12px;\n            line-height: 30px;\n            width: 34px;\n            text-align: center;\n            color: $opacity-5;\n            position: relative;\n        }\n\n        p {\n          margin-bottom: 0;\n        }\n\n        .collapse,\n        .collapsing {\n          .nav {\n            margin-top: 0;\n          }\n        }\n    }\n\n    .sidebar-background{\n        position: absolute;\n        z-index: 1;\n        height: 100%;\n        width: 100%;\n        display: block;\n        top: 0;\n        left: 0;\n        background-size: cover;\n        background-position: center center;\n\n        &:after{\n            position: absolute;\n            z-index: 3;\n            width: 100%;\n            height: 100%;\n            content: \"\";\n            display: block;\n            background: #FFFFFF;\n            opacity: 1;\n        }\n    }\n\n    .logo{\n        position: relative;\n        padding: 7px $padding-base-horizontal;\n        z-index: 4;\n\n        a.logo-mini,\n        a.logo-normal{\n            @extend .animation-transition-general;\n        }\n\n        a.logo-mini{\n            opacity: 1;\n            float: left;\n            width: 34px;\n            text-align: center;\n            margin-left: 10px;\n            margin-right: 12px;\n        }\n\n        a.logo-normal{\n            display: block;\n            opacity: 1;\n            padding: 11px 0 8px;\n            @include transform-translate-x(0px);\n        }\n\n        &:after{\n            content: '';\n            position: absolute;\n            bottom: 0;\n            right: 15px;\n            height: 1px;\n            width: calc(100% - 30px);\n            background-color: $opacity-5;\n\n        }\n\n        p{\n            float: left;\n            font-size: 20px;\n            margin: 10px 10px;\n            color: $white-color;\n            line-height: 20px;\n            font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n        }\n\n        .simple-text{\n            text-transform: uppercase;\n            padding: $padding-base-vertical 0;\n            display: block;\n            white-space: nowrap;\n            font-size: $font-size-large;\n            color: $white-color;\n            text-decoration: none;\n            font-weight: $font-weight-normal;\n            line-height: 30px;\n            overflow: hidden;\n        }\n    }\n\n    .logo-tim{\n        border-radius: 50%;\n        border: 1px solid #333;\n        display: block;\n        height: 61px;\n        width: 61px;\n        float: left;\n        overflow: hidden;\n\n        img{\n            width: 60px;\n            height: 60px;\n        }\n    }\n\n    &:before,\n    &:after{\n        display: block;\n        content: \"\";\n        opacity: 1;\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        top: 0;\n        left: 0;\n    }\n\n    &:after{\n        @include icon-gradient($default-color);\n        z-index: 3;\n    }\n\n    &[data-color=\"white\"]{\n      @include sidebar-color($white-color);\n      @include sidebar-text-color($default-color);\n    }\n    &[data-color=\"black\"]{\n      @include sidebar-color($dark-color);\n    }\n\n\n    // Active color changing\n\n    &[data-active-color=\"primary\"]{\n        @include sidebar-active-color($primary-color);\n    }\n    &[data-active-color=\"info\"]{\n        @include sidebar-active-color($info-color);\n    }\n    &[data-active-color=\"success\"]{\n        @include sidebar-active-color($success-color);\n    }\n    &[data-active-color=\"warning\"]{\n        @include sidebar-active-color($warning-color);\n    }\n    &[data-active-color=\"danger\"]{\n        @include sidebar-active-color($danger-color);\n    }\n    &[data-active-color=\"default\"]{\n        @include sidebar-active-color($default-color);\n    }\n}\n\n.visible-on-sidebar-regular{\n    display: inline-block !important;\n}\n.visible-on-sidebar-mini{\n    display: none !important;\n}\n\n.off-canvas-sidebar{\n    .nav {\n        > li > a,\n        > li > a:hover{\n            color: $white-color;\n        }\n\n        > li > a:focus{\n            background: rgba(200, 200, 200, 0.2);\n        }\n    }\n}\n\n\n.main-panel{\n    position: relative;\n    float: right;\n    width: $sidebar-width;\n    background-color: #f4f3ef;;\n\n\n    @include transition (0.50s, cubic-bezier(0.685, 0.0473, 0.346, 1));\n\n    > .content{\n        padding: 0 30px 30px;\n        min-height: calc(100vh - px);\n        margin-top: 93px;\n    }\n\n    > .navbar{\n        margin-bottom: 0;\n    }\n\n\n    .header{\n        margin-bottom: 50px;\n    }\n}\n\n\n.perfect-scrollbar-on{\n  .sidebar,\n  .main-panel{\n      height: 100%;\n      max-height: 100%;\n  }\n}\n\n.panel-header {\n  height: 260px;\n  padding-top: 80px;\n  padding-bottom: 45px;\n  background: #141E30;  /* fallback for old browsers */\n  background: -webkit-gradient(linear, left top, right top, from(#0c2646), color-stop(60%, #204065), to(#2a5788));\n  background: linear-gradient(to right, #0c2646 0%, #204065 60%, #2a5788 100%);\n  position: relative;\n  overflow: hidden;\n\n  .header{\n    .title{\n      color: $white-color;\n    }\n    .category{\n      max-width: 600px;\n      color: $opacity-5;\n      margin: 0 auto;\n      font-size: 13px;\n\n      a{\n        color: $white-color;\n      }\n    }\n  }\n}\n\n.panel-header-sm{\n  height: 135px;\n}\n\n.panel-header-lg{\n  height: 380px\n}\n", ".footer{\n    padding: 24px 0;\n\n    &.footer-default{\n        background-color: #f2f2f2;\n    }\n\n    nav{\n        display: inline-block;\n        float: left;\n        padding-left: 0;\n    }\n\n    ul{\n        margin-bottom: 0;\n        padding: 0;\n        list-style: none;\n\n        li{\n            display: inline-block;\n\n            a{\n                color: inherit;\n                padding: $padding-base-vertical;\n                font-size: $font-size-small;\n                text-transform: uppercase;\n                text-decoration: none;\n\n                &:hover{\n                    text-decoration: none;\n                }\n            }\n        }\n    }\n\n    .copyright{\n        font-size: $font-size-small;\n        line-height: 1.8;\n    }\n\n    &:after{\n        display: table;\n        clear: both;\n        content: \" \";\n    }\n}\n", ".fixed-plugin{\n    position: fixed;\n    right: 0;\n    width: 64px;\n    background: rgba(0,0,0,.3);\n    z-index: 1031;\n    border-radius: 8px 0 0 8px;\n    text-align: center;\n    top: 120px;\n\n    li > a,\n    .badge{\n        transition: all .34s;\n        -webkit-transition: all .34s;\n        -moz-transition: all .34s;\n    }\n\n    .fa-cog{\n        color: #FFFFFF;\n        padding: 10px;\n        border-radius: 0 0 6px 6px;\n        width: auto;\n    }\n\n    .dropdown-menu{\n        right: 80px;\n        left: auto !important;\n        top: -52px !important;\n        width: 290px;\n        border-radius: 10px;\n        padding: 0 10px;\n    }\n\n    .dropdown .dropdown-menu .nc-icon{\n      top: 2px;\n      right: 10px;\n      font-size: 14px;\n    }\n\n    .dropdown-menu:after,\n    .dropdown-menu:before{\n        right: 10px;\n        margin-left: auto;\n        left: auto;\n    }\n\n    .fa-circle-thin{\n        color: #FFFFFF;\n    }\n\n    .active .fa-circle-thin{\n        color: #00bbff;\n    }\n\n    .dropdown-menu > .active > a,\n    .dropdown-menu > .active > a:hover,\n    .dropdown-menu > .active > a:focus{\n        color: #777777;\n        text-align: center;\n    }\n\n    img{\n        border-radius: 0;\n        width: 100%;\n        height: 100px;\n        margin: 0 auto;\n    }\n\n    .dropdown-menu li > a:hover,\n    .dropdown-menu li > a:focus{\n        box-shadow: none;\n    }\n\n    .badge{\n        border: 3px solid #FFFFFF;\n        border-radius: 50%;\n        cursor: pointer;\n        display: inline-block;\n        height: 23px;\n        margin-right: 5px;\n        position: relative;\n        width: 23px;\n\n      &.badge-light {\n        border: 1px solid $light-gray;\n\n        &.active,\n        &:hover {\n          border: 3px solid #0bf;\n        }\n      }\n    }\n\n    .badge.active,\n    .badge:hover{\n        border-color: #00bbff;\n    }\n\n    .badge-blue{\n        background-color: $brand-info;\n    }\n    .badge-green{\n        background-color: $brand-success;\n    }\n    .badge-orange{\n        background-color: $brand-primary;\n    }\n    .badge-yellow{\n        background-color: $brand-warning;\n    }\n    .badge-red{\n        background-color: $brand-danger;\n    }\n\n    h5{\n        font-size: 14px;\n        margin: 10px;\n    }\n\n    .dropdown-menu li{\n        display: block;\n        padding: 15px 2px;\n        width: 25%;\n        float: left;\n    }\n\n    li.adjustments-line,\n    li.header-title,\n    li.button-container{\n        width: 100%;\n        height: 35px;\n        min-height: inherit;\n    }\n\n    li.button-container{\n        height: auto;\n\n        div{\n            margin-bottom: 5px;\n        }\n    }\n\n    #sharrreTitle{\n        text-align: center;\n        padding: 10px 0;\n        height: 50px;\n    }\n\n    li.header-title{\n        height: 30px;\n        line-height: 25px;\n        font-size: 12px;\n        font-weight: 600;\n        text-align: center;\n        text-transform: uppercase;\n    }\n\n    .adjustments-line{\n        p{\n            float: left;\n            display: inline-block;\n            margin-bottom: 0;\n            font-size: 1em;\n            color: #3C4858;\n        }\n\n        a{\n            color: transparent;\n\n            .badge-colors{\n                position: relative;\n                top: -2px;\n            }\n\n            a:hover,\n            a:focus{\n                color: transparent;\n            }\n        }\n\n        .togglebutton{\n            text-align: center;\n\n            .label-switch{\n              position: relative;\n              left: -10px;\n              font-size: $font-size-mini;\n              color: $default-color;\n\n              &.label-right{\n                left: 10px;\n              }\n            }\n\n            .toggle{\n                margin-right: 0;\n            }\n        }\n\n        .dropdown-menu > li.adjustments-line > a{\n              padding-right: 0;\n              padding-left: 0;\n              border-bottom: 1px solid #ddd;\n              border-radius: 0;\n              margin: 0;\n        }\n    }\n\n\n\n    .dropdown-menu{\n        > li{\n            & > a.img-holder{\n                  font-size: 16px;\n                  text-align: center;\n                  border-radius: 10px;\n                  background-color: #FFF;\n                  border: 3px solid #FFF;\n                  padding-left: 0;\n                  padding-right: 0;\n                  opacity: 1;\n                  cursor: pointer;\n                  display: block;\n                  max-height: 100px;\n                  overflow: hidden;\n                  padding: 0;\n\n                  img{\n                     margin-top: auto;\n                  }\n            }\n\n            a.switch-trigger:hover,\n            & > a.switch-trigger:focus{\n                background-color: transparent;\n            }\n\n            &:hover,\n            &:focus{\n                > a.img-holder{\n                    border-color: rgba(0, 187, 255, 0.53);;\n                }\n            }\n        }\n\n        > .active > a.img-holder,\n        > .active > a.img-holder{\n            border-color: #00bbff;\n            background-color: #FFFFFF;\n        }\n\n    }\n\n    .btn-social{\n        width: 50%;\n        display: block;\n        width: 48%;\n        float: left;\n        font-weight: 600;\n    }\n\n    .btn-social{\n        i{\n            margin-right: 5px;\n        }\n\n        &:first-child{\n            margin-right: 2%;\n        }\n    }\n\n    .dropdown{\n        .dropdown-menu{\n          transform-origin: 0 0;\n\n          &:before{\n             border-bottom: 16px solid rgba(0, 0, 0, 0);\n             border-left: 16px solid rgba(0,0,0,0.2);\n             border-top: 16px solid rgba(0,0,0,0);\n             right: -27px;\n             bottom: 425px;\n          }\n\n          &:after{\n             border-bottom: 16px solid rgba(0, 0, 0, 0);\n             border-left: 16px solid #FFFFFF;\n             border-top: 16px solid rgba(0,0,0,0);\n             right: -26px;\n             bottom: 425px;\n          }\n\n          &:before,\n          &:after{\n             content: \"\";\n             display: inline-block;\n             position: absolute;\n             width: 16px;\n             transform: translateY(-50px);\n             -webkit-transform: translateY(-50px);\n             -moz-transform: translateY(-50px);\n          }\n        }\n\n      &.show-dropdown .show{\n        .dropdown-menu .show{\n          transform: translate3d(0, -60px, 0)!important;\n          bottom: auto!important;\n          top: 0!important;\n        }\n      }\n    }\n\n    .bootstrap-switch{\n        margin:0;\n    }\n}\n\n.fixed-plugin {\n  .show-dropdown {\n    .dropdown-menu[x-placement=bottom-start] {\n      @include transform-translate-y-fixed-plugin (-100px);\n\n      &:before,\n      &:after {\n        top: 100px;\n      }\n    }\n    .dropdown-menu[x-placement=top-start] {\n      @include transform-translate-y-fixed-plugin (100px);\n    }\n\n    &.show {\n      .dropdown-menu.show[x-placement=bottom-start] {\n        @include transform-translate-y-fixed-plugin (-60px);\n      }\n\n      .dropdown-menu.show[x-placement=top-start] {\n        @include transform-translate-y-fixed-plugin (470px);\n      }\n    }\n  }\n}\n", ".card{\n  border-radius: $border-radius-extreme;\n  box-shadow: 0 6px 10px -4px rgba(0, 0, 0, 0.15);\n  background-color: #FFFFFF;\n  color: $card-black-color;\n  margin-bottom: 20px;\n  position: relative;\n  border: 0 none;\n\n  -webkit-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -moz-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -o-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  -ms-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n  transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;\n\n    .card-body{\n        padding: 15px 15px 10px 15px;\n\n        &.table-full-width{\n            padding-left: 0;\n            padding-right: 0;\n        }\n    }\n\n    .card-header{\n      &:not([data-background-color]){\n        background-color: transparent;\n      }\n      padding: 15px 15px 0;\n      border: 0;\n\n      .card-title{\n          margin-top: 10px;\n      }\n    }\n\n    .map{\n        border-radius: $border-radius-small;\n\n        &.map-big{\n          height: 500px;\n        }\n    }\n\n    &[data-background-color=\"orange\"]{\n        background-color: $primary-color;\n\n        .card-header{\n            background-color: $primary-color;\n        }\n\n        .card-footer{\n            .stats{\n                color: $white-color;\n            }\n        }\n    }\n\n    &[data-background-color=\"red\"]{\n        background-color: $danger-color;\n    }\n\n    &[data-background-color=\"yellow\"]{\n        background-color: $warning-color;\n    }\n\n    &[data-background-color=\"blue\"]{\n        background-color: $info-color;\n    }\n\n    &[data-background-color=\"green\"]{\n        background-color: $success-color;\n    }\n\n    .image{\n        overflow: hidden;\n        height: 200px;\n        position: relative;\n    }\n\n    .avatar{\n        width: 30px;\n        height: 30px;\n        overflow: hidden;\n        border-radius: 50%;\n        margin-bottom: 15px;\n    }\n\n    .numbers {\n      font-size: 2em;\n    }\n\n    .big-title {\n      font-size: 12px;\n      text-align: center;\n      font-weight: 500;\n      padding-bottom: 15px;\n    }\n\n    label{\n        font-size: $font-size-small;\n        margin-bottom: 5px;\n        color: $dark-gray;\n    }\n\n    .card-footer{\n        background-color: transparent;\n        border: 0;\n\n\n        .stats{\n            i{\n                margin-right: 5px;\n                position: relative;\n                top: 0px;\n                color: $default-color;\n            }\n        }\n\n        .btn{\n            margin: 0;\n        }\n    }\n\n    &.card-plain{\n        background-color: transparent;\n        box-shadow: none;\n        border-radius: 0;\n\n\n        .card-body{\n            padding-left: 5px;\n            padding-right: 5px;\n        }\n\n        img{\n            border-radius: $border-radius-extreme;\n        }\n    }\n}\n", "\n.card-plain{\n    background: transparent;\n    box-shadow: none;\n\n    .card-header,\n    .card-footer{\n        margin-left: 0;\n        margin-right: 0;\n        background-color: transparent;\n    }\n\n    &:not(.card-subcategories).card-body{\n        padding-left: 0;\n        padding-right: 0;\n    }\n}\n", ".card-chart {\n  .card-header{\n    .card-title{\n      margin-top: 10px;\n      margin-bottom: 0;\n    }\n    .card-category{\n      margin-bottom: 5px;\n    }\n  }\n\n  .table{\n    margin-bottom: 0;\n\n    td{\n      border-top: none;\n      border-bottom: 1px solid #e9ecef;\n    }\n  }\n\n  .card-progress {\n    margin-top: 30px;\n  }\n\n  .chart-area {\n    height: 190px;\n    width: calc(100% + 30px);\n    margin-left: -15px;\n    margin-right: -15px;\n  }\n  .card-footer {\n    margin-top: 15px;\n\n    .stats{\n      color: $dark-gray;\n    }\n  }\n\n  .dropdown{\n    position: absolute;\n    right: 20px;\n    top: 20px;\n\n    .btn{\n      margin: 0;\n    }\n  }\n}\n", ".card-user{\n    .image{\n        height: 130px;\n\n      img {\n        border-radius: 12px;\n      }\n    }\n\n    .author{\n        text-align: center;\n        text-transform: none;\n        margin-top: -77px;\n\n        a +  p.description{\n            margin-top: -7px;\n        }\n    }\n\n    .avatar{\n        width: 124px;\n        height: 124px;\n        border: 1px solid $white-color;\n        position: relative;\n    }\n\n    .card-body{\n        min-height: 240px;\n    }\n\n    hr{\n        margin: 5px 15px 15px;\n    }\n\n    .card-body + .card-footer {\n      padding-top: 0;\n    }\n\n    .card-footer {\n      h5 {\n        font-size: 1.25em;\n        margin-bottom: 0;\n      }\n    }\n\n    .button-container{\n        margin-bottom: 6px;\n        text-align: center;\n    }\n}\n", ".map{\n    height: 500px;\n}\n", "%card-stats{\n  hr{\n    margin: 5px 15px;\n  }\n}\n\n\n.card-stats{\n    .card-body{\n        padding: 15px 15px 0px;\n\n        .numbers{\n          text-align: right;\n          font-size: 2em;\n\n            p{\n                margin-bottom: 0;\n            }\n            .card-category {\n              color: $dark-gray;\n              font-size: 16px;\n              line-height: 1.4em;\n            }\n        }\n    }\n    .card-footer{\n        padding: 0px 15px 15px;\n\n        .stats{\n          color: $dark-gray;\n        }\n\n        hr{\n          margin-top: 10px;\n          margin-bottom: 15px;\n        }\n    }\n    .icon-big {\n        font-size: 3em;\n        min-height: 64px;\n\n        i{\n            line-height: 59px;\n        }\n    }\n\n\n}\n", "@media screen and (max-width: 991px){\n\n  .navbar {\n    padding: 0;\n\n    &.navbar-absolute {\n      padding-top: 0;\n    }\n\n    .navbar-brand {\n      font-size: 16px;\n      margin-right: 0;\n    }\n  }\n\n    .profile-photo .profile-photo-small{\n        margin-left: -2px;\n    }\n\n    .button-dropdown{\n        display: none;\n    }\n\n    #minimizeSidebar{\n        display: none;\n    }\n\n    .navbar{\n        .container-fluid{\n            padding-right: 15px;\n            padding-left: 15px;\n        }\n\n        .navbar-collapse{\n          .input-group{\n            margin: 0;\n            margin-top: 5px;\n          }\n        }\n\n        .navbar-nav{\n            .nav-item:first-child{\n              margin-top: 10px;\n            }\n            .nav-item:not(:last-child){\n                margin-bottom: 10px;\n            }\n        }\n\n        .dropdown.show .dropdown-menu{\n            display: block;\n        }\n\n        .dropdown .dropdown-menu{\n            display: none;\n        }\n\n        .dropdown.show .dropdown-menu,\n        .dropdown .dropdown-menu{\n            border: 0;\n            transition: none;\n            -webkit-box-shadow: none;\n            width: auto;\n            margin: 0px 1rem;\n            margin-top: 0px;\n            box-shadow: none;\n            position: static;\n            padding-left: 10px;\n\n            &:before{\n                display: none;\n            }\n        }\n\n        .dropdown-menu .dropdown-item:focus,\n        .dropdown-menu .dropdown-item:hover{\n            color: $white-color;\n        }\n\n        &.bg-white .dropdown-menu .dropdown-item:focus,\n        &.bg-white .dropdown-menu .dropdown-item:hover{\n            color: $default-color;\n        }\n\n        .navbar-toggler-bar{\n            display: block;\n            position: relative;\n            width: 22px;\n            height: 1px;\n            border-radius: 1px;\n            background: $default-color;\n\n            & + .navbar-toggler-bar{\n                margin-top: 7px;\n            }\n\n            & + .navbar-toggler-bar.navbar-kebab{\n                margin-top: 3px;\n            }\n\n            &.bar2{\n                width: 17px;\n                transition: width .2s linear;\n            }\n        }\n\n        &.bg-white:not(.navbar-transparent) .navbar-toggler-bar{\n            background-color: $default-color;\n        }\n\n        & .toggled .navbar-toggler-bar{\n            width: 24px;\n\n            & + .navbar-toggler-bar{\n                margin-top: 5px;\n            }\n        }\n\n    }\n\n    .wrapper{\n        @include transition (0.50s, cubic-bezier(0.685, 0.0473, 0.346, 1));\n    }\n\n    .nav-open{\n        .main-panel{\n            right: 0;\n            @include transform-translate-x(260px);\n        }\n\n        .sidebar{\n            @include transform-translate-x(0px);\n        }\n\n        body{\n            position: relative;\n            overflow-x: hidden;\n        }\n\n        .menu-on-right{\n            .main-panel{\n                @include transform-translate-x(-260px);\n            }\n\n            .navbar-collapse,\n            .sidebar{\n                @include transform-translate-x(0px);\n            }\n\n            .navbar-translate{\n                @include transform-translate-x(-300px);\n            }\n\n            #bodyClick{\n                right: 260px;\n                left: auto;\n            }\n        }\n    }\n\n    .menu-on-right{\n        .sidebar{\n            left: auto;\n            right:0;\n            @include transform-translate-x(260px);\n        }\n    }\n\n    .bar1,\n    .bar2,\n    .bar3 {\n      outline: 1px solid transparent;\n    }\n    .bar1 {\n      top: 0px;\n      @include bar-animation($topbar-back);\n    }\n    .bar2 {\n      opacity: 1;\n    }\n    .bar3 {\n      bottom: 0px;\n      @include bar-animation($bottombar-back);\n    }\n    .toggled .bar1 {\n      top: 6px;\n      @include bar-animation($topbar-x);\n    }\n    .toggled .bar2 {\n      opacity: 0;\n    }\n    .toggled .bar3 {\n      bottom: 6px;\n      @include bar-animation($bottombar-x);\n    }\n\n    @include topbar-x-rotation();\n    @include topbar-back-rotation();\n    @include bottombar-x-rotation();\n    @include bottombar-back-rotation();\n\n    @-webkit-keyframes fadeIn {\n      0% {opacity: 0;}\n      100% {opacity: 1;}\n    }\n    @-moz-keyframes fadeIn {\n      0% {opacity: 0;}\n      100% {opacity: 1;}\n    }\n    @keyframes fadeIn {\n      0% {opacity: 0;}\n      100% {opacity: 1;}\n    }\n\n    #bodyClick{\n        height: 100%;\n        width: 100%;\n        position: fixed;\n        opacity: 1;\n        top: 0;\n        right: 0;\n        left: 260px;\n        content: \"\";\n        z-index: 9999;\n        overflow-x: hidden;\n        background-color: transparent;\n        @include transition (0.50s, cubic-bezier(0.685, 0.0473, 0.346, 1));\n    }\n\n    .footer{\n        .copyright{\n            text-align: right;\n        }\n    }\n\n    .section-nucleo-icons .icons-container{\n        margin-top: 65px;\n    }\n\n    .navbar-nav{\n        .nav-link{\n            i.fa,\n            i.nc-icon{\n                opacity: .5;\n            }\n        }\n    }\n\n    .sidebar,\n    .bootstrap-navbar {\n        position: fixed;\n        display: block;\n        top: 0;\n        height: 100%;\n        width: 260px;\n        right: auto;\n        left: 0;\n        z-index: 1032;\n        visibility: visible;\n        overflow-y: visible;\n        padding: 0;\n        @include transition (0.50s, cubic-bezier(0.685, 0.0473, 0.346, 1));\n\n        @include transform-translate-x(-260px);\n    }\n\n\n\n    .main-panel{\n      width: 100%;\n    }\n\n    .timeline{\n      &:before{\n          left: 5% !important;\n      }\n\n      > li > .timeline-badge{\n          left: 5% !important;\n      }\n\n      > li > .timeline-panel{\n          float: right !important;\n          width: 82% !important;\n\n          &:before{\n              border-left-width: 0 !important;\n              border-right-width: 15px !important;\n              left: -15px !important;\n              right: auto !important;\n          }\n\n          &:after{\n              border-left-width: 0 !important;\n              border-right-width: 14px !important;\n              left: -14px !important;\n              right: auto !important;\n          }\n      }\n  }\n\n}\n@media (max-width: 991px) and (min-width: 768px){\n  .nav-tabs-navigation.verical-navs {\n    padding: 0px 2px;\n  }\n}\n\n@media screen and (min-width: 768px){\n  .footer {\n    .footer-nav {\n      padding-left: 21px;\n    }\n\n    .credits {\n      padding-right: 15px;\n    }\n  }\n}\n\n@media screen and (min-width: 992px){\n    .navbar-collapse{\n        background: none !important;\n    }\n\n    .navbar .navbar-toggle{\n        display: none;\n    }\n\n    .navbar-nav{\n        .nav-link{\n            &.profile-photo{\n                padding: 0;\n                margin: 7px $padding-base-horizontal;\n            }\n        }\n    }\n\n    .section-nucleo-icons .icons-container{\n        margin: 0 0 0 auto;\n    }\n\n    .dropdown-menu .dropdown-item{\n        color: inherit;\n    }\n\n    .footer{\n        .copyright{\n            float: right;\n            padding-right: 15px;\n        }\n    }\n\n    .sidebar{\n      .sidebar-wrapper{\n        li.active{\n          > a:not([data-toggle=\"collapse\"]),\n          > [data-toggle=\"collapse\"] + div .nav li {\n            &:before{\n              border-right: 17px solid $medium-gray;\n              border-top: 17px solid transparent;\n              border-bottom: 17px solid transparent;\n              content: \"\";\n              display: inline-block;\n              position: absolute;\n              right: -16px;\n              opacity: 1;\n              top: 7px;\n              transition: opacity 150ms ease-in;\n            }\n\n            &:after{\n              border-right: 17px solid $default-body-bg;\n              border-top: 17px solid transparent;\n              border-bottom: 17px solid transparent;\n              content: \"\";\n              display: inline-block;\n              position: absolute;\n              right: -17px;\n              opacity: 1;\n              top: 7px;\n              transition: opacity 150ms ease-in;\n            }\n          }\n          >[data-toggle=\"collapse\"] + div .nav li {\n            a{\n              &:before,\n              &:after {\n                top: 0;\n              }\n            }\n          }\n        }\n      }\n    }\n\n}\n\n@media screen and (max-width: 768px){\n  .card-stats [class*=\"col-\"] .statistics::after {\n    display: none;\n  }\n\n  .main-panel .content {\n    padding-left: 15px;\n    padding-right: 15px;\n  }\n\n    .footer{\n        nav{\n            display: block;\n            margin-bottom: 5px;\n            float: none;\n        }\n    }\n\n    .landing-page .section-story-overview .image-container:nth-child(2){\n        margin-left: 0;\n        margin-bottom: 30px;\n    }\n\n    .card {\n      .form-horizontal {\n        .col-md-3.col-form-label {\n          text-align: left;\n        }\n      }\n    }\n}\n\n@media screen and (max-width: 767px){\n  .nav-tabs-navigation.verical-navs{\n    padding: 0 28px;\n  }\n\n  .typography-line {\n    padding-left: 23% !important;\n\n    span {\n      width: 60px !important;\n    }\n  }\n\n  .login-page,\n  .lock-page,\n  .register-page {\n    .navbar{\n      padding: .5rem 1rem;\n    }\n  }\n\n  .footer {\n    .footer-nav,\n    .credits {\n      margin: 0 auto !important;\n    }\n\n    .footer-nav {\n      margin-bottom: 10px !important;\n    }\n  }\n\n  .register-page {\n    .content {\n      padding-top: 5vh;\n    }\n    .footer {\n      position: relative;\n    }\n    .info-area.info-horizontal {\n      margin-top: 0;\n    }\n  }\n}\n\n\n@media screen and (max-width: 413px){\n  .fixed-plugin {\n    .dropdown.show-dropdown.show{\n      .dropdown-menu.show {\n        width: 225px !important;\n\n        &[x-placement=top-start] {\n          transform: translate3d(0,400px,0)!important;\n        }\n\n        &:before,\n        &:after {\n          bottom: 360px !important;\n        }\n      }\n    }\n  }\n\n  .login-page {\n    .container {\n      padding-top: 100px !important;\n    }\n  }\n\n}\n\n\n@media screen and (max-width: 576px){\n    .navbar[class*='navbar-toggleable-'] .container{\n        margin-left: 0;\n        margin-right: 0;\n    }\n\n    .card-contributions .card-stats{\n      flex-direction: column;\n\n      .bootstrap-switch{\n        margin-bottom: 15px;\n      }\n    }\n\n    .footer{\n        .copyright{\n            text-align: center;\n        }\n    }\n\n    .section-nucleo-icons{\n        .icons-container{\n            i{\n                font-size: 30px;\n\n                &:nth-child(6){\n                    font-size: 48px;\n                }\n            }\n        }\n    }\n\n    .page-header{\n        .container h6.category-absolute{\n            width: 90%;\n        }\n    }\n\n    .card-timeline .timeline {\n      .timeline-panel {\n        width: 38%;\n        padding: 15px;\n      }\n    }\n}\n"]}