{"version": 3, "sources": ["_site_dashboard_free/assets/js/dashboard-free.js"], "names": ["hexToRGB", "hex", "alpha", "r", "parseInt", "slice", "g", "b", "isWindows", "navigator", "platform", "indexOf", "PerfectScrollbar", "$", "addClass", "backgroundOrange", "navbar_initialized", "fixedTop", "transparentDemo", "transparent", "toggle_initialized", "sidebar_mini_active", "seq", "delays", "durations", "seq2", "delays2", "durations2", "document", "ready", "length", "on", "this", "closest", "removeClass", "paperDashboard", "initMinimizeSidebar", "$navbar", "scroll_distance", "attr", "checkScrollForTransparentNavbar", "window", "parent", "each", "$this", "data_on_label", "data", "data_off_label", "bootstrapSwitch", "onText", "offText", "$toggle", "misc", "navbar_menu_visible", "setTimeout", "remove", "div", "appendTo", "click", "resize", "isExpanded", "find", "hasClass", "width", "showSidebarMessage", "simulateWindowResize", "setInterval", "dispatchEvent", "Event", "clearInterval", "message", "notify", "icon", "type", "timer", "placement", "from", "align", "e", "console", "log"], "mappings": "AA2LA,SAASA,SAASC,EAAKC,GACnB,IAAIC,EAAIC,SAASH,EAAII,MAAM,EAAG,GAAI,IAC9BC,EAAIF,SAASH,EAAII,MAAM,EAAG,GAAI,IAC9BE,EAAIH,SAASH,EAAII,MAAM,EAAG,GAAI,IAElC,OAAIH,EACO,QAAUC,EAAI,KAAOG,EAAI,KAAOC,EAAI,KAAOL,EAAQ,IAEnD,OAASC,EAAI,KAAOG,EAAI,KAAOC,EAAI,KAlLlD,WAGI,GAFAC,WAAiD,EAArCC,UAAUC,SAASC,QAAQ,OAEnCH,UAAW,CAEH,IAAII,iBAAiB,YACpB,IAAIA,iBAAiB,oBACrB,IAAIA,iBAAiB,eAC/BC,EAAE,QAAQC,SAAS,6BAGnBD,EAAE,QAAQC,SAAS,yBAX1B,GAoBAC,iBADAC,mBAFAC,WADAC,gBADAC,aAAc,GAOdC,mBADAC,qBAAsB,EAGtBC,IAAM,EAAGC,OAAS,GAAIC,UAAY,IAClCC,KAAO,EAAGC,QAAU,GAAIC,WAAa,IAErCd,EAAEe,UAAUC,MAAM,WAEoB,GAAhChB,EAAE,oBAAoBiB,QAAuC,GAAxBjB,EAAE,YAAYiB,QAErDjB,EAAE,aAAakB,GAAG,mBAAoB,WAClClB,EAAEmB,MAAMC,QAAQ,WAAWC,YAAY,sBAAsBpB,SAAS,cACvEiB,GAAG,mBAAoB,WACtBlB,EAAEmB,MAAMC,QAAQ,WAAWnB,SAAS,sBAAsBoB,YAAY,cAI5EC,eAAeC,sBAEfC,QAAUxB,EAAE,4BACZyB,gBAAkBD,QAAQE,KAAK,oBAAsB,IAGV,GAAxC1B,EAAE,4BAA4BiB,SAC7BK,eAAeK,kCACf3B,EAAE4B,QAAQV,GAAG,SAAUI,eAAeK,kCAG1C3B,EAAE,iBAAiBkB,GAAG,QAAS,WAC3BlB,EAAEmB,MAAMU,OAAO,gBAAgB5B,SAAS,uBACzCiB,GAAG,OAAQ,WACVlB,EAAEmB,MAAMU,OAAO,gBAAgBR,YAAY,uBAI/CrB,EAAE,qBAAqB8B,KAAK,WACxBC,MAAQ/B,EAAEmB,MACVa,cAAgBD,MAAME,KAAK,aAAe,GAC1CC,eAAiBH,MAAME,KAAK,cAAgB,GAE5CF,MAAMI,gBAAgB,CAClBC,OAAQJ,cACRK,QAASH,qBAKnBlC,EAAEe,UAAUG,GAAG,QAAS,iBAAkB,WACtCoB,QAAUtC,EAAEmB,MAEkC,GAA3CG,eAAeiB,KAAKC,qBACnBxC,EAAE,QAAQqB,YAAY,YACtBC,eAAeiB,KAAKC,oBAAsB,EAC1CC,WAAW,WACPH,QAAQjB,YAAY,WACpBrB,EAAE,cAAc0C,UACjB,OAGHD,WAAW,WACPH,QAAQrC,SAAS,YAClB,KAEH0C,IAAM,6BACN3C,EAAE2C,KAAKC,SAAS,QAAQC,MAAM,WAC1B7C,EAAE,QAAQqB,YAAY,YACtBC,eAAeiB,KAAKC,oBAAsB,EACtCC,WAAW,WACPH,QAAQjB,YAAY,WACpBrB,EAAE,cAAc0C,UAClB,OAGV1C,EAAE,QAAQC,SAAS,YACnBqB,eAAeiB,KAAKC,oBAAsB,KAIlDxC,EAAE4B,QAAQkB,OAAO,WAEbrC,IAAMG,KAAO,EAEsB,GAAhCZ,EAAE,oBAAoBiB,QAAuC,GAAxBjB,EAAE,YAAYiB,SACpDO,QAAUxB,EAAE,WACZ+C,WAAa/C,EAAE,WAAWgD,KAAK,4BAA4BtB,KAAK,iBAC5DF,QAAQyB,SAAS,aAAmC,IAApBjD,EAAE4B,QAAQsB,QAC5C1B,QAAQH,YAAY,YAAYpB,SAAS,sBAChCuB,QAAQyB,SAAS,uBAAyBjD,EAAE4B,QAAQsB,QAAU,KAAqB,SAAdH,YAC9EvB,QAAQvB,SAAS,YAAYoB,YAAY,yBAKjDC,eAAiB,CACfiB,KAAK,CACDC,oBAAqB,GAGzBjB,oBAAoB,WACgB,GAA7BvB,EAAE,iBAAiBiB,SACpBT,qBAAsB,GAGxBR,EAAE,oBAAoB6C,MAAM,WACb7C,EAAEmB,MAEa,GAAvBX,qBACDR,EAAE,QAAQC,SAAS,gBACnBO,qBAAsB,EACtBc,eAAe6B,mBAAmB,+BAElCnD,EAAE,QAAQqB,YAAY,gBACtBb,qBAAsB,EACtBc,eAAe6B,mBAAmB,gCAIpC,IAAIC,EAAuBC,YAAY,WACnCzB,OAAO0B,cAAc,IAAIC,MAAM,YACjC,KAGFd,WAAW,WACPe,cAAcJ,IAChB,QAIVD,mBAAoB,SAASM,GAC3B,IACEzD,EAAE0D,OAAO,CACLC,KAAM,4BACNF,QAASA,GACT,CACEG,KAAM,OACNC,MAAO,IACPC,UAAW,CACPC,KAAM,MACNC,MAAO,WAGjB,MAAOC,GACPC,QAAQC,IAAI"}