<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Upload CSV - Megacare HQ PRD</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="/megacare/hqprd/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  
  <style>
    .upload-area {
      border: 2px dashed #ddd;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .upload-area:hover {
      border-color: #667eea;
      background-color: #f8f9ff;
    }
    .upload-area.dragover {
      border-color: #667eea;
      background-color: #f0f4ff;
    }
    .upload-icon {
      font-size: 48px;
      color: #ddd;
      margin-bottom: 20px;
    }
    .upload-area:hover .upload-icon {
      color: #667eea;
    }
  </style>
</head>

<body class="">
  <div class="wrapper ">
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-mini">
          <div class="logo-image-small">
            <img src="/megacare/hqprd/img/logo-small.png">
          </div>
        </a>
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li>
            <a href="/megacare/hqprd/dashboard.html">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li class="active">
            <a href="/megacare/hqprd/upload.html">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload CSV</p>
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Upload CSV</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item btn-rotate dropdown">
                <a class="nav-link dropdown-toggle" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="nc-icon nc-single-02"></i>
                  <p>
                    <span class="d-lg-none d-md-block">Account</span>
                  </p>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                  <a class="dropdown-item" href="/megacare/hqprd/logout">Logout</a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <!-- End Navbar -->
      
      <div class="content">
        <div class="row">
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload CSV File</h4>
              </div>
              <div class="card-body">
                <div id="alertContainer"></div>
                
                <form id="uploadForm" enctype="multipart/form-data">
                  <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                      <i class="nc-icon nc-cloud-upload-94"></i>
                    </div>
                    <h5>Drag & Drop your CSV file here</h5>
                    <p class="text-muted">or click to browse files</p>
                    <input type="file" id="csvFile" name="csvFile" accept=".csv" style="display: none;">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFile').click();">
                      <i class="fa fa-folder-open"></i> Browse Files
                    </button>
                  </div>
                  
                  <div id="fileInfo" style="display: none;" class="mt-3">
                    <div class="alert alert-info">
                      <strong>Selected file:</strong> <span id="fileName"></span>
                      <br>
                      <strong>Size:</strong> <span id="fileSize"></span>
                    </div>
                    <button type="submit" class="btn btn-success" id="uploadBtn">
                      <i class="fa fa-upload"></i> Upload File
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearFile()">
                      <i class="fa fa-times"></i> Clear
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload Guidelines</h4>
              </div>
              <div class="card-body">
                <ul class="list-unstyled">
                  <li><i class="fa fa-check text-success"></i> File format: CSV only</li>
                  <li><i class="fa fa-check text-success"></i> Maximum size: 10MB</li>
                  <li><i class="fa fa-check text-success"></i> UTF-8 encoding recommended</li>
                  <li><i class="fa fa-check text-success"></i> First row should contain headers</li>
                </ul>
                
                <hr>
                
                <h6>Sample CSV Format:</h6>
                <pre class="text-muted" style="font-size: 12px;">id,name,email,department
1,John Doe,<EMAIL>,IT
2,Jane Smith,<EMAIL>,HR</pre>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload Status</h4>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table">
                    <thead class="text-primary">
                      <tr>
                        <th>Filename</th>
                        <th>Upload Date</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th class="text-right">Total Rows</th>
                      </tr>
                    </thead>
                    <tbody id="uploadStatusTable">
                      <tr>
                        <td colspan="5" class="text-center">
                          <i class="fa fa-spinner fa-spin"></i> Loading...
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <nav class="footer-nav">
              <ul>
                <li><a href="/megacare/hqprd/dashboard.html">Dashboard</a></li>
                <li><a href="/megacare/hqprd/upload.html">Upload</a></li>
              </ul>
            </nav>
            <div class="credits ml-auto">
              <span class="copyright">
                © <script>document.write(new Date().getFullYear())</script>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="/megacare/hqprd/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/js/core/popper.min.js"></script>
  <script src="/megacare/hqprd/js/core/bootstrap.min.js"></script>
  <script src="/megacare/hqprd/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/megacare/hqprd/js/paper-dashboard.min.js?v=2.0.1" type="text/javascript"></script>

  <script>
    const uploadArea = document.getElementById('uploadArea');
    const csvFile = document.getElementById('csvFile');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadForm = document.getElementById('uploadForm');
    const alertContainer = document.getElementById('alertContainer');
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    });
    
    uploadArea.addEventListener('click', () => {
      csvFile.click();
    });
    
    csvFile.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
      }
    });
    
    function handleFileSelect(file) {
      if (!file.name.toLowerCase().endsWith('.csv')) {
        showAlert('Please select a CSV file.', 'danger');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB
        showAlert('File size must be less than 10MB.', 'danger');
        return;
      }
      
      fileName.textContent = file.name;
      fileSize.textContent = formatFileSize(file.size);
      fileInfo.style.display = 'block';
    }
    
    function clearFile() {
      csvFile.value = '';
      fileInfo.style.display = 'none';
      alertContainer.innerHTML = '';
    }
    
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    function showAlert(message, type) {
      alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
          ${message}
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      `;
    }
    
    // Form submission
    uploadForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      if (!csvFile.files[0]) {
        showAlert('Please select a file to upload.', 'warning');
        return;
      }
      
      const uploadBtn = document.getElementById('uploadBtn');
      uploadBtn.disabled = true;
      uploadBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Uploading...';
      
      const formData = new FormData();
      formData.append('csvFile', csvFile.files[0]);
      
      try {
        const response = await fetch('/megacare/hqprd/upload', {
          method: 'POST',
          body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
          showAlert(data.message, 'success');
          clearFile();
          loadUploadStatus(); // Refresh the status table
        } else {
          showAlert(data.message, 'danger');
        }
      } catch (error) {
        showAlert('An error occurred during upload. Please try again.', 'danger');
      } finally {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fa fa-upload"></i> Upload File';
      }
    });
    
    // Load upload status
    async function loadUploadStatus() {
      try {
        const response = await fetch('/megacare/hqprd/api/upload-status');
        const data = await response.json();
        
        const tableBody = document.getElementById('uploadStatusTable');
        if (data.success && data.uploads.length > 0) {
          tableBody.innerHTML = data.uploads.map(upload => {
            const statusBadge = getStatusBadge(upload.status);
            const progress = upload.total_rows > 0 ? Math.round((upload.processed_rows / upload.total_rows) * 100) : 0;
            const uploadDate = new Date(upload.upload_date).toLocaleDateString();
            
            return `
              <tr>
                <td>${upload.filename}</td>
                <td>${uploadDate}</td>
                <td>${statusBadge}</td>
                <td>
                  <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: ${progress}%" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                      ${progress}%
                    </div>
                  </div>
                </td>
                <td class="text-right">${upload.total_rows || 0}</td>
              </tr>
            `;
          }).join('');
        } else {
          tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No uploads yet</td></tr>';
        }
      } catch (error) {
        console.error('Error loading upload status:', error);
        document.getElementById('uploadStatusTable').innerHTML = 
          '<tr><td colspan="5" class="text-center text-danger">Error loading status</td></tr>';
      }
    }
    
    function getStatusBadge(status) {
      const badges = {
        'pending': '<span class="badge badge-warning">Pending</span>',
        'processing': '<span class="badge badge-info">Processing</span>',
        'completed': '<span class="badge badge-success">Completed</span>',
        'failed': '<span class="badge badge-danger">Failed</span>'
      };
      return badges[status] || '<span class="badge badge-secondary">Unknown</span>';
    }
    
    // Load status on page load and refresh every 10 seconds
    document.addEventListener('DOMContentLoaded', function() {
      loadUploadStatus();
      setInterval(loadUploadStatus, 10000);
    });
  </script>
</body>
</html>
