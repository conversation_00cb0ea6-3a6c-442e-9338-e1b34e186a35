<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Dashboard - Megacare HQ PRD</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="/megacare/hqprd/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <!-- DataTables CSS -->
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">

  <style>
    .table th, .table td {
      white-space: nowrap;
      font-size: 12px;
      padding: 8px 6px;
    }
    .table-responsive {
      border-radius: 10px;
    }
    .dataTables_wrapper .dataTables_scroll {
      border-radius: 10px;
    }
    .dataTables_wrapper .dataTables_scrollHead {
      background-color: #f8f9fa;
    }
    .dataTables_wrapper .dataTables_scrollBody {
      border: 1px solid #dee2e6;
    }
  </style>
</head>

<body class="">
  <div class="wrapper ">
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-mini">
          <div class="logo-image-small">
            <img src="/megacare/hqprd/img/logo-small.png">
          </div>
        </a>
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li class="active">
            <a href="/megacare/hqprd/dashboard.html">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li>
            <a href="/megacare/hqprd/upload.html">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload CSV</p>
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Dashboard</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item btn-rotate dropdown">
                <a class="nav-link dropdown-toggle" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="nc-icon nc-single-02"></i>
                  <p>
                    <span class="d-lg-none d-md-block">Account</span>
                  </p>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                  <a class="dropdown-item" href="/megacare/hqprd/logout">Logout</a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <!-- End Navbar -->
      
      <div class="content">
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-globe text-warning"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Total Uploads</p>
                      <p class="card-title" id="totalUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-refresh"></i>
                  All time
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-check-2 text-success"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Completed</p>
                      <p class="card-title" id="completedUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-check"></i>
                  Successfully processed
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-time-alarm text-info"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Processing</p>
                      <p class="card-title" id="processingUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-clock-o"></i>
                  Currently processing
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-favourite-28 text-primary"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Pending</p>
                      <p class="card-title" id="pendingUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-hourglass"></i>
                  Waiting to process
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <div class="row">
                  <div class="col-md-6">
                    <h4 class="card-title">CSV Data</h4>
                  </div>
                  <div class="col-md-6">
                    <div class="row">
                      <div class="col-md-4">
                        <input type="date" id="startDate" class="form-control form-control-sm" placeholder="Start Date">
                      </div>
                      <div class="col-md-4">
                        <input type="date" id="endDate" class="form-control form-control-sm" placeholder="End Date">
                      </div>
                      <div class="col-md-4">
                        <button id="filterBtn" class="btn btn-primary btn-sm">
                          <i class="fa fa-filter"></i> Filter
                        </button>
                        <button id="clearFilterBtn" class="btn btn-secondary btn-sm ml-1">
                          <i class="fa fa-times"></i> Clear
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="dataTab" role="tablist">
                  <li class="nav-item">
                    <a class="nav-link active" id="all-tab" data-toggle="tab" href="#all" role="tab" aria-controls="all" aria-selected="true">
                      <i class="fa fa-list"></i> All Data
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="zm-tab" data-toggle="tab" href="#zm" role="tab" aria-controls="zm" aria-selected="false">
                      <i class="fa fa-user-tie"></i> ZM
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="rm-tab" data-toggle="tab" href="#rm" role="tab" aria-controls="rm" aria-selected="false">
                      <i class="fa fa-users"></i> RM
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="hq-tab" data-toggle="tab" href="#hq" role="tab" aria-controls="hq" aria-selected="false">
                      <i class="fa fa-building"></i> HQ
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="mr-tab" data-toggle="tab" href="#mr" role="tab" aria-controls="mr" aria-selected="false">
                      <i class="fa fa-user-md"></i> MR
                    </a>
                  </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="dataTabContent">
                  <!-- All Data Tab -->
                  <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                    <div class="table-responsive mt-3">
                      <table class="table" id="allDataTable">
                        <thead class="text-primary">
                          <tr>
                            <th>Upload Name</th>
                            <th>Data Month</th>
                            <th>Division Code</th>
                            <th>Division Name</th>
                            <th>Pool Code</th>
                            <th>Pool Name</th>
                            <th>ZM Code</th>
                            <th>ZM Name</th>
                            <th>RM Code</th>
                            <th>RM Name</th>
                            <th>AM Code</th>
                            <th>AM Name</th>
                            <th>MR Current</th>
                            <th>MR Name</th>
                            <th>HQ Name</th>
                            <th>HQ Count 04</th>
                            <th>HQ Count 05</th>
                            <th>MR Count 04</th>
                            <th>MR Count 05</th>
                            <th>Material</th>
                            <th>Material Description</th>
                            <th>Product Common Code</th>
                            <th>Product Common Code Des</th>
                            <th>Product Group</th>
                            <th>Product Group 2</th>
                            <th>Product Group 3</th>
                            <th>Phyzii Disha</th>
                            <th>CY 04 Net Qty</th>
                            <th>CY 04 Net Value</th>
                            <th>CY 04 Trg Qty</th>
                            <th>CY 04 Trg Value</th>
                            <th>CY 05 Net Qty</th>
                            <th>CY 05 Net Value</th>
                            <th>CY 05 Trg Qty</th>
                            <th>CY 05 Trg Value</th>
                            <th>CY 06 Trg Qty</th>
                            <th>CY 06 Trg Value</th>
                            <th>CY YTD Net Qty</th>
                            <th>CY YTD Net Value</th>
                            <th>CY YTD Trg Qty</th>
                            <th>CY YTD Trg Value</th>
                            <th>LY 01 Net Qty</th>
                            <th>LY 01 Net Value</th>
                            <th>LY 02 Net Qty</th>
                            <th>LY 02 Net Value</th>
                            <th>LY 03 Net Qty</th>
                            <th>LY 03 Net Value</th>
                            <th>LY 04 Net Qty</th>
                            <th>LY 04 Net Value</th>
                            <th>LY 05 Net Qty</th>
                            <th>LY 05 Net Value</th>
                            <th>LY 06 Net Qty</th>
                            <th>LY 06 Net Value</th>
                            <th>LY 07 Net Qty</th>
                            <th>LY 07 Net Value</th>
                            <th>LY 08 Net Qty</th>
                            <th>LY 08 Net Value</th>
                            <th>LY 09 Net Qty</th>
                            <th>LY 09 Net Value</th>
                            <th>LY 10 Net Qty</th>
                            <th>LY 10 Net Value</th>
                            <th>LY 11 Net Qty</th>
                            <th>LY 11 Net Value</th>
                            <th>LY 12 Net Qty</th>
                            <th>LY 12 Net Value</th>
                            <th>LY Total Net Qty</th>
                            <th>LY Total Net Value</th>
                            <th>LY YTD Net Qty</th>
                            <th>LY YTD Net Value</th>
                            <th>Processed Date</th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>

                  <!-- ZM Tab -->
                  <div class="tab-pane fade" id="zm" role="tabpanel" aria-labelledby="zm-tab">
                    <div class="table-responsive mt-3">
                      <table class="table" id="zmDataTable">
                        <thead class="text-primary">
                          <tr>
                            <th>Upload Name</th>
                            <th>Data Month</th>
                            <th>Division Code</th>
                            <th>Division Name</th>
                            <th>Pool Code</th>
                            <th>Pool Name</th>
                            <th>ZM Code</th>
                            <th>ZM Name</th>
                            <th>RM Code</th>
                            <th>RM Name</th>
                            <th>AM Code</th>
                            <th>AM Name</th>
                            <th>MR Current</th>
                            <th>MR Name</th>
                            <th>HQ Name</th>
                            <th>HQ Count 04</th>
                            <th>HQ Count 05</th>
                            <th>MR Count 04</th>
                            <th>MR Count 05</th>
                            <th>Material</th>
                            <th>Material Description</th>
                            <th>Product Common Code</th>
                            <th>Product Common Code Des</th>
                            <th>Product Group</th>
                            <th>Product Group 2</th>
                            <th>Product Group 3</th>
                            <th>Phyzii Disha</th>
                            <th>CY 04 Net Qty</th>
                            <th>CY 04 Net Value</th>
                            <th>CY 04 Trg Qty</th>
                            <th>CY 04 Trg Value</th>
                            <th>CY 05 Net Qty</th>
                            <th>CY 05 Net Value</th>
                            <th>CY 05 Trg Qty</th>
                            <th>CY 05 Trg Value</th>
                            <th>CY 06 Trg Qty</th>
                            <th>CY 06 Trg Value</th>
                            <th>CY YTD Net Qty</th>
                            <th>CY YTD Net Value</th>
                            <th>CY YTD Trg Qty</th>
                            <th>CY YTD Trg Value</th>
                            <th>LY 01 Net Qty</th>
                            <th>LY 01 Net Value</th>
                            <th>LY 02 Net Qty</th>
                            <th>LY 02 Net Value</th>
                            <th>LY 03 Net Qty</th>
                            <th>LY 03 Net Value</th>
                            <th>LY 04 Net Qty</th>
                            <th>LY 04 Net Value</th>
                            <th>LY 05 Net Qty</th>
                            <th>LY 05 Net Value</th>
                            <th>LY 06 Net Qty</th>
                            <th>LY 06 Net Value</th>
                            <th>LY 07 Net Qty</th>
                            <th>LY 07 Net Value</th>
                            <th>LY 08 Net Qty</th>
                            <th>LY 08 Net Value</th>
                            <th>LY 09 Net Qty</th>
                            <th>LY 09 Net Value</th>
                            <th>LY 10 Net Qty</th>
                            <th>LY 10 Net Value</th>
                            <th>LY 11 Net Qty</th>
                            <th>LY 11 Net Value</th>
                            <th>LY 12 Net Qty</th>
                            <th>LY 12 Net Value</th>
                            <th>LY Total Net Qty</th>
                            <th>LY Total Net Value</th>
                            <th>LY YTD Net Qty</th>
                            <th>LY YTD Net Value</th>
                            <th>Processed Date</th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>

                  <!-- RM Tab -->
                  <div class="tab-pane fade" id="rm" role="tabpanel" aria-labelledby="rm-tab">
                    <div class="table-responsive mt-3">
                      <table class="table" id="rmDataTable">
                        <thead class="text-primary">
                          <tr>
                            <th>Upload Name</th>
                            <th>Data Month</th>
                            <th>Division Code</th>
                            <th>Division Name</th>
                            <th>Pool Code</th>
                            <th>Pool Name</th>
                            <th>ZM Code</th>
                            <th>ZM Name</th>
                            <th>RM Code</th>
                            <th>RM Name</th>
                            <th>AM Code</th>
                            <th>AM Name</th>
                            <th>MR Current</th>
                            <th>MR Name</th>
                            <th>HQ Name</th>
                            <th>HQ Count 04</th>
                            <th>HQ Count 05</th>
                            <th>MR Count 04</th>
                            <th>MR Count 05</th>
                            <th>Material</th>
                            <th>Material Description</th>
                            <th>Product Common Code</th>
                            <th>Product Common Code Des</th>
                            <th>Product Group</th>
                            <th>Product Group 2</th>
                            <th>Product Group 3</th>
                            <th>Phyzii Disha</th>
                            <th>CY 04 Net Qty</th>
                            <th>CY 04 Net Value</th>
                            <th>CY 04 Trg Qty</th>
                            <th>CY 04 Trg Value</th>
                            <th>CY 05 Net Qty</th>
                            <th>CY 05 Net Value</th>
                            <th>CY 05 Trg Qty</th>
                            <th>CY 05 Trg Value</th>
                            <th>CY 06 Trg Qty</th>
                            <th>CY 06 Trg Value</th>
                            <th>CY YTD Net Qty</th>
                            <th>CY YTD Net Value</th>
                            <th>CY YTD Trg Qty</th>
                            <th>CY YTD Trg Value</th>
                            <th>LY 01 Net Qty</th>
                            <th>LY 01 Net Value</th>
                            <th>LY 02 Net Qty</th>
                            <th>LY 02 Net Value</th>
                            <th>LY 03 Net Qty</th>
                            <th>LY 03 Net Value</th>
                            <th>LY 04 Net Qty</th>
                            <th>LY 04 Net Value</th>
                            <th>LY 05 Net Qty</th>
                            <th>LY 05 Net Value</th>
                            <th>LY 06 Net Qty</th>
                            <th>LY 06 Net Value</th>
                            <th>LY 07 Net Qty</th>
                            <th>LY 07 Net Value</th>
                            <th>LY 08 Net Qty</th>
                            <th>LY 08 Net Value</th>
                            <th>LY 09 Net Qty</th>
                            <th>LY 09 Net Value</th>
                            <th>LY 10 Net Qty</th>
                            <th>LY 10 Net Value</th>
                            <th>LY 11 Net Qty</th>
                            <th>LY 11 Net Value</th>
                            <th>LY 12 Net Qty</th>
                            <th>LY 12 Net Value</th>
                            <th>LY Total Net Qty</th>
                            <th>LY Total Net Value</th>
                            <th>LY YTD Net Qty</th>
                            <th>LY YTD Net Value</th>
                            <th>Processed Date</th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>

                  <!-- HQ Tab -->
                  <div class="tab-pane fade" id="hq" role="tabpanel" aria-labelledby="hq-tab">
                    <div class="table-responsive mt-3">
                      <table class="table" id="hqDataTable">
                        <thead class="text-primary">
                          <tr>
                            <th>Upload Name</th>
                            <th>Data Month</th>
                            <th>Division Code</th>
                            <th>Division Name</th>
                            <th>Pool Code</th>
                            <th>Pool Name</th>
                            <th>ZM Code</th>
                            <th>ZM Name</th>
                            <th>RM Code</th>
                            <th>RM Name</th>
                            <th>AM Code</th>
                            <th>AM Name</th>
                            <th>MR Current</th>
                            <th>MR Name</th>
                            <th>HQ Name</th>
                            <th>HQ Count 04</th>
                            <th>HQ Count 05</th>
                            <th>MR Count 04</th>
                            <th>MR Count 05</th>
                            <th>Material</th>
                            <th>Material Description</th>
                            <th>Product Common Code</th>
                            <th>Product Common Code Des</th>
                            <th>Product Group</th>
                            <th>Product Group 2</th>
                            <th>Product Group 3</th>
                            <th>Phyzii Disha</th>
                            <th>CY 04 Net Qty</th>
                            <th>CY 04 Net Value</th>
                            <th>CY 04 Trg Qty</th>
                            <th>CY 04 Trg Value</th>
                            <th>CY 05 Net Qty</th>
                            <th>CY 05 Net Value</th>
                            <th>CY 05 Trg Qty</th>
                            <th>CY 05 Trg Value</th>
                            <th>CY 06 Trg Qty</th>
                            <th>CY 06 Trg Value</th>
                            <th>CY YTD Net Qty</th>
                            <th>CY YTD Net Value</th>
                            <th>CY YTD Trg Qty</th>
                            <th>CY YTD Trg Value</th>
                            <th>LY 01 Net Qty</th>
                            <th>LY 01 Net Value</th>
                            <th>LY 02 Net Qty</th>
                            <th>LY 02 Net Value</th>
                            <th>LY 03 Net Qty</th>
                            <th>LY 03 Net Value</th>
                            <th>LY 04 Net Qty</th>
                            <th>LY 04 Net Value</th>
                            <th>LY 05 Net Qty</th>
                            <th>LY 05 Net Value</th>
                            <th>LY 06 Net Qty</th>
                            <th>LY 06 Net Value</th>
                            <th>LY 07 Net Qty</th>
                            <th>LY 07 Net Value</th>
                            <th>LY 08 Net Qty</th>
                            <th>LY 08 Net Value</th>
                            <th>LY 09 Net Qty</th>
                            <th>LY 09 Net Value</th>
                            <th>LY 10 Net Qty</th>
                            <th>LY 10 Net Value</th>
                            <th>LY 11 Net Qty</th>
                            <th>LY 11 Net Value</th>
                            <th>LY 12 Net Qty</th>
                            <th>LY 12 Net Value</th>
                            <th>LY Total Net Qty</th>
                            <th>LY Total Net Value</th>
                            <th>LY YTD Net Qty</th>
                            <th>LY YTD Net Value</th>
                            <th>Processed Date</th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>

                  <!-- MR Tab -->
                  <div class="tab-pane fade" id="mr" role="tabpanel" aria-labelledby="mr-tab">
                    <div class="table-responsive mt-3">
                      <table class="table" id="mrDataTable">
                        <thead class="text-primary">
                          <tr>
                            <th>Upload Name</th>
                            <th>Data Month</th>
                            <th>Division Code</th>
                            <th>Division Name</th>
                            <th>Pool Code</th>
                            <th>Pool Name</th>
                            <th>ZM Code</th>
                            <th>ZM Name</th>
                            <th>RM Code</th>
                            <th>RM Name</th>
                            <th>AM Code</th>
                            <th>AM Name</th>
                            <th>MR Current</th>
                            <th>MR Name</th>
                            <th>HQ Name</th>
                            <th>HQ Count 04</th>
                            <th>HQ Count 05</th>
                            <th>MR Count 04</th>
                            <th>MR Count 05</th>
                            <th>Material</th>
                            <th>Material Description</th>
                            <th>Product Common Code</th>
                            <th>Product Common Code Des</th>
                            <th>Product Group</th>
                            <th>Product Group 2</th>
                            <th>Product Group 3</th>
                            <th>Phyzii Disha</th>
                            <th>CY 04 Net Qty</th>
                            <th>CY 04 Net Value</th>
                            <th>CY 04 Trg Qty</th>
                            <th>CY 04 Trg Value</th>
                            <th>CY 05 Net Qty</th>
                            <th>CY 05 Net Value</th>
                            <th>CY 05 Trg Qty</th>
                            <th>CY 05 Trg Value</th>
                            <th>CY 06 Trg Qty</th>
                            <th>CY 06 Trg Value</th>
                            <th>CY YTD Net Qty</th>
                            <th>CY YTD Net Value</th>
                            <th>CY YTD Trg Qty</th>
                            <th>CY YTD Trg Value</th>
                            <th>LY 01 Net Qty</th>
                            <th>LY 01 Net Value</th>
                            <th>LY 02 Net Qty</th>
                            <th>LY 02 Net Value</th>
                            <th>LY 03 Net Qty</th>
                            <th>LY 03 Net Value</th>
                            <th>LY 04 Net Qty</th>
                            <th>LY 04 Net Value</th>
                            <th>LY 05 Net Qty</th>
                            <th>LY 05 Net Value</th>
                            <th>LY 06 Net Qty</th>
                            <th>LY 06 Net Value</th>
                            <th>LY 07 Net Qty</th>
                            <th>LY 07 Net Value</th>
                            <th>LY 08 Net Qty</th>
                            <th>LY 08 Net Value</th>
                            <th>LY 09 Net Qty</th>
                            <th>LY 09 Net Value</th>
                            <th>LY 10 Net Qty</th>
                            <th>LY 10 Net Value</th>
                            <th>LY 11 Net Qty</th>
                            <th>LY 11 Net Value</th>
                            <th>LY 12 Net Qty</th>
                            <th>LY 12 Net Value</th>
                            <th>LY Total Net Qty</th>
                            <th>LY Total Net Value</th>
                            <th>LY YTD Net Qty</th>
                            <th>LY YTD Net Value</th>
                            <th>Processed Date</th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <nav class="footer-nav">
              <ul>
                <li><a href="/megacare/hqprd/dashboard.html">Dashboard</a></li>
                <li><a href="/megacare/hqprd/upload.html">Upload</a></li>
              </ul>
            </nav>
            <div class="credits ml-auto">
              <span class="copyright">
                © <script>document.write(new Date().getFullYear())</script>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="/megacare/hqprd/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/js/core/popper.min.js"></script>
  <script src="/megacare/hqprd/js/core/bootstrap.min.js"></script>
  <script src="/megacare/hqprd/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/megacare/hqprd/js/paper-dashboard.min.js?v=2.0.1" type="text/javascript"></script>
  <!-- DataTables JS -->
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

  <script>
    let dataTables = {};
    let currentTab = 'ALL';

    // Initialize all DataTables
    function initializeDataTables() {
      const tableConfigs = [
        { id: 'allDataTable', tab: 'ALL' },
        { id: 'zmDataTable', tab: 'ZM' },
        { id: 'rmDataTable', tab: 'RM' },
        { id: 'hqDataTable', tab: 'HQ' },
        { id: 'mrDataTable', tab: 'MR' }
      ];

      tableConfigs.forEach(config => {
        if ($.fn.DataTable.isDataTable(`#${config.id}`)) {
          $(`#${config.id}`).DataTable().destroy();
        }

        dataTables[config.tab] = $(`#${config.id}`).DataTable({
          scrollX: true,
          scrollY: '500px',
          scrollCollapse: true,
          pageLength: 10,
          lengthMenu: [[10, 20, 50, 100, -1], [10, 20, 50, 100, "All"]],
          order: [[67, 'desc']], // Sort by processed date descending (last column)
          columnDefs: [
            { targets: [67], className: 'text-center' }, // Processed date center aligned
            { targets: '_all', className: 'text-nowrap' } // Prevent text wrapping
          ],
          language: {
            search: "Search data:",
            lengthMenu: "Show _MENU_ records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ records",
            infoEmpty: "No records available",
            infoFiltered: "(filtered from _MAX_ total records)",
            emptyTable: "No data found",
            zeroRecords: "No matching records found"
          },
          dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
               '<"row"<"col-sm-12"tr>>' +
               '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
        });
      });
    }

    // Load CSV data for specific tab
    async function loadCsvData(tabFilter = 'ALL') {
      try {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        let url = '/megacare/hqprd/api/csv-data';
        const params = new URLSearchParams();

        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        if (tabFilter !== 'ALL') params.append('tabFilter', tabFilter);

        if (params.toString()) {
          url += '?' + params.toString();
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.success && dataTables[tabFilter]) {
          const table = dataTables[tabFilter];
          table.clear();

          if (data.data && data.data.length > 0) {
            console.log(`Loading ${data.data.length} records for tab: ${tabFilter}`);
            data.data.forEach(row => {
              const processedDate = new Date(row.processed_at).toLocaleDateString();

              // Add all fields from the data
              table.row.add([
                row.upload_name || '',
                row.data_month || '',
                row.division_code || '',
                row.division_name || '',
                row.pool_code || '',
                row.pool_name || '',
                row.zm || '',
                row.zm_name || '',
                row.rm || '',
                row.rm_name || '',
                row.am || '',
                row.am_name || '',
                row.mr_current || '',
                row.mr_name || '',
                row.hq_name || '',
                row.hq_count_04 || '',
                row.hq_count_05 || '',
                row.mr_count_04 || '',
                row.mr_count_05 || '',
                row.material || '',
                row.material_description || '',
                row.product_common_code || '',
                row.product_common_code_des || '',
                row.product_group || '',
                row.product_group2 || '',
                row.product_group3 || '',
                row.phyzii_disha || '',
                row.cy_04_net_qty || '',
                row.cy_04_net_value || '',
                row.cy_04_trg_qty || '',
                row.cy_04_trg_value || '',
                row.cy_05_net_qty || '',
                row.cy_05_net_value || '',
                row.cy_05_trg_qty || '',
                row.cy_05_trg_value || '',
                row.cy_06_trg_qty || '',
                row.cy_06_trg_value || '',
                row.cy_ytd_net_qty || '',
                row.cy_ytd_net_value || '',
                row.cy_ytd_trgqty || '',
                row.cy_ytd_trgval || '',
                row.ly_01_net_qty || '',
                row.ly_01_net_value || '',
                row.ly_02_net_qty || '',
                row.ly_02_net_value || '',
                row.ly_03_net_qty || '',
                row.ly_03_net_value || '',
                row.ly_04_net_qty || '',
                row.ly_04_net_value || '',
                row.ly_05_net_qty || '',
                row.ly_05_net_value || '',
                row.ly_06_net_qty || '',
                row.ly_06_net_value || '',
                row.ly_07_net_qty || '',
                row.ly_07_net_value || '',
                row.ly_08_net_qty || '',
                row.ly_08_net_value || '',
                row.ly_09_net_qty || '',
                row.ly_09_net_value || '',
                row.ly_10_net_qty || '',
                row.ly_10_net_value || '',
                row.ly_11_net_qty || '',
                row.ly_11_net_value || '',
                row.ly_12_net_qty || '',
                row.ly_12_net_value || '',
                row.ly_total_net_qty || '',
                row.ly_total_net_value || '',
                row.ly_ytd_net_qty || '',
                row.ly_ytd_net_value || '',
                processedDate
              ]);
            });
          }

          table.draw();
        }
      } catch (error) {
        console.error('Error loading CSV data:', error);
        if (dataTables[tabFilter]) {
          dataTables[tabFilter].clear().draw();
        }
      }
    }

    // Load dashboard data
    async function loadDashboardData() {
      try {
        const response = await fetch('/megacare/hqprd/api/dashboard-data');
        const data = await response.json();
        
        if (data.success) {
          // Update statistics
          document.getElementById('totalUploads').textContent = data.stats.totalUploads;
          document.getElementById('completedUploads').textContent = data.stats.completedUploads;
          document.getElementById('processingUploads').textContent = data.stats.processingUploads;
          document.getElementById('pendingUploads').textContent = data.stats.pendingUploads;
          
          // Load CSV data for current tab
          loadCsvData(currentTab);
        }
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        if (dataTables[currentTab]) {
          dataTables[currentTab].clear().draw();
        }
      }
    }
    
    function getStatusBadge(status) {
      const badges = {
        'pending': '<span class="badge badge-warning">Pending</span>',
        'processing': '<span class="badge badge-info">Processing</span>',
        'completed': '<span class="badge badge-success">Completed</span>',
        'failed': '<span class="badge badge-danger">Failed</span>'
      };
      return badges[status] || '<span class="badge badge-secondary">Unknown</span>';
    }

    // Tab switching functionality
    function setupTabSwitching() {
      $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        const tabId = $(e.target).attr('href').substring(1); // Remove #
        currentTab = tabId.toUpperCase();

        // Load data for the selected tab
        loadCsvData(currentTab);
      });
    }

    // Filter functionality
    function setupFilters() {
      document.getElementById('filterBtn').addEventListener('click', function() {
        loadCsvData(currentTab);
      });

      document.getElementById('clearFilterBtn').addEventListener('click', function() {
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        loadCsvData(currentTab);
      });

      // Enter key support for date inputs
      document.getElementById('startDate').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          loadCsvData(currentTab);
        }
      });

      document.getElementById('endDate').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          loadCsvData(currentTab);
        }
      });
    }
    
    // Load data on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize all DataTables first
      initializeDataTables();

      // Setup tab switching and filters
      setupTabSwitching();
      setupFilters();

      // Then load data
      loadDashboardData();

      // Refresh data every 30 seconds
      setInterval(loadDashboardData, 30000);
    });
  </script>
</body>
</html>
