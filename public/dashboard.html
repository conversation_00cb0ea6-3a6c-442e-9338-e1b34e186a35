<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Dashboard - Megacare HQ PRD</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="/megacare/hqprd/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <!-- DataTables CSS -->
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
</head>

<body class="">
  <div class="wrapper ">
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-mini">
          <div class="logo-image-small">
            <img src="/megacare/hqprd/img/logo-small.png">
          </div>
        </a>
        <a href="/megacare/hqprd/dashboard.html" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li class="active">
            <a href="/megacare/hqprd/dashboard.html">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li>
            <a href="/megacare/hqprd/upload.html">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload CSV</p>
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Dashboard</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item btn-rotate dropdown">
                <a class="nav-link dropdown-toggle" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="nc-icon nc-single-02"></i>
                  <p>
                    <span class="d-lg-none d-md-block">Account</span>
                  </p>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                  <a class="dropdown-item" href="/megacare/hqprd/logout">Logout</a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <!-- End Navbar -->
      
      <div class="content">
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-globe text-warning"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Total Uploads</p>
                      <p class="card-title" id="totalUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-refresh"></i>
                  All time
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-check-2 text-success"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Completed</p>
                      <p class="card-title" id="completedUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-check"></i>
                  Successfully processed
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-time-alarm text-info"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Processing</p>
                      <p class="card-title" id="processingUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-clock-o"></i>
                  Currently processing
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body ">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-favourite-28 text-primary"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Pending</p>
                      <p class="card-title" id="pendingUploads">0</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer ">
                <hr>
                <div class="stats">
                  <i class="fa fa-hourglass"></i>
                  Waiting to process
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Recent Uploads</h4>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table" id="recentUploadsDataTable">
                    <thead class="text-primary">
                      <tr>
                        <th>Upload Name</th>
                        <th>Filename</th>
                        <th>Month</th>
                        <th>Upload Date</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th class="text-right">Total Rows</th>
                      </tr>
                    </thead>
                    <tbody id="recentUploadsTable">
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <nav class="footer-nav">
              <ul>
                <li><a href="/megacare/hqprd/dashboard.html">Dashboard</a></li>
                <li><a href="/megacare/hqprd/upload.html">Upload</a></li>
              </ul>
            </nav>
            <div class="credits ml-auto">
              <span class="copyright">
                © <script>document.write(new Date().getFullYear())</script>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="/megacare/hqprd/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/js/core/popper.min.js"></script>
  <script src="/megacare/hqprd/js/core/bootstrap.min.js"></script>
  <script src="/megacare/hqprd/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/megacare/hqprd/js/paper-dashboard.min.js?v=2.0.1" type="text/javascript"></script>
  <!-- DataTables JS -->
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

  <script>
    let recentUploadsTable;

    // Initialize DataTable
    function initializeDataTable() {
      if ($.fn.DataTable.isDataTable('#recentUploadsDataTable')) {
        $('#recentUploadsDataTable').DataTable().destroy();
      }

      recentUploadsTable = $('#recentUploadsDataTable').DataTable({
        responsive: true,
        pageLength: 10,
        lengthMenu: [[10, 20, 50, 100, -1], [10, 20, 50, 100, "All"]],
        order: [[3, 'desc']], // Sort by upload date descending
        columnDefs: [
          { targets: [5], orderable: false }, // Progress column not sortable
          { targets: [6], className: 'text-right' } // Total rows right aligned
        ],
        language: {
          search: "Search uploads:",
          lengthMenu: "Show _MENU_ uploads per page",
          info: "Showing _START_ to _END_ of _TOTAL_ uploads",
          infoEmpty: "No uploads available",
          infoFiltered: "(filtered from _MAX_ total uploads)",
          emptyTable: "No uploads found",
          zeroRecords: "No matching uploads found"
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
      });
    }

    // Load dashboard data
    async function loadDashboardData() {
      try {
        const response = await fetch('/megacare/hqprd/api/dashboard-data');
        const data = await response.json();
        
        if (data.success) {
          // Update statistics
          document.getElementById('totalUploads').textContent = data.stats.totalUploads;
          document.getElementById('completedUploads').textContent = data.stats.completedUploads;
          document.getElementById('processingUploads').textContent = data.stats.processingUploads;
          document.getElementById('pendingUploads').textContent = data.stats.pendingUploads;
          
          // Update DataTable
          if (recentUploadsTable) {
            recentUploadsTable.clear();

            if (data.recentUploads.length > 0) {
              data.recentUploads.forEach(upload => {
                const statusBadge = getStatusBadge(upload.status);
                const progress = upload.total_rows > 0 ? Math.round((upload.processed_rows / upload.total_rows) * 100) : 0;
                const uploadDate = new Date(upload.upload_date).toLocaleDateString();
                const monthDisplay = upload.selected_month || 'N/A';

                const progressBar = `
                  <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: ${progress}%" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                      ${progress}%
                    </div>
                  </div>
                `;

                recentUploadsTable.row.add([
                  upload.upload_name || 'N/A',
                  upload.original_filename || upload.filename,
                  monthDisplay,
                  uploadDate,
                  statusBadge,
                  progressBar,
                  upload.total_rows || 0
                ]);
              });
            }

            recentUploadsTable.draw();
          }
        }
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        if (recentUploadsTable) {
          recentUploadsTable.clear().draw();
        }
      }
    }
    
    function getStatusBadge(status) {
      const badges = {
        'pending': '<span class="badge badge-warning">Pending</span>',
        'processing': '<span class="badge badge-info">Processing</span>',
        'completed': '<span class="badge badge-success">Completed</span>',
        'failed': '<span class="badge badge-danger">Failed</span>'
      };
      return badges[status] || '<span class="badge badge-secondary">Unknown</span>';
    }
    
    // Load data on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize DataTable first
      initializeDataTable();

      // Then load data
      loadDashboardData();

      // Refresh data every 30 seconds
      setInterval(loadDashboardData, 30000);
    });
  </script>
</body>
</html>
