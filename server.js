const express = require('express');
const session = require('express-session');
const flash = require('express-flash');
const path = require('path');
require('dotenv').config();

// Import database and models
const { testConnection } = require('./config/database');
const { syncDatabase } = require('./models');

// Import routes
const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const indexRoutes = require('./routes/index');

const app = express();
const PORT = process.env.PORT || 3000;
const BASE_URL = process.env.BASE_URL || '/megacare/hqprd';

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'megacare_hqprd_secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Flash messages
app.use(flash());

// Serve static HTML files
app.use(express.static(path.join(__dirname, 'public')));

// Static files - serve from /megacare/hqprd path
app.use(BASE_URL, express.static(path.join(__dirname, 'public')));

// Routes
app.use('/', indexRoutes);
app.use(BASE_URL, authRoutes);
app.use(BASE_URL, dashboardRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Handle multer errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    req.flash('error', 'File too large. Maximum size is 10MB.');
    return res.redirect(BASE_URL + '/upload');
  }
  
  if (err.message === 'Only CSV files are allowed!') {
    req.flash('error', 'Only CSV files are allowed.');
    return res.redirect(BASE_URL + '/upload');
  }
  
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: '404 - Page Not Found',
    message: 'The page you are looking for does not exist.'
  });
});

// Initialize database and start server
async function startServer() {
  try {
    // Test database connection
    await testConnection();

    // Sync database (create tables)
    await syncDatabase();

    console.log('Database connected and synchronized successfully.');
  } catch (error) {
    console.warn('Database connection failed, continuing without database:', error.message);
    console.warn('Please ensure MySQL is running and database credentials are correct.');
  }

  // Start server regardless of database status
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Application available at: http://localhost:${PORT}${BASE_URL}`);
    console.log(`Login with: admin / megacare`);
  });
}

startServer();
