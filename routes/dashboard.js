const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const uploadController = require('../controllers/uploadController');
const { requireAuth } = require('../middleware/auth');

// Apply authentication middleware to all dashboard routes
router.use(requireAuth);

// Dashboard routes
router.get('/dashboard', dashboardController.showDashboard);

// Upload routes
router.get('/upload', uploadController.showUpload);
router.post('/upload', uploadController.uploadFile, uploadController.processUpload);

// API routes
router.get('/api/upload-status', uploadController.getUploadStatus);

module.exports = router;
