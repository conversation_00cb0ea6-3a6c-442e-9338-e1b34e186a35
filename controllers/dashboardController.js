const { models } = require('../models');
const path = require('path');

const dashboardController = {
  // Show dashboard page
  showDashboard: (req, res) => {
    res.sendFile(path.join(__dirname, '../public/dashboard.html'));
  },

  // Get dashboard data API
  getDashboardData: async (req, res) => {
    try {
      // Mock data if database is not connected
      let stats = {
        totalUploads: 0,
        completedUploads: 0,
        pendingUploads: 0,
        processingUploads: 0
      };
      let recentUploads = [];

      // Try to get real data if database is available
      try {
        const totalUploads = await models.CsvUploadMeta.count();
        const completedUploads = await models.CsvUploadMeta.count({
          where: { status: 'completed' }
        });
        const pendingUploads = await models.CsvUploadMeta.count({
          where: { status: 'pending' }
        });
        const processingUploads = await models.CsvUploadMeta.count({
          where: { status: 'processing' }
        });

        const uploads = await models.CsvUploadMeta.findAll({
          limit: 10,
          order: [['created_at', 'DESC']],
          attributes: [
            'id',
            'upload_name',
            'original_filename',
            'selected_month',
            'table_name',
            'upload_date',
            'status',
            'total_rows',
            'processed_rows',
            'uploaded_by'
          ]
        });

        stats = {
          totalUploads,
          completedUploads,
          pendingUploads,
          processingUploads
        };
        recentUploads = uploads;
      } catch (dbError) {
        console.log('Database not available, using mock data');
        // Provide mock data with new structure
        recentUploads = [
          {
            id: 1,
            upload_name: 'Sample Upload',
            original_filename: 'sample_data.csv',
            selected_month: '2024-01',
            table_name: 'csv_data_2024_01',
            upload_date: new Date(),
            status: 'completed',
            total_rows: 100,
            processed_rows: 100,
            uploaded_by: 'admin'
          }
        ];
      }

      res.json({
        success: true,
        user: req.session.user,
        stats,
        recentUploads
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      res.status(500).json({
        success: false,
        message: 'Error loading dashboard data'
      });
    }
  }
};

module.exports = dashboardController;
