const { models } = require('../models');

const dashboardController = {
  // Show dashboard page
  showDashboard: async (req, res) => {
    try {
      // Get recent CSV uploads for dashboard stats
      const recentUploads = await models.CsvData.findAll({
        limit: 10,
        order: [['created_at', 'DESC']],
        attributes: ['id', 'filename', 'upload_date', 'status', 'total_rows', 'processed_rows']
      });

      // Get upload statistics
      const totalUploads = await models.CsvData.count();
      const completedUploads = await models.CsvData.count({
        where: { status: 'completed' }
      });
      const pendingUploads = await models.CsvData.count({
        where: { status: 'pending' }
      });
      const processingUploads = await models.CsvData.count({
        where: { status: 'processing' }
      });

      res.render('dashboard', {
        title: 'Dashboard - Megacare HQ PRD',
        user: req.session.user,
        baseUrl: process.env.BASE_URL,
        stats: {
          totalUploads,
          completedUploads,
          pendingUploads,
          processingUploads
        },
        recentUploads
      });
    } catch (error) {
      console.error('Error loading dashboard:', error);
      res.status(500).render('error', {
        title: 'Error - Megacare HQ PRD',
        message: 'Error loading dashboard',
        baseUrl: process.env.BASE_URL
      });
    }
  }
};

module.exports = dashboardController;
