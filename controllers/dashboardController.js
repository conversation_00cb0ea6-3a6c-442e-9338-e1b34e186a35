const { models } = require('../models');
const { sequelize } = require('../config/database');
const path = require('path');

const dashboardController = {
  // Show dashboard page
  showDashboard: (req, res) => {
    res.sendFile(path.join(__dirname, '../public/dashboard.html'));
  },

  // Get dashboard data API
  getDashboardData: async (req, res) => {
    try {
      // Mock data if database is not connected
      let stats = {
        totalUploads: 0,
        completedUploads: 0,
        pendingUploads: 0,
        processingUploads: 0
      };
      let recentUploads = [];

      // Try to get real data if database is available
      try {
        const totalUploads = await models.CsvUploadMeta.count();
        const completedUploads = await models.CsvUploadMeta.count({
          where: { status: 'completed' }
        });
        const pendingUploads = await models.CsvUploadMeta.count({
          where: { status: 'pending' }
        });
        const processingUploads = await models.CsvUploadMeta.count({
          where: { status: 'processing' }
        });

        const uploads = await models.CsvUploadMeta.findAll({
          limit: 10,
          order: [['created_at', 'DESC']],
          attributes: [
            'id',
            'upload_name',
            'original_filename',
            'selected_month',
            'table_name',
            'upload_date',
            'status',
            'total_rows',
            'processed_rows',
            'uploaded_by'
          ]
        });

        stats = {
          totalUploads,
          completedUploads,
          pendingUploads,
          processingUploads
        };
        recentUploads = uploads;
      } catch (dbError) {
        console.log('Database not available, using mock data');
        // Provide mock data with new structure
        recentUploads = [
          {
            id: 1,
            upload_name: 'Sample Upload',
            original_filename: 'sample_data.csv',
            selected_month: '2024-01',
            table_name: 'csv_data_2024_01',
            upload_date: new Date(),
            status: 'completed',
            total_rows: 100,
            processed_rows: 100,
            uploaded_by: 'admin'
          }
        ];
      }

      res.json({
        success: true,
        user: req.session.user,
        stats,
        recentUploads
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      res.status(500).json({
        success: false,
        message: 'Error loading dashboard data'
      });
    }
  },

  // Get unified CSV data from all monthly tables
  getUnifiedCsvData: async (req, res) => {
    try {
      const { startDate, endDate, tabFilter } = req.query;

      // Get all monthly tables that exist
      const tablesQuery = `
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME LIKE 'csv_data_%'
        ORDER BY TABLE_NAME
      `;

      const [tables] = await sequelize.query(tablesQuery);

      if (tables.length === 0) {
        return res.json({
          success: true,
          data: [],
          message: 'No data tables found'
        });
      }

      // Build union query to get data from all monthly tables
      let unionQueries = [];

      for (const table of tables) {
        const tableName = table.TABLE_NAME;

        // Extract month from table name (csv_data_YYYY_MM)
        const monthMatch = tableName.match(/csv_data_(\d{4})_(\d{2})/);
        if (!monthMatch) continue;

        const year = monthMatch[1];
        const month = monthMatch[2];
        const tableMonth = `${year}-${month}`;

        // Build query with proper column selection - use * for now to avoid column issues
        let query = `SELECT '${tableMonth}' as data_month, upload_meta_id, row_number, processed_at, emp_code, emp_name, h_q, level, region, status FROM ${tableName} WHERE 1=1`;

        // Add date filtering if provided
        if (startDate) {
          query += ` AND DATE(processed_at) >= '${startDate}'`;
        }
        if (endDate) {
          query += ` AND DATE(processed_at) <= '${endDate}'`;
        }

        // Add tab-specific filtering
        if (tabFilter && tabFilter !== 'ALL') {
          switch (tabFilter.toUpperCase()) {
            case 'ZM':
              query += ` AND (level LIKE '%ZM%' OR level LIKE '%Zone%' OR level LIKE '%Zonal%')`;
              break;
            case 'RM':
              query += ` AND (level LIKE '%RM%' OR level LIKE '%Regional%' OR level LIKE '%Region%')`;
              break;
            case 'HQ':
              query += ` AND (h_q IS NOT NULL AND h_q != '' AND h_q != 'N/A')`;
              break;
            case 'MR':
              query += ` AND (level LIKE '%MR%' OR level LIKE '%Medical%' OR level LIKE '%Rep%')`;
              break;
          }
        }

        unionQueries.push(query);
      }

      if (unionQueries.length === 0) {
        return res.json({
          success: true,
          data: [],
          message: 'No matching data found'
        });
      }

      // Combine all queries with UNION and add ordering
      let finalQuery;
      if (unionQueries.length === 1) {
        // Single table, no UNION needed
        finalQuery = unionQueries[0] + ' ORDER BY processed_at DESC LIMIT 1000';
      } else {
        // Multiple tables, use UNION ALL
        finalQuery = unionQueries.join(' UNION ALL ') + ' ORDER BY processed_at DESC LIMIT 1000';
      }

      console.log('Final Query:', finalQuery); // Debug log

      const [csvData] = await sequelize.query(finalQuery);

      // Get upload metadata for additional context
      const uploadMetas = await models.CsvUploadMeta.findAll({
        attributes: ['id', 'upload_name', 'original_filename', 'selected_month']
      });

      // Create a map for quick lookup
      const metaMap = {};
      uploadMetas.forEach(meta => {
        metaMap[meta.id] = meta;
      });

      // Enhance data with upload metadata
      const enhancedData = csvData.map(row => ({
        ...row,
        upload_name: metaMap[row.upload_meta_id]?.upload_name || 'Unknown',
        original_filename: metaMap[row.upload_meta_id]?.original_filename || 'Unknown'
      }));

      res.json({
        success: true,
        data: enhancedData,
        totalRecords: enhancedData.length,
        tablesScanned: tables.length
      });

    } catch (error) {
      console.error('Error fetching unified CSV data:', error);
      res.status(500).json({
        success: false,
        message: 'Error fetching CSV data',
        error: error.message
      });
    }
  }
};

module.exports = dashboardController;
