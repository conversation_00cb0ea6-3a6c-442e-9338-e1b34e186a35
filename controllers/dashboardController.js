const { models } = require('../models');
const path = require('path');

const dashboardController = {
  // Show dashboard page
  showDashboard: (req, res) => {
    res.sendFile(path.join(__dirname, '../public/dashboard.html'));
  },

  // Get dashboard data API
  getDashboardData: async (req, res) => {
    try {
      // Mock data if database is not connected
      let stats = {
        totalUploads: 0,
        completedUploads: 0,
        pendingUploads: 0,
        processingUploads: 0
      };
      let recentUploads = [];

      // Try to get real data if database is available
      try {
        const totalUploads = await models.CsvData.count();
        const completedUploads = await models.CsvData.count({
          where: { status: 'completed' }
        });
        const pendingUploads = await models.CsvData.count({
          where: { status: 'pending' }
        });
        const processingUploads = await models.CsvData.count({
          where: { status: 'processing' }
        });

        const uploads = await models.CsvData.findAll({
          limit: 10,
          order: [['created_at', 'DESC']],
          attributes: ['id', 'filename', 'upload_date', 'status', 'total_rows', 'processed_rows']
        });

        stats = {
          totalUploads,
          completedUploads,
          pendingUploads,
          processingUploads
        };
        recentUploads = uploads;
      } catch (dbError) {
        console.log('Database not available, using mock data');
      }

      res.json({
        success: true,
        user: req.session.user,
        stats,
        recentUploads
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      res.status(500).json({
        success: false,
        message: 'Error loading dashboard data'
      });
    }
  }
};

module.exports = dashboardController;
