const { validateLogin } = require('../middleware/auth');

const path = require('path');

const authController = {
  // Show login page
  showLogin: (req, res) => {
    res.sendFile(path.join(__dirname, '../public/login.html'));
  },

  // Handle login POST request
  login: (req, res) => {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide both username and password'
      });
    }

    if (validateLogin(username, password)) {
      req.session.authenticated = true;
      req.session.user = { username: 'admin' };
      return res.json({
        success: true,
        message: 'Login successful',
        redirectUrl: '/megacare/hqprd/dashboard.html'
      });
    } else {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password'
      });
    }
  },

  // Handle logout
  logout: (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        console.error('Error destroying session:', err);
      }
      res.redirect('/megacare/hqprd/login');
    });
  }
};

module.exports = authController;
