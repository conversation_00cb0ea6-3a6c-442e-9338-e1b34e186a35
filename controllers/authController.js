const { validateLogin } = require('../middleware/auth');

const authController = {
  // Show login page
  showLogin: (req, res) => {
    const error = req.flash('error');
    res.render('login', { 
      title: 'Login - Megacare HQ PRD',
      error: error.length > 0 ? error[0] : null,
      baseUrl: process.env.BASE_URL
    });
  },

  // Handle login POST request
  login: (req, res) => {
    const { username, password } = req.body;

    if (!username || !password) {
      req.flash('error', 'Please provide both username and password');
      return res.redirect('/megacare/hqprd/login');
    }

    if (validateLogin(username, password)) {
      req.session.authenticated = true;
      req.session.user = { username: 'admin' };
      return res.redirect('/megacare/hqprd/dashboard');
    } else {
      req.flash('error', 'Invalid username or password');
      return res.redirect('/megacare/hqprd/login');
    }
  },

  // Handle logout
  logout: (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        console.error('Error destroying session:', err);
      }
      res.redirect('/megacare/hqprd/login');
    });
  }
};

module.exports = authController;
