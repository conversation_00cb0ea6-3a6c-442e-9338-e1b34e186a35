const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { Worker } = require('worker_threads');
const { models } = require('../models');
const TableManager = require('../utils/tableManager');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv' || path.extname(file.originalname).toLowerCase() === '.csv') {
    cb(null, true);
  } else {
    cb(new Error('Only CSV files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

const uploadController = {
  // Show upload page
  showUpload: (req, res) => {
    res.sendFile(path.join(__dirname, '../public/upload.html'));
  },

  // Handle file upload
  uploadFile: upload.single('csvFile'),

  // Process uploaded file
  processUpload: async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      const { selectedMonth, uploadName } = req.body;

      // Validate required fields
      if (!selectedMonth) {
        fs.unlinkSync(req.file.path);
        return res.status(400).json({
          success: false,
          message: 'Selected month is required'
        });
      }

      if (!uploadName || !uploadName.trim()) {
        fs.unlinkSync(req.file.path);
        return res.status(400).json({
          success: false,
          message: 'Upload name is required'
        });
      }

      // Validate month format (YYYY-MM)
      const monthRegex = /^\d{4}-\d{2}$/;
      if (!monthRegex.test(selectedMonth)) {
        fs.unlinkSync(req.file.path);
        return res.status(400).json({
          success: false,
          message: 'Invalid month format. Expected YYYY-MM'
        });
      }

      try {
        // Check if upload name already exists
        const existingUpload = await models.CsvUploadMeta.findOne({
          where: { upload_name: uploadName.trim() }
        });

        if (existingUpload) {
          fs.unlinkSync(req.file.path);
          return res.status(400).json({
            success: false,
            message: 'Upload name already exists. Please choose a different name.'
          });
        }

        // Generate table name for the selected month
        const tableName = TableManager.generateTableName(selectedMonth);

        // Create database record for the upload
        const csvUploadMeta = await models.CsvUploadMeta.create({
          upload_name: uploadName.trim(),
          original_filename: req.file.originalname,
          selected_month: selectedMonth,
          table_name: tableName,
          status: 'pending',
          uploaded_by: 'admin' // TODO: Get from session
        });

        // Start worker thread to process the CSV
        const worker = new Worker(path.join(__dirname, '../workers/csvProcessor.js'), {
          workerData: {
            filePath: req.file.path,
            uploadMetaId: csvUploadMeta.id,
            originalFilename: req.file.originalname,
            selectedMonth: selectedMonth,
            tableName: tableName,
            uploadName: uploadName.trim()
          }
        });

        worker.on('message', async (message) => {
          if (message.type === 'progress') {
            // Update progress in database
            await models.CsvUploadMeta.update(
              {
                processed_rows: message.processedRows,
                total_rows: message.totalRows,
                status: 'processing'
              },
              { where: { id: csvUploadMeta.id } }
            );
          } else if (message.type === 'complete') {
            // Mark as completed
            await models.CsvUploadMeta.update(
              {
                status: 'completed',
                processed_rows: message.totalProcessed,
                total_rows: message.totalRows
              },
              { where: { id: csvUploadMeta.id } }
            );
          } else if (message.type === 'error') {
            // Mark as failed
            await models.CsvUploadMeta.update(
              {
                status: 'failed',
                error_message: message.error
              },
              { where: { id: csvUploadMeta.id } }
            );
          }
        });

        worker.on('error', async (error) => {
          console.error('Worker error:', error);
          await models.CsvUploadMeta.update(
            {
              status: 'failed',
              error_message: error.message
            },
            { where: { id: csvUploadMeta.id } }
          );
        });

        res.json({
          success: true,
          message: `File "${req.file.originalname}" uploaded successfully for ${selectedMonth} and is being processed.`,
          uploadId: csvUploadMeta.id,
          tableName: tableName,
          selectedMonth: selectedMonth
        });
      } catch (dbError) {
        console.log('Database not available, simulating upload processing');
        // Clean up uploaded file
        fs.unlinkSync(req.file.path);

        // Simulate processing without database
        res.json({
          success: true,
          message: `File "${req.file.originalname}" uploaded successfully for ${selectedMonth}. (Database not connected - simulation mode)`,
          uploadId: 'mock-' + Date.now(),
          tableName: TableManager.generateTableName(selectedMonth),
          selectedMonth: selectedMonth
        });
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Clean up uploaded file if it exists
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: 'Error processing upload: ' + error.message
      });
    }
  },

  // Get upload status (API endpoint)
  getUploadStatus: async (req, res) => {
    try {
      let uploads = [];

      try {
        uploads = await models.CsvUploadMeta.findAll({
          order: [['created_at', 'DESC']],
          limit: 20,
          attributes: [
            'id',
            'upload_name',
            'original_filename',
            'selected_month',
            'table_name',
            'total_rows',
            'processed_rows',
            'status',
            'upload_date',
            'uploaded_by',
            'error_message'
          ]
        });
      } catch (dbError) {
        console.log('Database not available, returning mock data');
        // Return mock data when database is not available
        uploads = [
          {
            id: 1,
            upload_name: 'Sample Upload',
            original_filename: 'sample_data.csv',
            selected_month: '2024-01',
            table_name: 'csv_data_2024_01',
            upload_date: new Date(),
            status: 'completed',
            total_rows: 10,
            processed_rows: 10,
            uploaded_by: 'admin'
          }
        ];
      }

      res.json({
        success: true,
        uploads: uploads
      });
    } catch (error) {
      console.error('Error fetching upload status:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
};

module.exports = uploadController;
