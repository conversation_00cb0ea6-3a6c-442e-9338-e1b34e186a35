const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { Worker } = require('worker_threads');
const { models } = require('../models');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv' || path.extname(file.originalname).toLowerCase() === '.csv') {
    cb(null, true);
  } else {
    cb(new Error('Only CSV files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

const uploadController = {
  // Show upload page
  showUpload: (req, res) => {
    const success = req.flash('success');
    const error = req.flash('error');
    
    res.render('upload', {
      title: 'Upload CSV - Megacare HQ PRD',
      user: req.session.user,
      baseUrl: process.env.BASE_URL,
      success: success.length > 0 ? success[0] : null,
      error: error.length > 0 ? error[0] : null
    });
  },

  // Handle file upload
  uploadFile: upload.single('csvFile'),

  // Process uploaded file
  processUpload: async (req, res) => {
    try {
      if (!req.file) {
        req.flash('error', 'No file uploaded');
        return res.redirect('/megacare/hqprd/upload');
      }

      // Create database record for the upload
      const csvData = await models.CsvData.create({
        filename: req.file.originalname,
        upload_date: new Date(),
        status: 'pending'
      });

      // Start worker thread to process the CSV
      const worker = new Worker(path.join(__dirname, '../workers/csvProcessor.js'), {
        workerData: {
          filePath: req.file.path,
          csvDataId: csvData.id,
          originalFilename: req.file.originalname
        }
      });

      worker.on('message', async (message) => {
        if (message.type === 'progress') {
          // Update progress in database
          await models.CsvData.update(
            { 
              processed_rows: message.processedRows,
              total_rows: message.totalRows,
              status: 'processing'
            },
            { where: { id: csvData.id } }
          );
        } else if (message.type === 'complete') {
          // Mark as completed
          await models.CsvData.update(
            { 
              status: 'completed',
              processed_rows: message.totalProcessed,
              total_rows: message.totalRows
            },
            { where: { id: csvData.id } }
          );
        } else if (message.type === 'error') {
          // Mark as failed
          await models.CsvData.update(
            { 
              status: 'failed',
              error_message: message.error
            },
            { where: { id: csvData.id } }
          );
        }
      });

      worker.on('error', async (error) => {
        console.error('Worker error:', error);
        await models.CsvData.update(
          { 
            status: 'failed',
            error_message: error.message
          },
          { where: { id: csvData.id } }
        );
      });

      req.flash('success', `File "${req.file.originalname}" uploaded successfully and is being processed.`);
      res.redirect('/megacare/hqprd/upload');

    } catch (error) {
      console.error('Upload error:', error);
      req.flash('error', 'Error processing upload: ' + error.message);
      res.redirect('/megacare/hqprd/upload');
    }
  },

  // Get upload status (API endpoint)
  getUploadStatus: async (req, res) => {
    try {
      const uploads = await models.CsvData.findAll({
        order: [['created_at', 'DESC']],
        limit: 20
      });

      res.json({
        success: true,
        uploads: uploads
      });
    } catch (error) {
      console.error('Error fetching upload status:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
};

module.exports = uploadController;
