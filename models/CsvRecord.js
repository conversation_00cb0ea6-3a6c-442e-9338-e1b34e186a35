const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const CsvData = require('./CsvData');

const CsvRecord = sequelize.define('CsvRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  csv_data_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: CsvData,
      key: 'id'
    },
    comment: 'Foreign key to csv_uploads table'
  },
  row_number: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Row number in the original CSV file'
  },
  data: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: 'JSON representation of the CSV row data'
  },
  processed_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Timestamp when this record was processed'
  }
}, {
  tableName: 'csv_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['csv_data_id']
    },
    {
      fields: ['csv_data_id', 'row_number']
    }
  ]
});

// Define associations
CsvData.hasMany(CsvRecord, { foreignKey: 'csv_data_id', as: 'records' });
CsvRecord.belongsTo(CsvData, { foreignKey: 'csv_data_id', as: 'csvData' });

module.exports = CsvRecord;
