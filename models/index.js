const { sequelize } = require('../config/database');
const CsvData = require('./CsvData');
const CsvUploadMeta = require('./CsvUploadMeta');

// Initialize all models
const models = {
  CsvData,
  CsvUploadMeta
};

// Sync database (create tables if they don't exist)
const syncDatabase = async () => {
  try {
    await sequelize.sync({ alter: true });
    console.log('Database synchronized successfully.');
  } catch (error) {
    console.error('Error synchronizing database:', error);
  }
};

module.exports = {
  sequelize,
  models,
  syncDatabase
};
