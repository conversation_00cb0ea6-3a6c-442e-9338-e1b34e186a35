const { sequelize } = require('../config/database');
const CsvData = require('./CsvData');
const CsvRecord = require('./CsvRecord');

// Initialize all models
const models = {
  CsvData,
  CsvRecord
};

// Sync database (create tables if they don't exist)
const syncDatabase = async () => {
  try {
    await sequelize.sync({ alter: true });
    console.log('Database synchronized successfully.');
  } catch (error) {
    console.error('Error synchronizing database:', error);
  }
};

module.exports = {
  sequelize,
  models,
  syncDatabase
};
