const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CsvUploadMeta = sequelize.define('CsvUploadMeta', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  upload_name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'Unique name for the CSV upload batch'
  },
  original_filename: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Original filename of the uploaded CSV'
  },
  selected_month: {
    type: DataTypes.STRING(7), // Format: YYYY-MM
    allowNull: false,
    comment: 'Selected month for data storage (YYYY-MM format)'
  },
  table_name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Generated table name for monthly data storage'
  },
  total_rows: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Total number of rows in the CSV file'
  },
  processed_rows: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Number of rows successfully processed'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: 'Processing status of the CSV file'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Error message if processing failed'
  },
  upload_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the CSV was uploaded'
  },
  uploaded_by: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'admin',
    comment: 'User who uploaded the CSV'
  }
}, {
  tableName: 'csv_upload_meta',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['selected_month']
    },
    {
      fields: ['table_name']
    },
    {
      fields: ['upload_name']
    },
    {
      fields: ['status']
    }
  ]
});

module.exports = CsvUploadMeta;
