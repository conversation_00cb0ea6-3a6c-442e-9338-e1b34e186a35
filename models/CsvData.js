const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CsvData = sequelize.define('CsvData', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  filename: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Original filename of the uploaded CSV'
  },
  upload_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the CSV was uploaded'
  },
  total_rows: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Total number of rows in the CSV file'
  },
  processed_rows: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Number of rows successfully processed'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: 'Processing status of the CSV file'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Error message if processing failed'
  }
}, {
  tableName: 'csv_uploads',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = CsvData;
