// Authentication middleware
const requireAuth = (req, res, next) => {
  if (req.session && req.session.authenticated) {
    return next();
  } else {
    return res.redirect('/megacare/hqprd/login');
  }
};

// Redirect authenticated users away from login page
const redirectIfAuthenticated = (req, res, next) => {
  if (req.session && req.session.authenticated) {
    return res.redirect('/megacare/hqprd/dashboard');
  } else {
    return next();
  }
};

// Login validation
const validateLogin = (username, password) => {
  // Hardcoded credentials as requested
  const validUsername = 'admin';
  const validPassword = 'megacare';
  
  return username === validUsername && password === validPassword;
};

module.exports = {
  requireAuth,
  redirectIfAuthenticated,
  validateLogin
};
