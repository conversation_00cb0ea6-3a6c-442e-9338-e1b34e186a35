const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

class TableManager {
  /**
   * Generate table name for a given month
   * @param {string} month - Month in YYYY-MM format
   * @returns {string} - Table name
   */
  static generateTableName(month) {
    const [year, monthNum] = month.split('-');
    return `csv_data_${year}_${monthNum}`;
  }

  /**
   * Create a dynamic table for storing CSV data for a specific month
   * @param {string} month - Month in YYYY-MM format
   * @param {Array} csvHeaders - Array of CSV column headers
   * @returns {Object} - Sequelize model for the created table
   */
  static async createMonthlyTable(month, csvHeaders = []) {
    const tableName = this.generateTableName(month);
    
    try {
      // Check if table already exists
      const tableExists = await sequelize.getQueryInterface().showAllTables()
        .then(tables => tables.includes(tableName));
      
      if (tableExists) {
        console.log(`Table ${tableName} already exists`);
        return this.getMonthlyTableModel(month, csvHeaders);
      }

      // Define base columns that every monthly table should have
      const baseColumns = {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        upload_meta_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          comment: 'Foreign key to csv_upload_meta table'
        },
        row_number: {
          type: DataTypes.INTEGER,
          allowNull: false,
          comment: 'Row number in the original CSV file'
        },
        processed_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
          comment: 'Timestamp when this record was processed'
        }
      };

      // Add dynamic columns based on CSV headers
      const dynamicColumns = {};
      if (csvHeaders && csvHeaders.length > 0) {
        csvHeaders.forEach(header => {
          // Clean header name for database column
          const cleanHeader = header.toLowerCase()
            .replace(/[^a-z0-9_]/g, '_')
            .replace(/_{2,}/g, '_')
            .replace(/^_|_$/g, '');
          
          dynamicColumns[cleanHeader] = {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: `Data from CSV column: ${header}`
          };
        });
      } else {
        // If no headers provided, create a generic JSON column
        dynamicColumns.csv_data = {
          type: DataTypes.JSON,
          allowNull: false,
          comment: 'JSON representation of the CSV row data'
        };
      }

      // Combine base and dynamic columns
      const allColumns = { ...baseColumns, ...dynamicColumns };

      // Create the model
      const MonthlyModel = sequelize.define(tableName, allColumns, {
        tableName: tableName,
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
        indexes: [
          {
            fields: ['upload_meta_id']
          },
          {
            fields: ['upload_meta_id', 'row_number']
          },
          {
            fields: ['processed_at']
          }
        ]
      });

      // Sync the table to create it in the database
      await MonthlyModel.sync({ force: false });
      
      console.log(`Created monthly table: ${tableName}`);
      return MonthlyModel;

    } catch (error) {
      console.error(`Error creating monthly table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get existing monthly table model
   * @param {string} month - Month in YYYY-MM format
   * @param {Array} csvHeaders - Array of CSV column headers
   * @returns {Object} - Sequelize model for the table
   */
  static getMonthlyTableModel(month, csvHeaders = []) {
    const tableName = this.generateTableName(month);
    
    // Check if model is already defined
    if (sequelize.models[tableName]) {
      return sequelize.models[tableName];
    }

    // Define base columns
    const baseColumns = {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      upload_meta_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key to csv_upload_meta table'
      },
      row_number: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Row number in the original CSV file'
      },
      processed_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'Timestamp when this record was processed'
      }
    };

    // Add dynamic columns based on CSV headers
    const dynamicColumns = {};
    if (csvHeaders && csvHeaders.length > 0) {
      csvHeaders.forEach(header => {
        const cleanHeader = header.toLowerCase()
          .replace(/[^a-z0-9_]/g, '_')
          .replace(/_{2,}/g, '_')
          .replace(/^_|_$/g, '');
        
        dynamicColumns[cleanHeader] = {
          type: DataTypes.TEXT,
          allowNull: true,
          comment: `Data from CSV column: ${header}`
        };
      });
    } else {
      dynamicColumns.csv_data = {
        type: DataTypes.JSON,
        allowNull: false,
        comment: 'JSON representation of the CSV row data'
      };
    }

    const allColumns = { ...baseColumns, ...dynamicColumns };

    return sequelize.define(tableName, allColumns, {
      tableName: tableName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    });
  }

  /**
   * Get list of available monthly tables
   * @returns {Array} - Array of table names
   */
  static async getAvailableMonthlyTables() {
    try {
      const tables = await sequelize.getQueryInterface().showAllTables();
      return tables.filter(table => table.startsWith('csv_data_'));
    } catch (error) {
      console.error('Error getting available monthly tables:', error);
      return [];
    }
  }

  /**
   * Drop a monthly table
   * @param {string} month - Month in YYYY-MM format
   * @returns {boolean} - Success status
   */
  static async dropMonthlyTable(month) {
    const tableName = this.generateTableName(month);
    
    try {
      await sequelize.getQueryInterface().dropTable(tableName);
      
      // Remove from sequelize models if exists
      if (sequelize.models[tableName]) {
        delete sequelize.models[tableName];
      }
      
      console.log(`Dropped monthly table: ${tableName}`);
      return true;
    } catch (error) {
      console.error(`Error dropping monthly table ${tableName}:`, error);
      return false;
    }
  }
}

module.exports = TableManager;
