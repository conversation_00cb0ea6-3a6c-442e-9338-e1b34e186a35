const { parentPort, workerData } = require('worker_threads');
const fs = require('fs');
const csv = require('csv-parser');

// Import TableManager
const TableManager = require('../utils/tableManager');

// Models are now handled by TableManager for dynamic monthly tables

// Retry function with exponential backoff
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }

      // Check if it's a connection error
      if (error.name === 'SequelizeConnectionError' ||
          error.name === 'ConnectionError' ||
          error.message.includes('ETIMEDOUT') ||
          error.message.includes('timeout')) {

        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`Connection error on attempt ${attempt}. Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error; // Non-connection errors should not be retried
      }
    }
  }
}

async function processCsvFile() {
  const { filePath, uploadMetaId, selectedMonth, tableName } = workerData;

  try {
    const records = [];
    let rowNumber = 0;
    let totalRows = 0;
    let csvHeaders = [];
    let errorCount = 0;
    const MAX_ERRORS = 10; // Maximum errors before stopping
    const BATCH_SIZE = 25; // Reduced batch size for stability

    // First pass: get headers and count total rows
    await new Promise((resolve, reject) => {
      let isFirstRow = true;
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('headers', (headers) => {
          csvHeaders = headers;
        })
        .on('data', (data) => {
          if (isFirstRow) {
            csvHeaders = Object.keys(data);
            isFirstRow = false;
          }
          totalRows++;
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`Processing CSV with ${totalRows} rows for month ${selectedMonth}`);
    console.log(`CSV Headers:`, csvHeaders);

    // Create monthly table with dynamic columns based on CSV headers
    const MonthlyModel = await TableManager.createMonthlyTable(selectedMonth, csvHeaders);

    parentPort.postMessage({
      type: 'progress',
      totalRows,
      processedRows: 0
    });

    // Reset for second pass
    rowNumber = 0;

    // Second pass: process and store data in monthly table
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', async (data) => {
          rowNumber++;

          // Prepare record for monthly table
          const recordData = {
            upload_meta_id: uploadMetaId,
            row_number: rowNumber,
            processed_at: new Date()
          };

          // Add CSV data as individual columns
          if (csvHeaders && csvHeaders.length > 0) {
            csvHeaders.forEach(header => {
              const cleanHeader = header.toLowerCase()
                .replace(/[^a-z0-9_]/g, '_')
                .replace(/_{2,}/g, '_')
                .replace(/^_|_$/g, '');
              recordData[cleanHeader] = data[header] || null;
            });
          } else {
            // Fallback to JSON storage
            recordData.csv_data = data;
          }

          records.push(recordData);

          // Batch insert every 25 records for better performance and stability
          if (records.length >= BATCH_SIZE) {
            try {
              await retryWithBackoff(async () => {
                await MonthlyModel.bulkCreate(records, {
                  ignoreDuplicates: true,
                  validate: false,
                  logging: false
                });
              });
              records.length = 0; // Clear the array

              parentPort.postMessage({
                type: 'progress',
                totalRows,
                processedRows: rowNumber
              });
            } catch (error) {
              console.error('Error inserting batch:', error);
              errorCount++;

              // Stop processing if too many errors
              if (errorCount >= MAX_ERRORS) {
                parentPort.postMessage({
                  type: 'error',
                  error: `Too many errors (${errorCount}). Stopping processing.`
                });
                return;
              }

              // Try individual inserts if batch fails (only for connection errors)
              if (error.name === 'SequelizeConnectionError' || error.name === 'ConnectionError') {
                console.log('Connection error, skipping batch and continuing...');
                records.length = 0; // Clear the array
              } else {
                // For other errors, try individual inserts
                for (const record of records) {
                  try {
                    await MonthlyModel.create(record);
                  } catch (individualError) {
                    console.error('Error inserting individual record:', individualError);
                  }
                }
                records.length = 0; // Clear the array
              }

              parentPort.postMessage({
                type: 'progress',
                totalRows,
                processedRows: rowNumber
              });
            }
          }
        })
        .on('end', async () => {
          // Insert remaining records
          if (records.length > 0) {
            try {
              await retryWithBackoff(async () => {
                await MonthlyModel.bulkCreate(records, {
                  ignoreDuplicates: true,
                  validate: false,
                  logging: false
                });
              });
            } catch (error) {
              console.error('Error inserting final batch:', error);

              // For connection errors, just skip the final batch
              if (error.name === 'SequelizeConnectionError' || error.name === 'ConnectionError') {
                console.log('Connection error in final batch, skipping...');
              } else {
                // For other errors, try individual inserts
                for (const record of records) {
                  try {
                    await MonthlyModel.create(record);
                  } catch (individualError) {
                    console.error('Error inserting individual record:', individualError);
                  }
                }
              }
            }
          }
          resolve();
        })
        .on('error', reject);
    });

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    parentPort.postMessage({
      type: 'complete',
      totalRows,
      totalProcessed: rowNumber,
      tableName: tableName,
      selectedMonth: selectedMonth
    });

  } catch (error) {
    console.error('CSV processing error:', error);

    // Clean up the uploaded file if it still exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    parentPort.postMessage({
      type: 'error',
      error: error.message
    });
  }
}

// Start processing
processCsvFile();
