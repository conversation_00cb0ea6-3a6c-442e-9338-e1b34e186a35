const { parentPort, workerData } = require('worker_threads');
const fs = require('fs');
const csv = require('csv-parser');
const { Sequelize, DataTypes } = require('sequelize');

// Initialize database connection in worker
const sequelize = new Sequelize(
  process.env.DB_NAME || 'megacare_hqprd',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || '',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: false
  }
);

// Define models in worker context
const CsvData = sequelize.define('CsvData', {
  id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
  filename: { type: DataTypes.STRING, allowNull: false },
  upload_date: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW },
  total_rows: { type: DataTypes.INTEGER, allowNull: true },
  processed_rows: { type: DataTypes.INTEGER, allowNull: true, defaultValue: 0 },
  status: { type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'), allowNull: false, defaultValue: 'pending' },
  error_message: { type: DataTypes.TEXT, allowNull: true }
}, {
  tableName: 'csv_uploads',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

const CsvRecord = sequelize.define('CsvRecord', {
  id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
  csv_data_id: { type: DataTypes.INTEGER, allowNull: false },
  row_number: { type: DataTypes.INTEGER, allowNull: false },
  data: { type: DataTypes.JSON, allowNull: false },
  processed_at: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW }
}, {
  tableName: 'csv_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

async function processCsvFile() {
  const { filePath, csvDataId, originalFilename } = workerData;
  
  try {
    const records = [];
    let rowNumber = 0;
    let totalRows = 0;

    // First pass: count total rows
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', () => totalRows++)
        .on('end', resolve)
        .on('error', reject);
    });

    parentPort.postMessage({
      type: 'progress',
      totalRows,
      processedRows: 0
    });

    // Second pass: process and store data
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', async (data) => {
          rowNumber++;
          
          // Store each row as a record
          records.push({
            csv_data_id: csvDataId,
            row_number: rowNumber,
            data: data,
            processed_at: new Date()
          });

          // Batch insert every 100 records for performance
          if (records.length >= 100) {
            try {
              await CsvRecord.bulkCreate(records);
              records.length = 0; // Clear the array
              
              parentPort.postMessage({
                type: 'progress',
                totalRows,
                processedRows: rowNumber
              });
            } catch (error) {
              console.error('Error inserting batch:', error);
            }
          }
        })
        .on('end', async () => {
          // Insert remaining records
          if (records.length > 0) {
            try {
              await CsvRecord.bulkCreate(records);
            } catch (error) {
              console.error('Error inserting final batch:', error);
            }
          }
          resolve();
        })
        .on('error', reject);
    });

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    parentPort.postMessage({
      type: 'complete',
      totalRows,
      totalProcessed: rowNumber
    });

  } catch (error) {
    console.error('CSV processing error:', error);
    parentPort.postMessage({
      type: 'error',
      error: error.message
    });
  }
}

// Start processing
processCsvFile();
