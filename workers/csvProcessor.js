const { parentPort, workerData } = require('worker_threads');
const fs = require('fs');
const csv = require('csv-parser');

// Import TableManager
const TableManager = require('../utils/tableManager');

// Models are now handled by TableManager for dynamic monthly tables

async function processCsvFile() {
  const { filePath, uploadMetaId, selectedMonth, tableName } = workerData;

  try {
    const records = [];
    let rowNumber = 0;
    let totalRows = 0;
    let csvHeaders = [];

    // First pass: get headers and count total rows
    await new Promise((resolve, reject) => {
      let isFirstRow = true;
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('headers', (headers) => {
          csvHeaders = headers;
        })
        .on('data', (data) => {
          if (isFirstRow) {
            csvHeaders = Object.keys(data);
            isFirstRow = false;
          }
          totalRows++;
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`Processing CSV with ${totalRows} rows for month ${selectedMonth}`);
    console.log(`CSV Headers:`, csvHeaders);

    // Create monthly table with dynamic columns based on CSV headers
    const MonthlyModel = await TableManager.createMonthlyTable(selectedMonth, csvHeaders);

    parentPort.postMessage({
      type: 'progress',
      totalRows,
      processedRows: 0
    });

    // Reset for second pass
    rowNumber = 0;

    // Second pass: process and store data in monthly table
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', async (data) => {
          rowNumber++;

          // Prepare record for monthly table
          const recordData = {
            upload_meta_id: uploadMetaId,
            row_number: rowNumber,
            processed_at: new Date()
          };

          // Add CSV data as individual columns
          if (csvHeaders && csvHeaders.length > 0) {
            csvHeaders.forEach(header => {
              const cleanHeader = header.toLowerCase()
                .replace(/[^a-z0-9_]/g, '_')
                .replace(/_{2,}/g, '_')
                .replace(/^_|_$/g, '');
              recordData[cleanHeader] = data[header] || null;
            });
          } else {
            // Fallback to JSON storage
            recordData.csv_data = data;
          }

          records.push(recordData);

          // Batch insert every 50 records for better performance and stability
          if (records.length >= 50) {
            try {
              await MonthlyModel.bulkCreate(records, {
                ignoreDuplicates: true,
                validate: false,
                logging: false
              });
              records.length = 0; // Clear the array

              parentPort.postMessage({
                type: 'progress',
                totalRows,
                processedRows: rowNumber
              });
            } catch (error) {
              console.error('Error inserting batch:', error);
              // Try individual inserts if batch fails
              for (const record of records) {
                try {
                  await MonthlyModel.create(record);
                } catch (individualError) {
                  console.error('Error inserting individual record:', individualError);
                }
              }
              records.length = 0; // Clear the array even if some failed

              parentPort.postMessage({
                type: 'progress',
                totalRows,
                processedRows: rowNumber
              });
            }
          }
        })
        .on('end', async () => {
          // Insert remaining records
          if (records.length > 0) {
            try {
              await MonthlyModel.bulkCreate(records, {
                ignoreDuplicates: true,
                validate: false,
                logging: false
              });
            } catch (error) {
              console.error('Error inserting final batch:', error);
              // Try individual inserts if batch fails
              for (const record of records) {
                try {
                  await MonthlyModel.create(record);
                } catch (individualError) {
                  console.error('Error inserting individual record:', individualError);
                }
              }
            }
          }
          resolve();
        })
        .on('error', reject);
    });

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    parentPort.postMessage({
      type: 'complete',
      totalRows,
      totalProcessed: rowNumber,
      tableName: tableName,
      selectedMonth: selectedMonth
    });

  } catch (error) {
    console.error('CSV processing error:', error);

    // Clean up the uploaded file if it still exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    parentPort.postMessage({
      type: 'error',
      error: error.message
    });
  }
}

// Start processing
processCsvFile();
