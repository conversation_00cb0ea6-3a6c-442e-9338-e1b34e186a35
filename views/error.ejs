<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="<%= baseUrl %>/img/apple-icon.png">
  <link rel="icon" type="image/png" href="<%= baseUrl %>/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title><%= title %></title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="<%= baseUrl %>/css/bootstrap.min.css" rel="stylesheet" />
  <link href="<%= baseUrl %>/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  
  <style>
    .error-page {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .error-card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 500px;
      text-align: center;
    }
    .error-icon {
      font-size: 64px;
      color: #dc3545;
      margin-bottom: 20px;
    }
    .error-title {
      color: #333;
      font-weight: 300;
      margin-bottom: 15px;
    }
    .error-message {
      color: #666;
      margin-bottom: 30px;
    }
    .btn-home {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 30px;
      font-weight: 500;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
    }
    .btn-home:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      color: white;
      text-decoration: none;
    }
  </style>
</head>

<body class="error-page">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="error-card">
          <div class="error-icon">
            <i class="fa fa-exclamation-triangle"></i>
          </div>
          
          <h2 class="error-title"><%= title %></h2>
          <p class="error-message"><%= message %></p>
          
          <% if (error && error.stack && process.env.NODE_ENV === 'development') { %>
            <div class="alert alert-danger text-left">
              <h6>Debug Information:</h6>
              <pre><%= error.stack %></pre>
            </div>
          <% } %>
          
          <a href="<%= baseUrl %>/dashboard" class="btn-home">
            <i class="fa fa-home"></i> Go to Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="<%= baseUrl %>/js/core/jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/core/popper.min.js"></script>
  <script src="<%= baseUrl %>/js/core/bootstrap.min.js"></script>
</body>
</html>
