<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="<%= baseUrl %>/img/apple-icon.png">
  <link rel="icon" type="image/png" href="<%= baseUrl %>/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title><%= title %></title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="<%= baseUrl %>/css/bootstrap.min.css" rel="stylesheet" />
  <link href="<%= baseUrl %>/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <link href="<%= baseUrl %>/demo/demo.css" rel="stylesheet" />
</head>

<body class="">
  <div class="wrapper">
    <!-- Sidebar -->
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="<%= baseUrl %>/dashboard" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li class="active">
            <a href="<%= baseUrl %>/dashboard">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li>
            <a href="<%= baseUrl %>/upload">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload CSV</p>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Panel -->
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Dashboard</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item btn-rotate dropdown">
                <a class="nav-link dropdown-toggle" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="nc-icon nc-single-02"></i>
                  <p><%= user.username %></p>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                  <a class="dropdown-item" href="<%= baseUrl %>/logout">Logout</a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <!-- Content -->
      <div class="content">
        <div class="row">
          <!-- Statistics Cards -->
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-cloud-upload-94 text-warning"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Total Uploads</p>
                      <p class="card-title"><%= stats.totalUploads %></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-check-2 text-success"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Completed</p>
                      <p class="card-title"><%= stats.completedUploads %></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-time-alarm text-info"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Processing</p>
                      <p class="card-title"><%= stats.processingUploads %></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-watch-time text-danger"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">Pending</p>
                      <p class="card-title"><%= stats.pendingUploads %></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Uploads Table -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Recent Uploads</h4>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table">
                    <thead class="text-primary">
                      <tr>
                        <th>Filename</th>
                        <th>Upload Date</th>
                        <th>Status</th>
                        <th>Total Rows</th>
                        <th>Processed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% if (recentUploads.length > 0) { %>
                        <% recentUploads.forEach(upload => { %>
                          <tr>
                            <td><%= upload.filename %></td>
                            <td><%= new Date(upload.upload_date).toLocaleDateString() %></td>
                            <td>
                              <span class="badge badge-<%= upload.status === 'completed' ? 'success' : upload.status === 'processing' ? 'info' : upload.status === 'failed' ? 'danger' : 'warning' %>">
                                <%= upload.status %>
                              </span>
                            </td>
                            <td><%= upload.total_rows || '-' %></td>
                            <td><%= upload.processed_rows || '-' %></td>
                          </tr>
                        <% }) %>
                      <% } else { %>
                        <tr>
                          <td colspan="5" class="text-center">No uploads yet</td>
                        </tr>
                      <% } %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <div class="credits ml-auto">
              <span class="copyright">
                © <%= new Date().getFullYear() %>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="<%= baseUrl %>/js/core/jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/core/popper.min.js"></script>
  <script src="<%= baseUrl %>/js/core/bootstrap.min.js"></script>
  <script src="<%= baseUrl %>/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/paper-dashboard.min.js?v=2.0.1" type="text/javascript"></script>
</body>
</html>
