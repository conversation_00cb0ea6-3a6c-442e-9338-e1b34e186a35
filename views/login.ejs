<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="<%= baseUrl %>/img/apple-icon.png">
  <link rel="icon" type="image/png" href="<%= baseUrl %>/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title><%= title %></title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="<%= baseUrl %>/css/bootstrap.min.css" rel="stylesheet" />
  <link href="<%= baseUrl %>/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  
  <style>
    .login-page {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 400px;
    }
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    .login-header h2 {
      color: #333;
      font-weight: 300;
      margin-bottom: 10px;
    }
    .login-header p {
      color: #666;
      font-size: 14px;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .form-control {
      border: 1px solid #e3e3e3;
      border-radius: 8px;
      padding: 12px 15px;
      font-size: 14px;
    }
    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-login {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 30px;
      font-weight: 500;
      width: 100%;
      color: white;
      transition: all 0.3s ease;
    }
    .btn-login:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      color: white;
    }
    .alert {
      border-radius: 8px;
      margin-bottom: 20px;
    }
  </style>
</head>

<body class="login-page">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <div class="login-card">
          <div class="login-header">
            <h2>Megacare HQ PRD</h2>
            <p>Please sign in to your account</p>
          </div>

          <% if (error) { %>
            <div class="alert alert-danger" role="alert">
              <i class="fa fa-exclamation-triangle"></i> <%= error %>
            </div>
          <% } %>

          <form action="<%= baseUrl %>/login" method="POST">
            <div class="form-group">
              <label for="username">Username</label>
              <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" required>
            </div>
            
            <div class="form-group">
              <label for="password">Password</label>
              <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
            </div>
            
            <button type="submit" class="btn btn-login">
              <i class="fa fa-sign-in"></i> Sign In
            </button>
          </form>
          
          <div class="text-center mt-3">
            <small class="text-muted">
              Default credentials: admin / megacare
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="<%= baseUrl %>/js/core/jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/core/popper.min.js"></script>
  <script src="<%= baseUrl %>/js/core/bootstrap.min.js"></script>
</body>
</html>
