<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="<%= baseUrl %>/img/apple-icon.png">
  <link rel="icon" type="image/png" href="<%= baseUrl %>/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title><%= title %></title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  
  <!-- Fonts and icons -->
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- CSS Files -->
  <link href="<%= baseUrl %>/css/bootstrap.min.css" rel="stylesheet" />
  <link href="<%= baseUrl %>/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <link href="<%= baseUrl %>/demo/demo.css" rel="stylesheet" />
  
  <style>
    .upload-area {
      border: 2px dashed #ddd;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .upload-area:hover {
      border-color: #007bff;
      background-color: #f8f9fa;
    }
    .upload-area.dragover {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
    .upload-icon {
      font-size: 48px;
      color: #ddd;
      margin-bottom: 20px;
    }
    .file-input {
      display: none;
    }
  </style>
</head>

<body class="">
  <div class="wrapper">
    <!-- Sidebar -->
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="<%= baseUrl %>/dashboard" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li>
            <a href="<%= baseUrl %>/dashboard">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li class="active">
            <a href="<%= baseUrl %>/upload">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload CSV</p>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Panel -->
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Upload CSV</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item btn-rotate dropdown">
                <a class="nav-link dropdown-toggle" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="nc-icon nc-single-02"></i>
                  <p><%= user.username %></p>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                  <a class="dropdown-item" href="<%= baseUrl %>/logout">Logout</a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <!-- Content -->
      <div class="content">
        <div class="row">
          <div class="col-md-8 offset-md-2">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload CSV File</h4>
                <p class="card-category">Upload your CSV file for processing</p>
              </div>
              <div class="card-body">
                <% if (success) { %>
                  <div class="alert alert-success" role="alert">
                    <i class="fa fa-check"></i> <%= success %>
                  </div>
                <% } %>

                <% if (error) { %>
                  <div class="alert alert-danger" role="alert">
                    <i class="fa fa-exclamation-triangle"></i> <%= error %>
                  </div>
                <% } %>

                <form action="<%= baseUrl %>/upload" method="POST" enctype="multipart/form-data" id="uploadForm">
                  <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                      <i class="nc-icon nc-cloud-upload-94"></i>
                    </div>
                    <h5>Drag & Drop your CSV file here</h5>
                    <p class="text-muted">or click to browse</p>
                    <input type="file" name="csvFile" id="csvFile" class="file-input" accept=".csv" required>
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFile').click()">
                      Choose File
                    </button>
                  </div>
                  
                  <div class="mt-3" id="fileInfo" style="display: none;">
                    <div class="alert alert-info">
                      <strong>Selected file:</strong> <span id="fileName"></span>
                    </div>
                  </div>

                  <div class="text-center mt-4">
                    <button type="submit" class="btn btn-success" id="uploadBtn" disabled>
                      <i class="fa fa-upload"></i> Upload & Process
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Status -->
        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload Status</h4>
                <button class="btn btn-sm btn-info float-right" onclick="refreshStatus()">
                  <i class="fa fa-refresh"></i> Refresh
                </button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table" id="statusTable">
                    <thead class="text-primary">
                      <tr>
                        <th>Filename</th>
                        <th>Upload Date</th>
                        <th>Status</th>
                        <th>Progress</th>
                      </tr>
                    </thead>
                    <tbody id="statusTableBody">
                      <tr>
                        <td colspan="4" class="text-center">Loading...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <div class="credits ml-auto">
              <span class="copyright">
                © <%= new Date().getFullYear() %>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="<%= baseUrl %>/js/core/jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/core/popper.min.js"></script>
  <script src="<%= baseUrl %>/js/core/bootstrap.min.js"></script>
  <script src="<%= baseUrl %>/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="<%= baseUrl %>/js/paper-dashboard.min.js?v=2.0.1" type="text/javascript"></script>

  <script>
    // File upload handling
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('csvFile');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const uploadBtn = document.getElementById('uploadBtn');

    // Click to upload
    uploadArea.addEventListener('click', () => {
      fileInput.click();
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
      }
    });

    // File selection
    fileInput.addEventListener('change', handleFileSelect);

    function handleFileSelect() {
      const file = fileInput.files[0];
      if (file) {
        fileName.textContent = file.name;
        fileInfo.style.display = 'block';
        uploadBtn.disabled = false;
      }
    }

    // Refresh status
    function refreshStatus() {
      fetch('<%= baseUrl %>/api/upload-status')
        .then(response => response.json())
        .then(data => {
          const tbody = document.getElementById('statusTableBody');
          if (data.success && data.uploads.length > 0) {
            tbody.innerHTML = data.uploads.map(upload => `
              <tr>
                <td>${upload.filename}</td>
                <td>${new Date(upload.upload_date).toLocaleDateString()}</td>
                <td>
                  <span class="badge badge-${getStatusColor(upload.status)}">
                    ${upload.status}
                  </span>
                </td>
                <td>
                  ${upload.total_rows ?
                    `${upload.processed_rows || 0} / ${upload.total_rows}` :
                    '-'
                  }
                </td>
              </tr>
            `).join('');
          } else {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">No uploads found</td></tr>';
          }
        })
        .catch(error => {
          console.error('Error fetching status:', error);
        });
    }

    function getStatusColor(status) {
      switch(status) {
        case 'completed': return 'success';
        case 'processing': return 'info';
        case 'failed': return 'danger';
        default: return 'warning';
      }
    }

    // Load status on page load
    refreshStatus();

    // Auto-refresh every 5 seconds
    setInterval(refreshStatus, 5000);
  </script>
