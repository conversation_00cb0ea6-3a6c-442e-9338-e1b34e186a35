{"name": "megacare-hqprd", "version": "1.0.0", "description": "Megacare HQ PRD Application with CSV Upload and Processing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["megacare", "csv", "upload", "dashboard"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "express-session": "^1.17.3", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "express-flash": "^0.0.2"}, "devDependencies": {"nodemon": "^3.0.2"}}