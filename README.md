# Megacare HQ PRD - CSV Upload Application

A Node.js web application for uploading and processing CSV files with a beautiful Paper Dashboard UI using plain HTML, CSS, and JavaScript.

## Features

- **Authentication**: Login with admin/megacare credentials
- **Dashboard**: Overview of upload statistics and recent uploads
- **CSV Upload**: Drag & drop or browse to upload CSV files
- **Background Processing**: Worker threads process CSV files asynchronously
- **Real-time Status**: Live updates on upload processing status
- **Paper Dashboard UI**: Beautiful, responsive interface with plain HTML/CSS/JavaScript
- **No Template Engine**: Uses static HTML files with dynamic JavaScript for better performance

## Prerequisites

- Node.js (v14 or higher)
- MySQL database
- npm or yarn package manager

## Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd /path/to/HQ_PRD
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup MySQL Database**
   - Create a MySQL database named `megacare_hqprd`
   - Update database credentials in `.env` file

4. **Configure Environment Variables**
   Edit the `.env` file with your database credentials:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=megacare_hqprd
   DB_USER=your_mysql_username
   DB_PASSWORD=your_mysql_password
   ```

5. **Start the Application**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the Application**
   Open your browser and navigate to:
   ```
   http://localhost:3000/megacare/hqprd
   ```

## Default Login Credentials

- **Username**: admin
- **Password**: megacare

## Project Structure

```
├── config/
│   └── database.js          # Database configuration
├── controllers/
│   ├── authController.js    # Authentication logic
│   ├── dashboardController.js # Dashboard logic
│   └── uploadController.js  # File upload logic
├── middleware/
│   └── auth.js             # Authentication middleware
├── models/
│   ├── CsvData.js          # CSV upload model
│   ├── CsvRecord.js        # CSV record model
│   └── index.js            # Model initialization
├── public/                 # Static assets (CSS, JS, images)
├── routes/
│   ├── auth.js             # Authentication routes
│   ├── dashboard.js        # Dashboard routes
│   └── index.js            # Root routes
├── uploads/                # Temporary file storage
├── public/
│   ├── dashboard.html      # Dashboard page
│   ├── login.html          # Login page
│   ├── upload.html         # Upload page
│   ├── css/                # CSS files (Paper Dashboard)
│   ├── js/                 # JavaScript files
│   └── img/                # Images and icons
├── workers/
│   └── csvProcessor.js     # CSV processing worker
├── .env                    # Environment variables
├── package.json            # Dependencies and scripts
├── server.js               # Main application file
└── sample_data.csv         # Sample CSV for testing
```

## API Endpoints

### Pages
- `GET /megacare/hqprd/login.html` - Login page
- `GET /megacare/hqprd/dashboard.html` - Dashboard page
- `GET /megacare/hqprd/upload.html` - Upload page

### API Routes
- `POST /megacare/hqprd/login` - Login authentication (JSON)
- `POST /megacare/hqprd/upload` - File upload endpoint
- `GET /megacare/hqprd/api/dashboard-data` - Dashboard statistics API
- `GET /megacare/hqprd/api/upload-status` - Upload status API
- `GET /megacare/hqprd/logout` - Logout

## Database Schema

### csv_uploads table
- `id` - Primary key
- `filename` - Original filename
- `upload_date` - Upload timestamp
- `total_rows` - Total rows in CSV
- `processed_rows` - Processed rows count
- `status` - Processing status (pending, processing, completed, failed)
- `error_message` - Error details if failed

### csv_records table
- `id` - Primary key
- `csv_data_id` - Foreign key to csv_uploads
- `row_number` - Row number in original CSV
- `data` - JSON data of the CSV row
- `processed_at` - Processing timestamp

## Usage

1. **Login**: Use admin/megacare to access the application
2. **Dashboard**: View upload statistics and recent uploads
3. **Upload CSV**: Navigate to Upload tab and select/drag CSV file
4. **Monitor Progress**: Watch real-time processing status
5. **View Data**: Processed data is stored in the database

## Development

- **Development Mode**: `npm run dev` (uses nodemon for auto-restart)
- **Production Mode**: `npm start`
- **Environment**: Set `NODE_ENV=production` for production

## Troubleshooting

1. **Database Connection Issues**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database `megacare_hqprd` exists

2. **File Upload Issues**
   - Check file permissions on `uploads/` directory
   - Verify file size is under 10MB
   - Ensure file is in CSV format

3. **Worker Thread Issues**
   - Check Node.js version (v14+ required)
   - Verify database connection in worker

## License

This project is proprietary to Megacare.
