# Megacare HQ PRD - CSV Upload Application

A Node.js web application for uploading and processing CSV files with a beautiful Paper Dashboard UI using plain HTML, CSS, and JavaScript.

## Features

- **Authentication**: Login with admin/megacare credentials
- **Dashboard**: Overview of upload statistics and recent uploads
- **CSV Upload**: Upload CSV files with month selection and custom naming
- **Monthly Data Partitioning**: Automatically creates monthly tables based on selected month
- **Background Processing**: Worker threads process CSV files asynchronously
- **Real-time Status**: Live updates on upload processing status
- **Dynamic Schema**: Tables adapt to CSV headers automatically
- **Paper Dashboard UI**: Beautiful, responsive interface with plain HTML/CSS/JavaScript
- **No Template Engine**: Uses static HTML files with dynamic JavaScript for better performance

## Prerequisites

- Node.js (v14 or higher)
- MySQL database
- npm or yarn package manager

## Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd /path/to/HQ_PRD
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Database Configuration**
   The application connects to a remote MySQL database with these credentials:
   - **Host**: alembicdigilabs.in
   - **Database**: megacare_hq_prd
   - **Username**: megacare_user
   - **Password**: megacare@2024

   No additional database setup is required as the application will automatically create necessary tables.

4. **Start the Application**
   ```bash
   npm start
   ```

5. **Access the Application**
   Open your browser and navigate to:
   - **Login**: http://localhost:3000/megacare/hqprd/login
   - **Dashboard**: http://localhost:3000/megacare/hqprd/dashboard (after login)
   - **Upload**: http://localhost:3000/megacare/hqprd/upload (after login)

## Default Login Credentials

- **Username**: admin
- **Password**: megacare

## How to Start the Project and Workers

### Starting the Main Server

```bash
npm start
```

The server will start on port 3000 and automatically handle:
- Database connection and table creation
- Session management
- Static file serving
- API endpoints

### Worker Management

**Important**: Workers are automatically managed by the main application. No separate worker process needs to be started.

**How Workers Function**:
- Workers are created automatically when CSV files are uploaded
- Each upload spawns a dedicated worker thread
- Workers process CSV files in the background without blocking the main application
- Progress is reported in real-time to the database and UI
- Workers automatically clean up after processing completion

## CSV Upload with Monthly Tables

### New Upload Process

1. **Select Month**: Choose the month for data categorization (YYYY-MM format)
2. **Enter Upload Name**: Provide a unique name for this upload batch
3. **Choose CSV File**: Select your CSV file
4. **Upload**: The system will:
   - Create a monthly table (e.g., `csv_data_2024_01` for January 2024)
   - Dynamically add columns based on CSV headers
   - Store upload metadata in `csv_upload_meta` table
   - Process data in background using worker threads

### Monthly Table Structure

When you upload a CSV for a specific month (e.g., "2024-01"), the system:
- Creates a table named `csv_data_2024_01`
- Adds columns for each CSV header (sanitized for database compatibility)
- Includes metadata columns: `upload_meta_id`, `row_number`, `processed_at`
- Stores all upload information in the `csv_upload_meta` table

## Project Structure

```
├── config/
│   └── database.js          # Database configuration
├── controllers/
│   ├── authController.js    # Authentication logic
│   ├── dashboardController.js # Dashboard logic
│   └── uploadController.js  # File upload logic
├── middleware/
│   └── auth.js             # Authentication middleware
├── models/
│   ├── CsvData.js          # Legacy CSV upload model
│   ├── CsvUploadMeta.js    # New upload metadata model
│   ├── CsvRecord.js        # Legacy CSV record model (deprecated)
│   └── index.js            # Model initialization
├── utils/
│   └── tableManager.js     # Monthly table management utility
├── public/                 # Static assets (CSS, JS, images)
├── routes/
│   ├── auth.js             # Authentication routes
│   ├── dashboard.js        # Dashboard routes
│   └── index.js            # Root routes
├── uploads/                # Temporary file storage
├── public/
│   ├── dashboard.html      # Dashboard page
│   ├── login.html          # Login page
│   ├── upload.html         # Upload page
│   ├── css/                # CSS files (Paper Dashboard)
│   ├── js/                 # JavaScript files
│   └── img/                # Images and icons
├── workers/
│   └── csvProcessor.js     # CSV processing worker
├── .env                    # Environment variables
├── package.json            # Dependencies and scripts
├── server.js               # Main application file
└── sample_data.csv         # Sample CSV for testing
```

## API Endpoints

### Pages
- `GET /megacare/hqprd/login.html` - Login page
- `GET /megacare/hqprd/dashboard.html` - Dashboard page
- `GET /megacare/hqprd/upload.html` - Upload page

### API Routes
- `POST /megacare/hqprd/login` - Login authentication (JSON)
- `POST /megacare/hqprd/upload` - File upload endpoint
- `GET /megacare/hqprd/api/dashboard-data` - Dashboard statistics API
- `GET /megacare/hqprd/api/upload-status` - Upload status API
- `GET /megacare/hqprd/logout` - Logout

## Database Schema

### New Schema (Current)

#### csv_upload_meta table
- `id` - Primary key
- `upload_name` - Unique identifier for each upload batch
- `original_filename` - Original CSV filename
- `selected_month` - Month selection (YYYY-MM format)
- `table_name` - Generated monthly table name
- `upload_date` - Upload timestamp
- `total_rows` - Total rows in CSV
- `processed_rows` - Processed rows count
- `status` - Processing status (pending, processing, completed, failed)
- `uploaded_by` - User who uploaded the file
- `error_message` - Error details if failed

#### Monthly Tables (e.g., csv_data_2024_01)
- `id` - Primary key
- `upload_meta_id` - Foreign key to csv_upload_meta
- `row_number` - Row number in original CSV
- `processed_at` - Processing timestamp
- Dynamic columns based on CSV headers (sanitized for database compatibility)

### Legacy Schema (Deprecated)

#### csv_data table (being phased out)
- `id` - Primary key
- `filename` - Original filename
- `upload_date` - Upload timestamp
- `selected_month` - Month selection
- `table_name` - Generated table name
- `total_rows` - Total rows in CSV
- `processed_rows` - Processed rows count
- `status` - Processing status

#### csv_records table (replaced by monthly tables)
- `id` - Primary key
- `csv_data_id` - Foreign key to csv_data
- `row_number` - Row number in original CSV
- `data` - JSON data of the CSV row
- `processed_at` - Processing timestamp

## Usage

1. **Login**: Use admin/megacare to access the application
2. **Dashboard**: View upload statistics and recent uploads
3. **Upload CSV**:
   - Navigate to Upload page
   - Select the month for data categorization (YYYY-MM format)
   - Enter a unique upload name for identification
   - Choose your CSV file
   - Click "Upload CSV"
4. **Monitor Progress**: Watch real-time processing status with detailed progress bars
5. **View Data**: Processed data is stored in monthly tables based on selected month

## Development

- **Development Mode**: `npm run dev` (uses nodemon for auto-restart)
- **Production Mode**: `npm start`
- **Environment**: Set `NODE_ENV=production` for production

## Troubleshooting

1. **Database Connection Issues**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database `megacare_hqprd` exists

2. **File Upload Issues**
   - Check file permissions on `uploads/` directory
   - Verify file size is under 10MB
   - Ensure file is in CSV format

3. **Worker Thread Issues**
   - Check Node.js version (v14+ required)
   - Verify database connection in worker

## License

This project is proprietary to Megacare.
